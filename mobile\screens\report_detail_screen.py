import logging
import threading
from typing import Literal, Optional, Dict, Any, List, Tuple
from datetime import datetime
from kivy.clock import Clock
from kivy.metrics import dp
from kivymd.app import MDApp
from kivymd.uix.boxlayout import MDBoxLayout
from kivymd.uix.card import MD<PERSON>ard
from kivymd.uix.label import MDLabel
from kivymd.uix.button import MDButton, MDButtonText
from kivymd.uix.progressindicator import MDLinearProgressIndicator
from kivymd.uix.divider import MDDivider
from kivymd.uix.scrollview import MDScrollView
from kivymd.uix.snackbar import MDSnackbar, MDSnackbarText

from screens.base_screen import BaseScreen
from utils.cloud_api import get_cloud_api
from theme import AppTheme

logger = logging.getLogger(__name__)

class ReportDetailScreen(BaseScreen):
    """报告详情页面 - 重构版，使用BaseScreen基类规范"""

    def __init__(self, **kwargs):
        # 设置页面基本属性
        self.page_title = "报告详情"
        self.show_back_button = True
        self.show_action_button = True
        self.action_button_icon = "refresh"
        super().__init__(**kwargs)
        
        self.report_data: Optional[Dict[str, Any]] = None
        # 使用全局AppTheme实例，而不是创建新实例
        from theme import AppTheme as AppThemeInstance
        self.theme = AppThemeInstance  # 统一主题访问
        self._ui_initialized = False  # 添加初始化标志

    def on_pre_enter(self, *args):
        """页面进入前的准备工作"""
        super().on_pre_enter(*args)
        # 调用init_ui进行UI初始化
        Clock.schedule_once(self.init_ui, 0.1)

    def init_ui(self, dt=0):
        """初始化UI界面"""
        if self._ui_initialized:
            logger.info("报告详情页面UI已初始化，跳过重复初始化")
            return
            
        try:
            # 调用父类初始化
            super().init_ui(dt)
            
            # 设置页面内容
            self.do_content_setup()
            
            self._ui_initialized = True
            logger.info("报告详情页面UI初始化完成")
        except Exception as e:
            logger.error(f"报告详情页面UI初始化失败: {e}")

    def do_content_setup(self):
        """设置页面内容 - 按照BaseScreen规范实现"""
        try:
            content_container = self.ids.get('content_container')
            if not content_container:
                logger.error("内容容器不存在，无法设置页面内容")
                return
                
            content_container.clear_widgets()
            
            # 加载报告数据
            self.load_report_data()
            
            # 创建报告内容
            self.create_report_content()
            
        except Exception as e:
            logger.error(f"设置页面内容时出错: {e}")

    def on_enter(self, *args):
        """页面进入时的处理"""
        super().on_enter(*args)
        # 如果UI未初始化，则进行初始化
        if not self._ui_initialized:
            Clock.schedule_once(self.init_ui, 0.1)

    def load_report_data(self):
        """加载真实报告数据"""
        app = MDApp.get_running_app()
        self.report_data = getattr(app, 'current_response', None) or getattr(self.manager, 'current_report_data', None)
        logger.info(f"初始报告数据: {self.report_data}")
        
        # 增强数据验证和错误处理
        if not self.report_data:
            logger.warning("未找到报告数据，尝试从其他来源获取")
            # 尝试从manager获取数据
            if hasattr(self.manager, 'shared_data'):
                self.report_data = self.manager.shared_data.get('current_report_data')
                logger.info(f"从manager.shared_data获取到数据: {self.report_data}")
            
            if not self.report_data:
                self.show_error("未找到报告数据")
                self.show_empty_state()
                return

        # 修正report_type的获取逻辑
        self.report_type = self.report_data.get('item_type', 'unknown') if self.report_data else 'unknown'
        # 如果item_type是unknown，尝试从type字段获取
        if self.report_type == 'unknown':
            type_field = self.report_data.get('type', '') if self.report_data else ''
            if 'assessment' in type_field:
                self.report_type = 'assessment'
            elif 'questionnaire' in type_field:
                self.report_type = 'questionnaire'
        
        # 如果仍然无法确定report_type，尝试从其他字段推断
        if self.report_type == 'unknown':
            if self.report_data.get('assessment_id'):
                self.report_type = 'assessment'
            elif self.report_data.get('questionnaire_id'):
                self.report_type = 'questionnaire'
                
        self.report_id = self.report_data.get('id') if self.report_data else None
        logger.info(f"加载报告数据: type={self.report_type}, id={self.report_id}")

        # 先显示基础数据，即使没有额外的维度数据
        Clock.schedule_once(lambda dt: self.create_report_content(), 0)

        # 显示加载指示器
        self.show_loading()

        # 异步从API加载额外数据（如果需要补充维度/趋势等）
        threading.Thread(target=self._fetch_additional_data, daemon=True).start()

    def _fetch_additional_data(self):
        """获取额外的维度数据，包含超时处理和缓存机制"""
        try:
            cloud_api = get_cloud_api()
            logger.info(f"开始获取额外数据，认证状态: {cloud_api.is_authenticated()}, report_id: {self.report_id}, report_type: {self.report_type}")
            if cloud_api.is_authenticated() and self.report_id:
                # 获取维度评估结果
                dimension_data = None
                # 设置较短的超时时间，避免长时间等待
                api_timeout = 10  # 10秒超时
                
                # 检查是否有缓存的维度数据
                cache_key = f"dimension_data_{self.report_id}"
                cached_data = getattr(self, '_dimension_cache', {}).get(cache_key)
                if cached_data:
                    logger.info("使用缓存的维度数据")
                    dimension_data = cached_data
                else:
                    try:
                        if self.report_type == 'assessment':
                            logger.info(f"获取评估维度数据，report_id: {self.report_id}")
                            # 对于评估，使用report_id作为assessment_result_id
                            assessment_result_id = self.report_id
                            logger.info(f"使用assessment_result_id: {assessment_result_id}")
                            dimension_response = cloud_api._make_request(
                                method="GET",
                                endpoint=f"assessment_results/{assessment_result_id}",
                                timeout=api_timeout
                            )
                            logger.info(f"评估结果数据响应: {dimension_response}")
                            if dimension_response and dimension_response.get("success"):
                                data = dimension_response.get("data", {})
                                dimension_scores = data.get("dimension_scores")
                                if dimension_scores:
                                    dimension_data = dimension_scores
                                    logger.info(f"从评估结果中获取到维度数据: {dimension_data}")
                        elif self.report_type == 'questionnaire':
                            logger.info(f"获取问卷维度数据，report_id: {self.report_id}")
                            # 对于问卷，使用report_id作为questionnaire_result_id
                            questionnaire_result_id = self.report_id
                            logger.info(f"使用questionnaire_result_id: {questionnaire_result_id}")
                            # 首先尝试直接获取维度数据
                            dimension_response = cloud_api._make_request(
                                method="GET",
                                endpoint=f"dimensions/questionnaire/response/{questionnaire_result_id}/dimension-scores",
                                timeout=api_timeout
                            )
                            logger.info(f"问卷维度数据响应: {dimension_response}")
                            if dimension_response and dimension_response.get("status") == "success":
                                dimension_data = dimension_response.get("dimension_scores", {})
                            # 如果上面的方法失败，尝试从问卷结果中获取
                            if not dimension_data:
                                dimension_response = cloud_api._make_request(
                                    method="GET",
                                    endpoint=f"questionnaire_results/{questionnaire_result_id}",
                                    timeout=api_timeout
                                )
                                logger.info(f"问卷结果数据响应: {dimension_response}")
                                if dimension_response and dimension_response.get("success"):
                                    data = dimension_response.get("data", {})
                                    dimension_scores = data.get("dimension_scores")
                                    if dimension_scores:
                                        dimension_data = dimension_scores
                                        logger.info(f"从问卷结果中获取到维度数据: {dimension_data}")
                        else:
                            logger.info(f"未知的报告类型: {self.report_type}，尝试使用report_id: {self.report_id}")
                            # 尝试两种方式
                            for attempt_type in ['assessment', 'questionnaire']:
                                try:
                                    if attempt_type == 'assessment':
                                        dimension_response = cloud_api._make_request(
                                            method="GET",
                                            endpoint=f"assessment_results/{self.report_id}",
                                            timeout=api_timeout
                                        )
                                        logger.info(f"{attempt_type}结果数据响应: {dimension_response}")
                                        if dimension_response and dimension_response.get("success"):
                                            data = dimension_response.get("data", {})
                                            dimension_scores = data.get("dimension_scores")
                                            if dimension_scores:
                                                dimension_data = dimension_scores
                                                self.report_type = attempt_type
                                                logger.info(f"从{attempt_type}结果中获取到维度数据: {dimension_data}")
                                                break
                                    else:  # questionnaire
                                        dimension_response = cloud_api._make_request(
                                            method="GET",
                                            endpoint=f"dimensions/{attempt_type}/response/{self.report_id}/dimension-scores",
                                            timeout=api_timeout
                                        )
                                        logger.info(f"{attempt_type}维度数据响应: {dimension_response}")
                                        if dimension_response and dimension_response.get("status") == "success":
                                            dimension_data = dimension_response.get("dimension_scores", {})
                                            self.report_type = attempt_type
                                            break
                                        # 如果直接获取维度数据失败，尝试从结果中获取
                                        if not dimension_data:
                                            dimension_response = cloud_api._make_request(
                                                method="GET",
                                                endpoint=f"questionnaire_results/{self.report_id}",
                                                timeout=api_timeout
                                            )
                                            logger.info(f"{attempt_type}结果数据响应: {dimension_response}")
                                            if dimension_response and dimension_response.get("success"):
                                                data = dimension_response.get("data", {})
                                                dimension_scores = data.get("dimension_scores")
                                                if dimension_scores:
                                                    dimension_data = dimension_scores
                                                    self.report_type = attempt_type
                                                    logger.info(f"从{attempt_type}结果中获取到维度数据: {dimension_data}")
                                                    break
                                except Exception as e:
                                    logger.warning(f"尝试{attempt_type}维度数据失败: {e}")
                                    continue
                    except Exception as e:
                        logger.warning(f"获取维度数据失败: {e}")
                        import traceback
                        logger.warning(f"获取维度数据失败的堆栈跟踪: {traceback.format_exc()}")
                
                    # 缓存获取到的维度数据
                    if dimension_data:
                        if not hasattr(self, '_dimension_cache'):
                            self._dimension_cache = {}
                        self._dimension_cache[cache_key] = dimension_data
                        logger.info("维度数据已缓存")
                
                # 如果获取到维度数据，更新report_data
                if dimension_data and self.report_data:
                    logger.info(f"获取到维度数据: {dimension_data}")
                    if 'dimension_results' not in self.report_data:
                        self.report_data['dimension_results'] = []
                    else:
                        # 清空现有的维度结果
                        self.report_data['dimension_results'].clear()
                    
                    # 将维度数据转换为列表格式
                    if isinstance(dimension_data, dict):
                        for key, value in dimension_data.items():
                            if isinstance(value, dict):
                                # 从字典中提取维度名称和分数
                                name = value.get('name', key)  # 优先使用中文名称，否则使用键名
                                score = value.get('score', value.get('standard_score', 0))
                                self.report_data['dimension_results'].append({
                                    'name': name,
                                    'score': score
                                })
                            else:
                                # 如果值不是字典，直接使用
                                self.report_data['dimension_results'].append({
                                    'name': key,
                                    'score': value
                                })
                    logger.info(f"更新后的report_data: {self.report_data}")
                else:
                    logger.info("未获取到维度数据")
                
                logger.info(f"维度数据获取完成: {dimension_data}")
            # 重新创建报告内容以包含新获取的维度数据
            Clock.schedule_once(lambda dt: self.update_report_content_with_dimensions(), 0)
        except Exception as e:
            logger.error(f"获取额外数据失败: {e}")
            import traceback
            logger.error(f"获取额外数据失败的堆栈跟踪: {traceback.format_exc()}")
            # 即使获取额外数据失败，也要隐藏加载指示器
            Clock.schedule_once(lambda dt: self.hide_loading(), 0)

    def update_report_content_with_dimensions(self):
        """更新报告内容，包含新获取的维度数据"""
        try:
            self.hide_loading()
            # 如果已经有内容，更新维度分析部分
            content_container = self.ids.get('content_container')
            if content_container and content_container.children:
                # 重新创建完整内容
                self.create_report_content()
            else:
                # 如果没有内容，创建完整内容
                self.create_report_content()
        except Exception as e:
            logger.error(f"更新报告内容失败: {e}")
            self.hide_loading()

    def create_report_content(self):
        """创建报告内容，使用真实数据"""
        content_container = self.ids.get('content_container')
        if not content_container:
            logger.warning("content_container不存在，无法创建报告内容")
            return
            
        content_container.clear_widgets()

        if not self.report_data:
            logger.warning("report_data为空，显示错误信息")
            self.show_error("未找到报告数据")
            return

        logger.info(f"创建报告内容，report_data keys: {list(self.report_data.keys()) if self.report_data else 'None'}")
        
        # 获取主题颜色
        surface_color = getattr(self.theme, 'surfaceContainerColor', [1, 1, 1, 1])
        
        main_card = MDCard(
            orientation='vertical',
            size_hint_y=None,
            md_bg_color=surface_color,
            radius=[dp(16)],
            elevation=2,
            padding=0,
            spacing=dp(8)
        )
        main_card.bind(minimum_height=main_card.setter('height'))

        # 添加各个部分的卡片
        try:
            self.add_summary_card(main_card)
            self.add_basic_info_card(main_card)
            self.add_result_card(main_card)
            self.add_dimension_analysis_card(main_card)
            self.add_trend_analysis_card(main_card)
            self.add_health_risk_card(main_card)
            self.add_recommendations_card(main_card)
        except Exception as e:
            logger.error(f"添加报告卡片时出错: {e}")
            # 显示错误信息给用户
            error_label = MDLabel(
                text=f"创建报告内容失败: {str(e)}",
                theme_text_color="Error",
                size_hint_y=None,
                height=dp(40)
            )
            main_card.add_widget(error_label)

        content_container.add_widget(main_card)
        logger.info("报告内容创建完成")
        
        # 确保隐藏加载指示器
        self.hide_loading()

    def create_label(self, text, font_style="Body", role="medium", color=None, bold=False, halign="left", size_hint_x=None, width=None):
        """创建标签的辅助方法"""
        # 构建kwargs字典，只包含非None的参数
        kwargs = {
            "text": text,
            "font_style": font_style,
            "role": role,
            "bold": bold,
            "halign": halign,
            "size_hint_y": None,
            "size_hint_x": size_hint_x,
            # 修改text_size以支持自动换行，设置为(width, None)以允许垂直扩展
            "text_size": (width if width is not None else None, None)
        }
        
        # 只有当width不为None时才添加到kwargs中
        if width is not None:
            kwargs["width"] = width
        
        label = MDLabel(**kwargs)
        # 绑定texture_size以自动调整高度，支持多行文本
        label.bind(texture_size=lambda inst, ts: setattr(inst, 'height', ts[1] + dp(8)))
        if color:
            label.text_color = color
        return label

    def create_wrapped_label(self, text, font_style="Body", role="medium", color=None, bold=False, halign="left", size_hint_x=None, width=None):
        """创建支持自动换行的标签的辅助方法"""
        # 构建kwargs字典，只包含非None的参数
        kwargs = {
            "text": text,
            "font_style": font_style,
            "role": role,
            "bold": bold,
            "halign": halign,
            "size_hint_y": None,
            "size_hint_x": size_hint_x,
            # 设置text_size以支持自动换行，第一个参数是最大宽度，第二个参数是最大高度
            "text_size": (width if width is not None else None, None)
        }
        
        # 只有当width不为None时才添加到kwargs中
        if width is not None:
            kwargs["width"] = width
        
        label = MDLabel(**kwargs)
        # 绑定texture_size以自动调整高度
        label.bind(texture_size=lambda inst, ts: setattr(inst, 'height', ts[1] + dp(8)))
        if color:
            label.text_color = color
        return label

    def create_full_width_label(self, text, font_style="Body", role="medium", color=None, bold=False, halign="left"):
        """创建满屏宽度显示的标签，支持自动换行"""
        # 创建一个能够自动扩展的标签
        label = MDLabel(
            text=text,
            font_style=font_style,
            role=role,
            bold=bold,
            halign=halign,
            size_hint_y=None,
            # 设置text_size为(None, None)以允许在容器内自动换行
            text_size=(None, None)
        )
        # 绑定texture_size以自动调整高度
        label.bind(texture_size=lambda inst, ts: setattr(inst, 'height', ts[1] + dp(8)))
        if color:
            label.text_color = color
        return label

    def create_row(self, label_text, value_text, highlight=False):
        """创建信息行的辅助方法"""
        # 获取主题颜色
        colors = getattr(self.theme, 'colors', {})
        on_surface_variant = colors.get('on_surface_variant', [0.5, 0.5, 0.5, 1])
        primary_color = colors.get('primary', [0.1, 0.4, 0.8, 1])
        
        row = MDBoxLayout(orientation='horizontal', size_hint_y=None, height=dp(40), spacing=dp(20), padding=[dp(16), dp(8)])
        # 调整标签宽度以支持最大10个汉字（约dp(100)），并增加右边距
        row.add_widget(self.create_label(f"{label_text}:", "Body", "medium", on_surface_variant, False, "left", None, dp(120)))
        # 使用满屏宽度显示的标签，增加左边距以创建间距
        value_label = self.create_full_width_label(value_text, "Body", "medium" if not highlight else "large", primary_color if highlight else None, highlight)
        row.add_widget(value_label)
        return row

    def create_section_card(self, title, content_widgets=None, max_height=None):
        """创建章节卡片的辅助方法"""
        # 获取主题颜色，使用surface_container_high增加对比度
        surface_color = getattr(self.theme, 'colors', {}).get('surface_container_high', [0.92, 0.92, 0.92, 1])
        colors = getattr(self.theme, 'colors', {})
        primary_color = colors.get('primary', [0.1, 0.4, 0.8, 1])
        outline_variant = colors.get('outline_variant', [0.8, 0.8, 0.8, 1])
        
        card = MDCard(
            orientation='vertical',
            size_hint_y=None,
            md_bg_color=surface_color,
            elevation=1,
            radius=[dp(12)],
            padding=0,
            spacing=0
        )
        card.bind(minimum_height=card.setter('height'))

        title_layout = MDBoxLayout(orientation='horizontal', size_hint_y=None, height=dp(56), padding=[dp(20), dp(16)])
        title_layout.add_widget(MDLabel(text=title, font_style="Headline", role="small", bold=True, text_color=primary_color))
        card.add_widget(title_layout)
        card.add_widget(MDDivider(color=outline_variant))

        if max_height:
            scroll = MDScrollView(size_hint_y=None, height=max_height - dp(57))
            content = MDBoxLayout(orientation='vertical', size_hint_y=None, padding=[dp(20), dp(16)], spacing=dp(8))
            content.bind(minimum_height=content.setter('height'))
            if content_widgets:
                for widget in content_widgets:
                    content.add_widget(widget)
            scroll.add_widget(content)
            card.add_widget(scroll)
        else:
            content = MDBoxLayout(orientation='vertical', size_hint_y=None, padding=[dp(20), dp(16)], spacing=dp(8))
            content.bind(minimum_height=content.setter('height'))
            if content_widgets:
                for widget in content_widgets:
                    content.add_widget(widget)
            card.add_widget(content)

        return card

    def add_summary_card(self, main_card):
        """添加摘要卡片"""
        total_score = self.report_data.get('total_score', 'N/A') if self.report_data else 'N/A'
        result_level = self.report_data.get('result_level', 'N/A') if self.report_data else 'N/A'
        
        # 获取健康颜色
        health_green = getattr(self.theme, 'get_health_color', lambda x: [0.3, 0.8, 0.3, 1])('health_green')
        health_orange = getattr(self.theme, 'get_health_color', lambda x: [1.0, 0.6, 0.0, 1])('health_orange')
        health_red = getattr(self.theme, 'get_health_color', lambda x: [1.0, 0.3, 0.3, 1])('health_red')
        
        # 根据得分等级选择颜色
        if isinstance(total_score, (int, float)):
            if total_score >= 80:
                score_color = health_green
                description = "您的健康状况良好，继续保持。"
            elif total_score >= 60:
                score_color = health_orange
                description = "您的健康状况一般，建议关注相关方面。"
            else:
                score_color = health_red
                description = "您的健康状况有待改善，建议寻求专业指导。"
        else:
            score_color = health_orange
            description = "暂无评分信息。"
        
        widgets = [
            self.create_row("总分", str(total_score), highlight=True),
            self.create_row("等级", result_level),
            self.create_full_width_label(
                description,
                color=score_color
            )
        ]
        summary_card = self.create_section_card("健康评估摘要", widgets, max_height=dp(200))
        main_card.add_widget(summary_card)

    def add_basic_info_card(self, main_card):
        """添加基本信息卡片"""
        # 从报告数据中获取提交时间和类型
        submitted_at = self.report_data.get('completed_at', 'N/A') if self.report_data else 'N/A'
        report_type = self.report_data.get('category', 'N/A') if self.report_data else 'N/A'
        
        # 如果completed_at为空，尝试从created_at获取
        if submitted_at == 'N/A' or not submitted_at:
            submitted_at = self.report_data.get('created_at', 'N/A') if self.report_data else 'N/A'
        
        # 格式化时间显示
        if submitted_at != 'N/A' and submitted_at != 'unknown':
            try:
                # 尝试解析ISO格式的时间
                from datetime import datetime
                dt = datetime.fromisoformat(submitted_at.replace('Z', '+00:00'))
                submitted_at = dt.strftime("%Y年%m月%d日 %H:%M")
            except:
                # 如果解析失败，保持原样
                pass
        
        # 格式化报告类型显示
        type_mapping = {
            'assessment': '健康评估',
            'questionnaire': '问卷调查',
            'medical_record': '医疗记录'
        }
        display_type = type_mapping.get(report_type, report_type)
        
        widgets = [
            self.create_row("提交时间", submitted_at),
            self.create_row("类型", display_type)
        ]
        basic_card = self.create_section_card("基本信息", widgets)
        main_card.add_widget(basic_card)

    def add_result_card(self, main_card):
        """添加评估结果卡片"""
        widgets = [
            self.create_full_width_label("详细分数分布"),
        ]
        
        # 添加各项分数，优先级：scores > dimensions > dimension_results > total_score
        scores = self.report_data.get('scores', {}) if self.report_data else {}
        if scores and isinstance(scores, dict):
            for category, score in scores.items():
                widgets.append(self.create_row(category, f"{score}分"))
        else:
            # 如果没有详细分数，显示维度分析
            dimensions = self.report_data.get('dimensions', []) if self.report_data else []
            if dimensions:
                for dim in dimensions:
                    if isinstance(dim, dict) and 'name' in dim and 'score' in dim:
                        widgets.append(self.create_row(dim['name'], f"{dim['score']}分"))
                    elif isinstance(dim, dict):  # 兼容不同的字典格式
                        name = dim.get('name', dim.get('dimension_name', '未知维度'))
                        score = dim.get('score', dim.get('dimension_score', 'N/A'))
                        widgets.append(self.create_row(name, f"{score}分"))
            else:
                # 检查是否有后端数据表中的维度评估结果
                dimension_results = self.report_data.get('dimension_results', []) if self.report_data else []
                if dimension_results:
                    logger.info(f"显示维度结果: {dimension_results}")
                    for dim_result in dimension_results:
                        if isinstance(dim_result, dict):
                            name = dim_result.get('name', '未知维度')
                            score = dim_result.get('score', 'N/A')
                            widgets.append(self.create_row(name, f"{score}分"))
                        else:
                            # 如果是其他格式，直接显示
                            widgets.append(self.create_full_width_label(str(dim_result)))
                else:
                    # 如果没有维度分析，显示总分
                    total_score = self.report_data.get('total_score', 0) if self.report_data else 0
                    widgets.append(self.create_row("总体评分", f"{total_score}分"))
        
        result_card = self.create_section_card("详细评估结果", widgets)
        main_card.add_widget(result_card)

    def add_dimension_analysis_card(self, main_card):
        """添加维度分析卡片"""
        dimensions = self.report_data.get('dimensions', []) if self.report_data else []
        if not dimensions:
            return
        widgets = []
        for dim in dimensions:
            if isinstance(dim, dict) and 'name' in dim and 'score' in dim:
                widgets.append(self.create_row(dim['name'], f"{dim['score']}分"))
            elif isinstance(dim, dict):  # 兼容不同的字典格式
                name = dim.get('name', dim.get('dimension_name', '未知维度'))
                score = dim.get('score', dim.get('dimension_score', 'N/A'))
                widgets.append(self.create_row(name, f"{score}分"))
        dim_card = self.create_section_card("多维度健康分析", widgets)
        main_card.add_widget(dim_card)

    def add_trend_analysis_card(self, main_card):
        """添加趋势分析卡片"""
        trends = self.report_data.get('trends', []) if self.report_data else []
        if not trends:
            return
            
        widgets = []
        for trend in trends:
            if isinstance(trend, dict) and 'date' in trend and 'score' in trend:
                widgets.append(self.create_row(trend['date'], f"{trend['score']}"))
            elif isinstance(trend, dict):  # 兼容不同的字典格式
                date = trend.get('date', trend.get('time', '未知时间'))
                score = trend.get('score', 'N/A')
                widgets.append(self.create_row(date, f"{score}"))
        
        # 添加进度条显示最新分数
        if trends:
            latest_score = None
            if isinstance(trends[-1], dict):
                latest_score = trends[-1].get('score')
            else:
                latest_score = trends[-1] if len(trends) > 0 else 0
            
            if latest_score is not None:
                # 获取主题颜色
                colors = getattr(self.theme, 'colors', {})
                primary_color = colors.get('primary', [0.1, 0.4, 0.8, 1])
                progress = MDLinearProgressIndicator(value=latest_score, indicator_color=primary_color)
                widgets.append(progress)
            
        trend_card = self.create_section_card("健康趋势分析", widgets, max_height=dp(300))
        main_card.add_widget(trend_card)

    def add_health_risk_card(self, main_card):
        """添加健康风险卡片"""
        risks = self.report_data.get('risk_factors', []) if self.report_data else []
        strengths = self.report_data.get('strength_factors', []) if self.report_data else []
        if not risks and not strengths:
            return
            
        # 获取健康颜色
        health_red = getattr(self.theme, 'get_health_color', lambda x: [1.0, 0.3, 0.3, 1])('health_red')
        health_green = getattr(self.theme, 'get_health_color', lambda x: [0.3, 0.8, 0.3, 1])('health_green')
        
        widgets = []
        
        # 风险因素
        if risks:
            widgets.append(self.create_full_width_label("风险因素", color=health_red))
            for risk in risks:
                if isinstance(risk, str):
                    widgets.append(self.create_full_width_label(f"• {risk}", color=health_red))
                elif isinstance(risk, dict):
                    risk_text = risk.get('description', risk.get('name', str(risk)))
                    widgets.append(self.create_full_width_label(f"• {risk_text}", color=health_red))
        
        # 优势因素
        if strengths:
            widgets.append(self.create_full_width_label("优势因素", color=health_green))
            for strength in strengths:
                if isinstance(strength, str):
                    widgets.append(self.create_full_width_label(f"• {strength}", color=health_green))
                elif isinstance(strength, dict):
                    strength_text = strength.get('description', strength.get('name', str(strength)))
                    widgets.append(self.create_full_width_label(f"• {strength_text}", color=health_green))
                
        risk_card = self.create_section_card("健康风险评估", widgets)
        main_card.add_widget(risk_card)

    def add_recommendations_card(self, main_card):
        """添加建议卡片"""
        # 从报告数据中获取建议
        recs = self.report_data.get('recommendations', []) if self.report_data else []
        
        # 如果没有明确的建议，从报告内容中提取
        if not recs:
            report_content = self.report_data.get('report', '') if self.report_data else ''
            if report_content:
                # 查找建议部分
                import re
                # 查找"建议"或"专业建议"部分
                suggestion_match = re.search(r'###?\s*建议\s*[\s\S]*?(?=\n###?|$)', report_content)
                if suggestion_match:
                    suggestion_text = suggestion_match.group(0)
                    # 提取建议条目
                    rec_lines = suggestion_text.split('\n')
                    for line in rec_lines:
                        # 匹配以-或•开头的建议条目
                        if line.strip().startswith(('-', '•')):
                            recs.append(line.strip()[1:].strip())  # 去掉前缀
                        # 匹配以数字开头的建议条目
                        elif re.match(r'\d+\.\s*', line.strip()):
                            recs.append(re.sub(r'\d+\.\s*', '', line.strip()))
        
        # 如果仍然没有建议，添加默认建议
        if not recs:
            recs = [
                "请根据评估结果关注相关方面",
                "如有疑问，请咨询专业人员",
                "保持良好的生活习惯"
            ]
        
        # 使用支持自动换行的标签
        widgets = [self.create_full_width_label(f"• {rec}") for rec in recs]
        rec_card = self.create_section_card("健康建议", widgets)
        main_card.add_widget(rec_card)

    def add_next_steps_card(self, main_card):
        """添加下一步行动卡片"""
        widgets = []
        actions = [
            ("预约健康咨询", self.go_to_appointment),
            ("查看详细建议", self.view_details),
            ("分享报告", self.share_report)
        ]
        
        # 获取主题颜色
        colors = getattr(self.theme, 'colors', {})
        primary_color = colors.get('primary', [0.1, 0.4, 0.8, 1])
        
        for text, callback in actions:
            btn = MDButton(
                MDButtonText(text=text),
                style="filled",
                md_bg_color=primary_color,
                size_hint_y=None,
                height=dp(48)
            )
            btn.bind(on_release=callback)
            widgets.append(btn)
            
        next_card = self.create_section_card("下一步行动", widgets)
        main_card.add_widget(next_card)

    def go_to_appointment(self, instance):
        """跳转到预约页面"""
        # TODO: 实现跳转到预约页面的逻辑
        self.show_info("跳转到预约页面")

    def view_details(self, instance):
        """查看详细建议"""
        self.show_info("查看详细建议")

    def share_report(self, instance):
        """分享报告"""
        # TODO: 实现分享逻辑
        self.show_info("报告已分享")

    def show_loading(self):
        """显示加载指示器"""
        content_container = self.ids.get('content_container')
        if content_container:
            # 获取主题颜色
            colors = getattr(self.theme, 'colors', {})
            primary_color = colors.get('primary', [0.1, 0.4, 0.8, 1])
            progress = MDLinearProgressIndicator(indicator_color=primary_color)
            content_container.add_widget(progress)

    def hide_loading(self):
        """隐藏加载指示器"""
        content_container = self.ids.get('content_container')
        if content_container:
            for child in content_container.children:
                if isinstance(child, MDLinearProgressIndicator):
                    content_container.remove_widget(child)
                    break

    def show_error(self, message):
        """显示错误信息"""
        # 获取主题颜色
        colors = getattr(self.theme, 'colors', {})
        error_color = colors.get('error', [1.0, 0.0, 0.0, 1])
        
        MDSnackbar(
            MDSnackbarText(text=message),
            md_bg_color=error_color
        ).open()

    def show_info(self, message):
        """显示信息提示"""
        # 获取主题颜色
        colors = getattr(self.theme, 'colors', {})
        primary_color = colors.get('primary', [0.1, 0.4, 0.8, 1])
        
        MDSnackbar(
            MDSnackbarText(text=message),
            md_bg_color=primary_color
        ).open()

    def show_empty_state(self):
        """显示空状态"""
        self.show_error("暂无报告数据")

    def go_back(self, *args):
        """返回上一页"""
        self.manager.current = 'survey_screen'

    def on_action(self):
        """处理右上角刷新按钮点击事件"""
        logger.info("[刷新按钮] 刷新报告数据")
        self.refresh_report()

    def refresh_report(self):
        """刷新报告数据"""
        logger.info("刷新报告数据")
        # 重新创建报告内容
        if hasattr(self, 'report_data') and self.report_data:
            self.create_report_content()
