# -*- coding: utf-8 -*-
"""
通用表单屏幕基类
提供问卷和评估量表的共同功能
"""

import json
import logging
import threading
from datetime import datetime
from typing import Optional, Dict, Any, List, Union

from kivy.clock import Clock
from kivy.metrics import dp
from kivy.properties import StringProperty, ListProperty, ObjectProperty, BooleanProperty, NumericProperty
from kivy.uix.scrollview import ScrollView

from kivymd.app import MDApp
from kivymd.uix.boxlayout import MDBoxLayout
from kivy.uix.boxlayout import BoxLayout
from kivymd.uix.button import MDButton, MDButtonText
from kivymd.uix.card import MDCard
from kivymd.uix.label import MDLabel
from kivymd.uix.textfield import MDTextField
from kivymd.uix.selectioncontrol import MDCheckbox
from kivymd.uix.slider import MDSlider
# 修复：KivyMD 2.0.1中使用progressindicator而不是progressbar
from kivymd.uix.progressindicator import MDCircularProgressIndicator, MDLinearProgressIndicator
from kivymd.uix.snackbar import MDSnackbar, MDSnackbarText

from screens.base_screen import BaseScreen
from utils.cloud_api import get_cloud_api
from utils.user_manager import get_user_manager
from kivy.factory import Factory
try:
    from mobile.theme import AppTheme, AppMetrics
except ImportError:
    # 如果无法从mobile包导入，则尝试直接导入
    try:
        from theme import AppTheme, AppMetrics
    except ImportError:
        # 如果两个都失败，抛出异常而不是创建模拟类
        raise ImportError("无法导入主题配置，请检查theme.py文件是否存在且可访问")

# 获取日志记录器
logger = logging.getLogger(__name__)

# 共享KV字符串
FORM_SCREEN_KV = '''
<FormScreen>:
    canvas.before:
        Color:
            rgba: [0.98, 0.98, 0.98, 1]  # 非常浅的灰色背景，确保与白色卡片有对比
        Rectangle:
            pos: self.pos
            size: self.size

    MDBoxLayout:
        orientation: 'vertical'
        spacing: dp(8)

        # 顶部应用栏
        MDBoxLayout:
            id: app_bar
            orientation: 'horizontal'
            size_hint_y: None
            height: dp(56)
            md_bg_color: root._get_theme_color("PRIMARY_COLOR") if root and hasattr(root, '_get_theme_color') else [0.2, 0.6, 1, 1]
            padding: [dp(4), dp(0), dp(4), dp(0)]

            MDIconButton:
                icon: "arrow-left"
                font_size: dp(24)
                theme_icon_color: "Custom"
                icon_color: root._get_theme_color("TEXT_LIGHT") if root and hasattr(root, '_get_theme_color') else [1, 1, 1, 1]
                on_release: root.go_back()

            MDLabel:
                text: root.title
                theme_text_color: "Custom"
                text_color: root._get_theme_color("TEXT_LIGHT") if root and hasattr(root, '_get_theme_color') else [1, 1, 1, 1]
                halign: "center"
                valign: "center"
                font_style: "Title"

            MDIconButton:
                icon: "help-circle"
                font_size: dp(24)
                theme_icon_color: "Custom"
                icon_color: root._get_theme_color("TEXT_LIGHT") if root and hasattr(root, '_get_theme_color') else [1, 1, 1, 1]
                on_release: root.show_info("请回答所有问题后点击提交按钮。")

        # 进度条
        MDBoxLayout:
            orientation: 'vertical'
            size_hint_y: None
            height: dp(48)
            padding: [dp(16), dp(8)]

            MDLabel:
                text: f"完成进度: {root.progress:.1f}%"
                theme_text_color: "Secondary"
                size_hint_y: None
                height: dp(20)

            # 使用线性进度指示器
            MDLinearProgressIndicator:
                id: progress_bar
                value: root.progress
                size_hint_y: None
                height: dp(4)

        # 表单容器
        ScrollView:
            do_scroll_x: False
            do_scroll_y: True

            MDBoxLayout:
                id: form_container
                orientation: 'vertical'
                size_hint_y: None
                height: self.minimum_height
                padding: [dp(16), dp(16)]
                spacing: dp(16)
'''


class FormScreen(BaseScreen):
    """表单屏幕基类"""
    title = StringProperty("表单")
    questions = ListProperty([])
    answers = ListProperty([])  # type: ignore
    current_data = ObjectProperty(None)  # 统一为current_data (assessment/questionnaire)
    is_submitting = BooleanProperty(False)
    progress = NumericProperty(0)

    def __init__(self, **kwargs):
        super().__init__(**kwargs)
        self.app = MDApp.get_running_app()
        # 明确指定answers的类型
        self.answers: List[Union[None, str, Dict[str, Any]]] = []
        # 延迟初始化，确保KV文件已加载
        Clock.schedule_once(self.init_ui, 0.1)

    def _get_theme_color(self, color_name: str, default_color: Optional[List[float]] = None) -> List[float]:
        """
        安全地获取主题颜色属性
        """
        if default_color is None:
            default_color = [0.5, 0.5, 0.5, 1.0]  # 默认灰色
            
        try:
            if self.app and hasattr(self.app, 'theme') and self.app.theme:
                theme = self.app.theme
                if hasattr(theme, color_name):
                    color = getattr(theme, color_name)
                    if color is not None:
                        return color
        except Exception as e:
            logger.warning(f"获取主题颜色 {color_name} 时出错: {e}")
            
        return default_color if default_color is not None else [0.5, 0.5, 0.5, 1.0]

    def _get_theme_attr(self, attr_name: str, default_value: Any = None) -> Any:
        """
        安全地获取主题属性
        """
        try:
            if self.app and hasattr(self.app, 'theme') and self.app.theme:
                theme = self.app.theme
                if hasattr(theme, attr_name):
                    value = getattr(theme, attr_name)
                    if value is not None:
                        return value
        except Exception as e:
            logger.warning(f"获取主题属性 {attr_name} 时出错: {e}")
            
        return default_value

    def on_enter(self, *args):
        """进入屏幕时调用，每次都重新初始化"""
        logger.info(f"进入{self.__class__.__name__}页面")
        # 每次进入页面都重新初始化UI，确保获取最新的数据
        Clock.schedule_once(self.init_ui, 0.1)

    def init_ui(self, dt=None):
        """初始化UI"""
        try:
            logger.info(f"初始化{self.__class__.__name__} UI")
            # 获取当前数据
            self.current_data = self.get_current_data()

            logger.debug(f"获取到的数据: {self.current_data}")

            if not self.current_data:
                logger.error("未找到数据")
                self.show_error("未找到数据")
                # 延迟执行返回操作，避免在UI初始化过程中立即跳转
                Clock.schedule_once(lambda dt: self.go_back(), 1.0)
                return

            # 设置标题
            form_title = self.current_data.get('title', '')
            if not form_title and 'template' in self.current_data and 'name' in self.current_data['template']:
                form_title = self.current_data['template']['name']

            if form_title:
                self.title = form_title
                logger.info(f"设置标题: {form_title}")

            # 获取问题列表
            self.questions = self.get_questions()
            
            # 如果仍然没有问题列表，创建默认问题
            if not self.questions:
                logger.warning("未找到问题列表，创建默认问题")
                self.questions = self.create_default_questions()

            logger.info(f"最终获得的问题数量: {len(self.questions)}")

            # 初始化答案列表
            self.answers = [None] * len(self.questions)

            # 创建问题表单
            logger.info("开始创建问题表单")
            self.create_question_form()
            logger.info("问题表单创建完成")
        except Exception as e:
            logger.error(f"初始化表单时出错: {e}")
            import traceback
            logger.error(traceback.format_exc())
            self.show_error(f"初始化表单时出错: {str(e)}")

    def get_current_data(self):
        """
        获取当前数据
        子类必须实现此方法
        """
        raise NotImplementedError("子类必须实现get_current_data")

    def get_questions(self):
        """
        获取问题列表
        可以被子类覆盖以实现特定的获取逻辑
        """
        questions = self._get_questions_from_data()
        return self.standardize_questions(questions) if questions else []
    
    def _get_questions_from_data(self):
        """从数据中获取问题列表"""
        # 优先检查根级别的questions字段
        if self._has_root_questions():
            questions = self.current_data['questions']
            logger.info(f"从根级别获取问题列表，共 {len(questions)} 个问题")
            return questions
        
        # 如果根级别没有，检查template.questions
        if self._has_template_questions():
            questions = self.current_data['template']['questions']
            logger.info(f"从template获取问题列表，共 {len(questions)} 个问题")
            # 同时将questions提取到根级别，保持兼容性
            self.current_data['questions'] = questions
            return questions
        
        return []
    
    def _has_root_questions(self):
        """检查是否有根级别的问题"""
        return ('questions' in self.current_data and 
                self.current_data['questions'])
    
    def _has_template_questions(self):
        """检查是否有模板问题"""
        return ('template' in self.current_data and 
                'questions' in self.current_data['template'])

    def standardize_questions(self, questions):
        """
        标准化问题格式
        统一字段名称和结构
        """
        standardized = []
        for q in questions:
            # 创建问题副本以避免修改原始数据
            question = q.copy()
            
            # 统一文本字段
            question['text'] = question.get('text', question.get('question_text', ''))
            
            # 统一类型字段
            question['type'] = question.get('type', question.get('question_type', 'text'))
            
            # 统一必填字段
            question['required'] = question.get('required', question.get('is_required', True))
            
            # 标准化选项格式
            if 'options' in question and question['options']:
                standardized_options = []
                for opt in question['options']:
                    if isinstance(opt, dict):
                        # 确保选项有label和value字段
                        standardized_opt = {
                            'value': opt.get('value', ''),
                            'label': opt.get('label', opt.get('text', ''))
                        }
                        standardized_options.append(standardized_opt)
                    else:
                        # 如果选项不是字典，创建标准格式
                        standardized_options.append({
                            'value': str(opt),
                            'label': str(opt)
                        })
                question['options'] = standardized_options
            
            standardized.append(question)
            
        return standardized

    def create_default_questions(self):
        """
        创建默认问题
        当无法获取问题列表时使用
        """
        return [
            {
                'id': i + 1,
                'text': f'问题 {i + 1}',
                'type': 'radio',
                'options': [
                    {'value': str(j), 'label': f'选项{j}'} 
                    for j in range(1, 6)
                ],
                'required': True
            } for i in range(5)
        ]

    def create_question_form(self):
        """创建问题表单"""
        try:
            logger.info(f"开始创建问题表单，问题数量: {len(self.questions)}")

            # 检查form_container是否存在
            if 'form_container' not in self.ids:
                logger.error("form_container不存在，UI可能未正确初始化")
                # 延迟重试
                Clock.schedule_once(lambda dt: self.create_question_form(), 0.5)
                return

            # 清空现有内容
            self.ids.form_container.clear_widgets()

            if not self.questions:
                logger.warning("问题列表为空，显示空状态")
                # 显示空状态
                self.ids.form_container.add_widget(MDLabel(
                    text="暂无问题数据",
                    theme_text_color="Secondary",
                    halign="center",
                    size_hint_y=None,
                    height=dp(100)
                ))
                return

            # 添加描述
            self.add_description(self.ids.form_container)

            # 添加问题卡片
            for i, question in enumerate(self.questions):
                question_card = self.create_question_card(i, question)
                if question_card:
                    self.ids.form_container.add_widget(question_card)
                else:
                    logger.error(f"第 {i+1} 个问题卡片创建失败")

                # 在问题之间添加间距（除了最后一个问题）
                if i < len(self.questions) - 1:
                    self.ids.form_container.add_widget(MDBoxLayout(
                        size_hint_y=None,
                        height=dp(16)
                    ))

            # 添加提交按钮
            self.add_submit_button(self.ids.form_container)

            logger.info("问题表单创建完成")

        except Exception as e:
            logger.error(f"创建问题表单时出错: {e}")
            import traceback
            logger.error(traceback.format_exc())
            self.show_error(f"创建表单时出错: {str(e)}")

    def add_description(self, container):
        """
        添加表单描述
        子类可以覆盖此方法以实现特定的描述逻辑
        """
        try:
            description = ""
            
            if self.current_data:
                # 尝试从不同位置获取描述，确保template是字典类型
                template = self.current_data.get('template', {})
                if isinstance(template, dict):
                    template_description = template.get('description')
                else:
                    template_description = None

                description = (
                    self.current_data.get('description') or
                    template_description or
                    ""
                )

            # 如果有描述，则显示
            if description:
                # 创建描述卡片
                desc_card = MDCard(
                    orientation="vertical",
                    size_hint_y=None,
                    height=dp(60),  # 设置初始最小高度
                    padding=[dp(12), dp(8)],
                    radius=[dp(6)],
                    elevation=1,
                    md_bg_color=self._get_theme_color("SURFACE_COLOR"),
                    shadow_softness=2
                )

                desc_label = MDLabel(
                    text=description,
                    theme_text_color="Secondary",
                    font_size=dp(11),
                    text_size=(None, None),
                    halign="left",
                    valign="top",
                    size_hint_y=None
                )

                # 绑定宽度变化以实现自适应
                def update_desc_size(instance, value):
                    instance.text_size = (instance.width - dp(24), None)
                    instance.texture_update()
                    if instance.texture_size[1] > 0:
                        instance.height = int(instance.texture_size[1] + dp(4))  # 修复类型错误，确保是int类型

                desc_label.bind(width=update_desc_size)
                desc_card.add_widget(desc_label)
                desc_card.height = int(max(dp(60), desc_label.height + dp(16)))  # 修复类型错误，确保是int类型

                container.add_widget(desc_card)

                # 添加间距
                container.add_widget(MDBoxLayout(
                    size_hint_y=None,
                    height=dp(12)
                ))

                logger.info("已添加表单描述")

        except Exception as e:
            logger.error(f"添加表单描述时出错: {e}")

    def create_question_card(self, index, question):
        """创建问题卡片"""
        try:
            # 获取问题数据
            question_text = question.get('text', '')
            # 如果问题文本为空，则使用默认文本
            if not question_text:
                question_text = f'未设置问题内容'
            question_type = question.get('type', 'text')
            options = question.get('options', [])
            required = question.get('required', True)

            # 创建卡片
            card = MDCard(
                orientation="vertical",
                size_hint_y=None,
                height=dp(100),  # 设置初始最小高度，后续会根据内容调整
                padding=[dp(16), dp(12)],
                radius=[dp(6)],
                elevation=0.5,
                md_bg_color=self._get_theme_color("CARD_BACKGROUND"),
                shadow_softness=2,
                line_color=self._get_theme_color("BORDER_COLOR"),
                line_width=0.5
            )

            # 问题标题
            title_container = MDBoxLayout(
                orientation="horizontal",
                size_hint_y=None,
                height=dp(40),
                spacing=dp(8)
            )

            # 问题序号
            number_label = MDLabel(
                text=f"{index + 1}.",
                theme_text_color="Custom",
                text_color=self._get_theme_color("PRIMARY_COLOR"),
                size_hint_x=None,
                width=dp(32),
                font_size=dp(14),
                bold=True,
                halign="left",
                valign="center"
            )
            title_container.add_widget(number_label)

            # 问题文本
            question_label = MDLabel(
                text=question_text + (" *" if required else ""),
                theme_text_color="Custom",
                text_color=[0.13, 0.13, 0.13, 1],  # 深灰色文字
                font_size=dp(13),
                text_size=(None, None),
                markup=True,
                valign="center"
            )
            # 绑定宽度变化以自动调整文本大小
            question_label.bind(width=lambda instance, width: setattr(instance, 'text_size', (width - dp(16), None)))
            title_container.add_widget(question_label)

            card.add_widget(title_container)

            # 添加分隔线
            card.add_widget(MDBoxLayout(
                size_hint_y=None,
                height=dp(1),
                md_bg_color=self._get_theme_color("BORDER_COLOR")
            ))

            # 添加间距
            card.add_widget(MDBoxLayout(
                size_hint_y=None,
                height=dp(6)
            ))

            # 根据问题类型添加不同的输入控件
            input_container = self.create_input_widget(index, question_type, options, question)
            card.add_widget(input_container)

            # 添加完所有内容后，计算并更新卡片高度
            def update_card_height(dt):
                # 首先更新输入容器的高度
                input_height = 0
                for child in input_container.children:
                    if hasattr(child, 'height'):
                        input_height += child.height
                    if hasattr(child, 'padding'):
                        if isinstance(child.padding, list) and len(child.padding) >= 2:
                            input_height += child.padding[1] * 2

                # 添加间距
                input_height += input_container.spacing * (len(input_container.children) - 1 if len(input_container.children) > 0 else 0)

                # 设置输入容器的高度
                input_container.height = max(dp(50), input_height + dp(10))

                # 然后计算卡片的总高度
                total_height = 0
                for child in card.children:
                    if hasattr(child, 'height'):
                        total_height += child.height
                    if hasattr(child, 'padding'):
                        if isinstance(child.padding, list) and len(child.padding) >= 2:
                            total_height += child.padding[1] * 2

                # 添加卡片间距
                total_height += card.padding[1] * 2 if isinstance(card.padding, list) and len(card.padding) >= 2 else 0

                # 设置最小高度
                card.height = int(max(dp(100), total_height + dp(20)))  # 修复类型错误，确保是int类型

            # 使用Clock延迟调用，确保子组件已完全加载
            Clock.schedule_once(update_card_height, 0.1)

            return card

        except Exception as e:
            logger.error(f"创建问题卡片时出错: {e}")
            import traceback
            logger.error(traceback.format_exc())

            # 返回一个错误卡片
            error_card = MDCard(
                orientation="vertical",
                size_hint_y=None,
                height=dp(80),
                padding=[dp(16), dp(12)],
                radius=[dp(8)],
                elevation=1,
                md_bg_color=self._get_theme_color("ERROR_COLOR", [1, 0.7, 0.7, 1])  # 使用安全访问
            )

            error_label = MDLabel(
                text=f"问题 {index + 1} 加载失败",
                theme_text_color="Custom",
                text_color=self._get_theme_color("TEXT_LIGHT", [1, 1, 1, 1]),  # 使用安全访问
                halign="center",
                font_size=AppMetrics.FONT_SIZE_SMALL
            )
            error_card.add_widget(error_label)
            return error_card

    def create_input_widget(self, index, question_type, options, question):
        """创建输入控件"""
        container = MDBoxLayout(
            orientation="vertical",
            spacing=dp(8),
            size_hint_y=None,
            height=dp(50)
        )
        
        # 题目active/inactive逻辑
        is_active = question.get('active', True)
        container.disabled = not is_active
        container.opacity = 1.0 if is_active else 0.3

        # 根据问题类型添加不同的输入控件
        if question_type in ['radio', 'single_choice']:
            # 单选题
            radio_container = MDBoxLayout(
                orientation="vertical",
                spacing=dp(2),
                size_hint_y=None
            )

            if options:
                # 创建表格样式的选项布局
                table_container = MDBoxLayout(
                    orientation="vertical",
                    spacing=dp(1),
                    size_hint_y=None,
                    padding=[dp(8), dp(4)]
                )

                for i, option in enumerate(options):
                    option_row = MDBoxLayout(
                        orientation="horizontal",
                        size_hint_y=None,
                        height=dp(36),
                        spacing=dp(12),
                        padding=[dp(4), dp(2)]
                    )

                    # 选项序号/标识
                    option_value = option.get('value', str(i))
                    option_id = MDLabel(
                        text=f"({option_value})",
                        theme_text_color="Custom",
                        text_color=self._get_theme_color("PRIMARY_COLOR"),  # 使用安全访问
                        font_size=dp(12),
                        size_hint_x=None,
                        width=dp(40),
                        halign="center",
                        valign="center",
                        bold=True
                    )
                    option_row.add_widget(option_id)

                    # 选项文本
                    option_text = option.get('label', f'选项 {i+1}')
                    # 确保选项文本不为空
                    if not option_text or option_text.strip() == '':
                        option_text = f'选项 {i+1}'

                    option_label = MDLabel(
                        text=option_text,
                        theme_text_color="Custom",
                        text_color=[0.13, 0.13, 0.13, 1],  # 深灰色文字
                        font_size=dp(12),
                        text_size=(None, None),
                        valign="center"
                    )
                    # 绑定宽度变化以自动调整文本大小
                    option_label.bind(width=lambda instance, width: setattr(instance, 'text_size', (width - dp(16), None)))
                    option_row.add_widget(option_label)

                    # 单选按钮放在右侧
                    radio_btn = MDCheckbox(
                        group=f"question_{index}",
                        size_hint=(None, None),
                        size=(dp(24), dp(24)),
                        pos_hint={'center_y': 0.5},
                        active=False,
                        color_inactive=self._get_theme_color("BORDER_COLOR"),  # 使用安全访问
                        color_active=self._get_theme_color("PRIMARY_COLOR"),   # 使用安全访问
                        disabled_color=self._get_theme_color("BORDER_COLOR")   # 使用安全访问
                    )
                    # 设置radio按钮的属性
                    radio_btn.question_index = index
                    radio_btn.option_index = i
                    radio_btn.option_value = option.get('value', i)
                    radio_btn.bind(active=self.on_radio_answer)

                    radio_container_widget = MDBoxLayout(
                        size_hint_x=None,
                        width=dp(40),
                        orientation="horizontal"
                    )
                    radio_container_widget.add_widget(radio_btn)
                    option_row.add_widget(radio_container_widget)

                    # 添加分隔线效果（除了最后一行）
                    if i < len(options) - 1:
                        option_row.md_bg_color = self._get_theme_color("SURFACE_COLOR") if i % 2 == 0 else self._get_theme_color("CARD_BACKGROUND")

                    table_container.add_widget(option_row)

                # 添加边框效果
                border_card = MDCard(
                    orientation="vertical",
                    size_hint_y=None,
                    height=int(len(options) * dp(36) + dp(16)),  # 修复类型错误，确保是int类型
                    padding=[dp(4), dp(4)],
                    radius=[dp(6)],
                    elevation=0,
                    md_bg_color=self._get_theme_color("SURFACE_COLOR"),
                    line_color=self._get_theme_color("BORDER_COLOR"),
                    line_width=1
                )

                table_container.height = int(len(options) * dp(36))  # 修复类型错误，确保是int类型
                border_card.add_widget(table_container)
                radio_container.add_widget(border_card)

                container_height = int(len(options) * dp(36) + dp(24))  # 修复类型错误，确保是int类型
            else:
                # 没有选项时显示提示
                no_options_label = MDLabel(
                    text="暂无选项",
                    theme_text_color="Secondary",
                    font_size=AppMetrics.FONT_SIZE_SMALL,
                    halign="center",
                    size_hint_y=None,
                    height=dp(32)
                )
                radio_container.add_widget(no_options_label)
                container_height = dp(32)

            radio_container.height = container_height
            container.height = container_height
            container.add_widget(radio_container)

        elif question_type in ['checkbox', 'multiple_choice']:
            # 多选题
            checkbox_container = MDBoxLayout(
                orientation="vertical",
                spacing=dp(6),
                size_hint_y=None
            )
            if options:
                for i, option in enumerate(options):
                    option_container = MDBoxLayout(
                        orientation="horizontal",
                        size_hint_y=None,
                        height=dp(36),
                        spacing=dp(8)
                    )
                    checkbox = MDCheckbox(
                        size_hint=(None, None),
                        size=(dp(20), dp(20)),
                        pos_hint={'center_y': 0.5},
                        active=False,
                        color_inactive=self._get_theme_color("BORDER_COLOR"),  # 使用安全访问
                        color_active=self._get_theme_color("PRIMARY_COLOR"),   # 使用安全访问
                        disabled_color=self._get_theme_color("BORDER_COLOR")   # 使用安全访问
                    )
                    checkbox.question_index = index
                    checkbox.option_index = i
                    checkbox.option_value = option.get('value', i)
                    checkbox.bind(active=lambda x, active, opt_idx=i: self.on_checkbox_answer(x, active))
                    option_container.add_widget(checkbox)
                    option_text = option.get('label', f'选项 {i+1}')
                    option_label = MDLabel(
                        text=option_text,
                        theme_text_color="Custom",
                        text_color=[0.13, 0.13, 0.13, 1],
                        font_size=dp(12),
                        size_hint_y=None,
                        height=dp(36),
                        text_size=(None, None),
                        valign="center"
                    )
                    option_container.add_widget(option_label)
                    checkbox_container.add_widget(option_container)
                container_height = int(len(options) * dp(36) + (len(options) - 1) * dp(6))  # 修复类型错误，确保是int类型
            else:
                no_options_label = MDLabel(
                    text="暂无选项",
                    theme_text_color="Secondary",
                    font_size=AppMetrics.FONT_SIZE_SMALL,
                    halign="center",
                    size_hint_y=None,
                    height=dp(32)
                )
                checkbox_container.add_widget(no_options_label)
                container_height = dp(32)
            checkbox_container.height = container_height
            container.height = container_height
            container.add_widget(checkbox_container)
            
        elif question_type in ['text', 'textarea']:
            # 文本输入
            text_field = MDTextField(
                hint_text="请输入答案",
                mode="outlined",
                size_hint_y=None,
                height=dp(80) if question_type == 'textarea' else dp(48),
                font_size=AppMetrics.FONT_SIZE_SMALL,
                line_color_normal=self._get_theme_color("BORDER_COLOR"),
                line_color_focus=self._get_theme_color("PRIMARY_COLOR"),
                multiline=(question_type == 'textarea')
            )
            text_field.question_index = index
            text_field.bind(text=lambda x, text: self.on_text_answer(x, text))
            container.height = text_field.height
            container.add_widget(text_field)
            
        elif question_type == 'number':
            # 数字输入
            number_field = MDTextField(
                hint_text="请输入数字",
                mode="outlined",
                input_filter="int",
                size_hint_y=None,
                height=dp(48),
                font_size=AppMetrics.FONT_SIZE_SMALL,
                line_color_normal=self._get_theme_color("BORDER_COLOR"),
                line_color_focus=self._get_theme_color("PRIMARY_COLOR"),
                multiline=False
            )
            number_field.question_index = index
            number_field.bind(text=lambda x, text: self.on_text_answer(x, text))
            container.height = number_field.height
            container.add_widget(number_field)
            
        elif question_type == 'rating':
            # 评分
            from kivymd.uix.slider import MDSlider
            rating_container = MDBoxLayout(
                orientation="vertical",
                spacing=dp(4),
                size_hint_y=None,
                height=dp(60)
            )
            label = MDLabel(
                text="请选择评分",
                theme_text_color="Secondary",
                font_size=AppMetrics.FONT_SIZE_SMALL,
                halign="left"
            )
            rating_slider = MDSlider(
                min=1,
                max=5,
                value=1,
                step=1,
                size_hint_y=None,
                height=dp(32)
            )
            rating_slider.question_index = index
            def on_rating_value(instance, value):
                self.on_slider_answer(instance, int(value))
            rating_slider.bind(value=on_rating_value)
            rating_container.add_widget(label)
            rating_container.add_widget(rating_slider)
            container.height = dp(60)
            container.add_widget(rating_container)
            
        elif question_type in ['scale', 'likert', 'score']:
            # 量表评分 - 支持更多评分类型
            from kivymd.uix.slider import MDSlider
            scale_container = MDBoxLayout(
                orientation="vertical",
                spacing=dp(4),
                size_hint_y=None,
                height=dp(60)
            )
            
            # 根据选项确定评分范围
            min_val = 1
            max_val = 5
            if options and len(options) > 0:
                max_val = len(options)
            elif question.get('scale_min') and question.get('scale_max'):
                min_val = int(question.get('scale_min', 1))
                max_val = int(question.get('scale_max', 5))
                
            label = MDLabel(
                text=f"请选择评分 ({min_val}-{max_val})",
                theme_text_color="Secondary",
                font_size=AppMetrics.FONT_SIZE_SMALL,
                halign="left"
            )
            scale_slider = MDSlider(
                min=min_val,
                max=max_val,
                value=min_val,
                step=1,
                size_hint_y=None,
                height=dp(32)
            )
            scale_slider.question_index = index
            def on_scale_value(instance, value):
                self.on_slider_answer(instance, int(value))
            scale_slider.bind(value=on_scale_value)
            scale_container.add_widget(label)
            scale_container.add_widget(scale_slider)
            container.height = dp(60)
            container.add_widget(scale_container)
            
        elif question_type in ['select', 'dropdown']:
            # 下拉选择 - 转换为单选处理
            logger.info(f"问题类型 '{question_type}' 转换为单选处理")
            return self.create_input_widget(index, 'radio', options, question)
            
        elif question_type in ['date', 'datetime']:
            # 日期选择 - 使用文本输入
            date_field = MDTextField(
                hint_text="请输入日期 (YYYY-MM-DD)" if question_type == 'date' else "请输入日期时间 (YYYY-MM-DD HH:MM)",
                mode="outlined",
                size_hint_y=None,
                height=dp(48),
                font_size=AppMetrics.FONT_SIZE_SMALL,
                line_color_normal=self._get_theme_color("BORDER_COLOR"),
                line_color_focus=self._get_theme_color("PRIMARY_COLOR"),
                multiline=False
            )
            date_field.question_index = index
            date_field.bind(text=lambda x, text: self.on_text_answer(x, text))
            container.height = date_field.height
            container.add_widget(date_field)
            
        elif question_type == 'time':
            # 时间选择 - 使用文本输入
            time_field = MDTextField(
                hint_text="请输入时间 (HH:MM)",
                mode="outlined",
                size_hint_y=None,
                height=dp(48),
                font_size=AppMetrics.FONT_SIZE_SMALL,
                line_color_normal=self._get_theme_color("BORDER_COLOR"),
                line_color_focus=self._get_theme_color("PRIMARY_COLOR"),
                multiline=False
            )
            time_field.question_index = index
            time_field.bind(text=lambda x, text: self.on_text_answer(x, text))
            container.height = time_field.height
            container.add_widget(time_field)
            
        else:
            # 不支持的类型 - 提供更友好的处理
            logger.warning(f"遇到不支持的问题类型: {question_type}，将使用文本输入作为备选方案")
            
            # 创建一个包含警告信息和文本输入的容器
            fallback_container = MDBoxLayout(
                orientation="vertical",
                spacing=dp(4),
                size_hint_y=None,
                height=dp(80)
            )
            
            # 警告标签
            warning_label = MDLabel(
                text=f"问题类型 '{question_type}' 暂不支持，请使用文本输入",
                theme_text_color="Custom",
                text_color=[0.8, 0.4, 0.0, 1],  # 橙色警告
                font_size=AppMetrics.FONT_SIZE_SMALL,
                halign="left",
                size_hint_y=None,
                height=dp(24)
            )
            
            # 备选文本输入
            fallback_field = MDTextField(
                hint_text="请输入答案",
                mode="outlined",
                size_hint_y=None,
                height=dp(48),
                font_size=AppMetrics.FONT_SIZE_SMALL,
                line_color_normal=self._get_theme_color("BORDER_COLOR"),
                line_color_focus=self._get_theme_color("PRIMARY_COLOR"),
                multiline=False
            )
            fallback_field.question_index = index
            fallback_field.bind(text=lambda x, text: self.on_text_answer(x, text))
            
            fallback_container.add_widget(warning_label)
            fallback_container.add_widget(fallback_field)
            container.height = dp(80)
            container.add_widget(fallback_container)
            
        return container

    def add_submit_button(self, container):
        """添加提交按钮"""
        try:
            container.add_widget(MDBoxLayout(size_hint_y=None, height=dp(24)))
            button_container = MDBoxLayout(
                orientation="horizontal",
                size_hint_y=None,
                height=dp(56),
                padding=[dp(24), 0],
                spacing=dp(16)
            )
            submit_button = MDButton(
                MDButtonText(
                    text="提交",
                    font_size=dp(14),
                    theme_text_color="Custom",
                    text_color="#FFFFFF"
                ),
                style="elevated",
                size_hint=(1, 1),
                md_bg_color="#0D47A1",
                elevation=3,
                on_release=self.on_submit
            )
            button_container.add_widget(submit_button)
            container.add_widget(button_container)
            container.add_widget(MDBoxLayout(size_hint_y=None, height=dp(32)))
        except Exception as e:
            logger.error(f"添加提交按钮时出错: {e}")

    # 答案处理方法
    def on_text_answer(self, instance, value):
        """文本回答变化时的回调"""
        try:
            question_index = getattr(instance, 'question_index', None)
            if question_index is not None:
                # 确保answers列表足够长
                while len(self.answers) <= question_index:
                    self.answers.append(None)
                self.answers[question_index] = value
                self.update_progress()
                logger.debug(f"问题 {question_index + 1} 文本答案更新: {value}")
            else:
                logger.warning("无法获取问题索引，文本答案未保存")
        except Exception as e:
            logger.error(f"处理文本答案时出错: {e}")

    def on_radio_answer(self, instance, value):
        """单选回答变化时的回调"""
        try:
            if value:  # 只处理选中状态
                question_index = getattr(instance, 'question_index', None)
                option_index = getattr(instance, 'option_index', None)
                option_value = getattr(instance, 'option_value', option_index)

                if question_index is not None and option_index is not None:
                    # 确保answers列表足够长
                    while len(self.answers) <= question_index:
                        self.answers.append(None)
                    
                    self.answers[question_index] = {
                        'option_index': option_index,
                        'option_value': option_value,
                        'type': 'radio'
                    }
                    self.update_progress()
                    logger.debug(f"问题 {question_index + 1} 单选答案更新: 选项 {option_index + 1}")
        except Exception as e:
            logger.error(f"处理单选答案时出错: {e}")

    def on_checkbox_answer(self, instance, value):
        """多选回答变化时的回调"""
        try:
            question_index = getattr(instance, 'question_index', None)
            option_index = getattr(instance, 'option_index', None)
            option_value = getattr(instance, 'option_value', option_index)

            if question_index is not None and option_index is not None:
                # 修复：确保answers列表足够长
                while len(self.answers) <= question_index:
                    self.answers.append(None)
                
                # 初始化多选答案
                if self.answers[question_index] is None:
                    self.answers[question_index] = {
                        'selected_options': [],
                        'type': 'checkbox'
                    }
                elif not isinstance(self.answers[question_index], dict) or 'selected_options' not in self.answers[question_index]:
                    self.answers[question_index] = {
                        'selected_options': [],
                        'type': 'checkbox'
                    }

                selected_options = self.answers[question_index]['selected_options']

                if value:  # 选中
                    option_data = {
                        'option_index': option_index,
                        'option_value': option_value
                    }
                    if option_data not in selected_options:
                        selected_options.append(option_data)
                else: # 取消选中
                    # 修复：使用列表推导式创建新列表而不是切片赋值
                    # 明确指定selected_options为List[Dict[str, Any]]类型
                    options_list: List[Dict[str, Any]] = selected_options
                    filtered_options = [opt for opt in options_list
                                      if isinstance(opt, dict) and opt.get('option_index') != option_index]
                    # 清空原列表并添加过滤后的选项
                    selected_options.clear()
                    selected_options.extend(filtered_options)

                self.update_progress()
                logger.debug(f"问题 {question_index + 1} 多选答案更新: {len(selected_options)} 个选项")
            else:
                logger.warning("无法获取问题索引或选项索引，多选答案未保存")
        except Exception as e:
            logger.error(f"处理多选答案时出错: {e}")

    def on_slider_answer(self, instance, value):
        """滑块回答变化时的回调"""
        try:
            question_index = getattr(instance, 'question_index', None)
            if question_index is not None:
                # 确保answers列表足够长
                while len(self.answers) <= question_index:
                    self.answers.append(None)
                
                self.answers[question_index] = {
                    'value': value,
                    'type': 'slider'
                }
                self.update_progress()
                logger.debug(f"问题 {question_index + 1} 滑块答案更新: {value}")
        except Exception as e:
            logger.error(f"处理滑块答案时出错: {e}")

    def update_progress(self):
        """更新完成进度"""
        try:
            if not self.questions:
                self.progress = 0
                return
            answered_count = 0
            active_count = 0
            for i, question in enumerate(self.questions):
                if not question.get('active', True):
                    continue
                active_count += 1
                answer = self.answers[i] if i < len(self.answers) else None
                if self.is_answer_valid(answer):
                    answered_count += 1
            self.progress = (answered_count / active_count) * 100 if active_count else 0

            logger.debug(f"进度更新: {answered_count}/{active_count} = {self.progress:.1f}%")
        except Exception as e:
            logger.error(f"更新进度时出错: {e}")

    def is_answer_valid(self, answer):
        """检查答案是否有效"""
        if answer is None:
            return False

        if isinstance(answer, str):
            return answer.strip() != ""

        if isinstance(answer, dict):
            if answer.get('type') == 'checkbox':
                return bool(answer.get('selected_options'))
            else:
                return True

        return True

    def on_submit(self, instance=None):
        """提交表单"""
        try:
            if self.is_submitting:
                logger.warning("正在提交中，请勿重复提交")
                return
            logger.info("开始提交表单")
            
            # 检查必填问题
            missing_required = []
            for i, question in enumerate(self.questions):
                if question.get('required', True) and question.get('active', True):
                    answer = self.answers[i] if i < len(self.answers) else None
                    if not self.is_answer_valid(answer):
                        missing_required.append(i + 1)
            if missing_required:
                missing_text = "、".join(map(str, missing_required))
                self.show_error(f"请完成必填问题：第 {missing_text} 题")
                return
                
            self.is_submitting = True
            submission_data = self.prepare_submission_data()
            
            # 在后台线程中提交
            threading.Thread(
                target=self.submit_thread,
                args=(submission_data,),
                daemon=True
            ).start()
        except Exception as e:
            logger.error(f"提交表单时出错: {e}")
            import traceback
            logger.error(traceback.format_exc())
            self.show_error(f"提交失败: {str(e)}")
            self.is_submitting = False

    def submit_thread(self, submission_data):
        """在后台线程中提交数据"""
        try:
            logger.info("开始后台提交数据")
            cloud_api = get_cloud_api()
            if not cloud_api.is_authenticated():
                Clock.schedule_once(lambda dt: self.show_error("用户未登录，请先登录"))
                return
                
            result = self.submit_api_call(cloud_api, submission_data)
            
            if result and result.get('status') == 'success':
                logger.info("数据提交成功")
                Clock.schedule_once(lambda dt: self.on_submit_success(result))
            else:
                error_msg = result.get('message', '提交失败') if result else '提交失败'
                logger.error(f"数据提交失败: {error_msg}")
                Clock.schedule_once(lambda dt: self.show_error(f"提交失败: {error_msg}"))
        except Exception as ex:
            logger.error(f"后台提交数据时出错: {ex}")
            import traceback
            logger.error(traceback.format_exc())
            error_message = str(ex)
            Clock.schedule_once(lambda dt: self.show_error(f"提交失败: {error_message}"))
        finally:
            Clock.schedule_once(lambda dt: setattr(self, 'is_submitting', False))

    def submit_api_call(self, cloud_api, submission_data):
        """
        调用具体的API提交方法
        子类必须实现此方法
        """
        raise NotImplementedError("子类必须实现submit_api_call")

    def on_submit_success(self, result):
        """提交成功后的处理"""
        try:
            logger.info("数据提交成功")
            self.show_success("提交成功！")
            
            # 刷新survey_screen
            app = MDApp.get_running_app()
            if app and hasattr(app, 'root') and app.root and hasattr(app.root, 'get_screen'):
                try:
                    survey_screen = app.root.get_screen('survey_screen')
                    if survey_screen:
                        survey_screen.load_response_history()
                        # 根据具体类型刷新对应的数据
                        if hasattr(self, 'refresh_survey_data'):
                            self.refresh_survey_data(survey_screen)
                except Exception as e:
                    logger.error(f"刷新SurveyScreen失败: {e}")
                    
            Clock.schedule_once(lambda dt: self.go_back(), 1.5)
        except Exception as e:
            logger.error(f"处理提交成功回调时出错: {e}")

    def refresh_survey_data(self, survey_screen):
        """
        刷新survey_screen的数据
        子类可以覆盖此方法以实现特定的刷新逻辑
        """
        pass

    def prepare_submission_data(self):
        """准备提交数据"""
        try:
            user_manager = get_user_manager()
            user_info = user_manager.get_user_info()
            
            # 获取template_id，优先从根级别获取，然后从template字段获取
            template_id = None
            if 'template_id' in self.current_data:
                template_id = self.current_data['template_id']
                logger.info(f"从根级别template_id字段获取: {template_id}")
            elif 'template' in self.current_data and isinstance(self.current_data['template'], dict):
                template = self.current_data['template']
                if 'id' in template:
                    template_id = template['id']
                    logger.info(f"从template.id字段获取: {template_id}")
                    # 同时更新到根级别，保持一致性
                    self.current_data['template_id'] = template_id
            
            if template_id is None:
                logger.warning("未能获取到template_id，可能影响提交结果")
            
            submission_data: Dict[str, Any] = {
                'id': self.current_data.get('id'),
                'template_id': template_id,
                'user_id': user_info.get('id') if user_info else None,
                'submitted_at': datetime.now().isoformat(),
                'answers': []
            }
            
            logger.info(f"准备提交数据，ID: {submission_data['id']}, 模板ID: {submission_data['template_id']}")
            
            for i, answer in enumerate(self.answers):
                if i < len(self.questions):
                    question = self.questions[i]
                    question_id = question.get('question_id') or question.get('id')
                    if not question_id:
                        logger.warning(f"问题 {i+1} 缺少question_id，将跳过此问题")
                        continue
                    # 后端要求question_id为字符串
                    question_id = str(question_id)
                    answer_data: Dict[str, Any] = {'question_id': question_id}
                    # 只允许answer字段
                    if answer is not None:
                        if isinstance(answer, str):
                            answer_data['answer'] = answer
                        elif isinstance(answer, dict):
                            if answer.get('type') == 'radio':
                                answer_data['answer'] = answer.get('option_value')
                            elif answer.get('type') == 'checkbox':
                                selected_options = answer.get('selected_options', [])
                                # 修复：确保selected_options是列表类型后再进行列表推导
                                if isinstance(selected_options, list) and selected_options:
                                    # 明确指定selected_options为List[Dict[str, Any]]类型
                                    options_list: List[Dict[str, Any]] = selected_options if isinstance(selected_options, list) else []
                                    answer_data['answer'] = [opt.get('option_value') for opt in options_list if isinstance(opt, dict)]
                                else:
                                    answer_data['answer'] = []
                            elif answer.get('type') == 'slider':
                                answer_data['answer'] = answer.get('value')
                            else:
                                answer_data['answer'] = answer.get('answer') or answer.get('value')
                        else:
                            answer_data['answer'] = answer
                    submission_data['answers'].append(answer_data)
                    
            logger.info(f"准备提交数据完成，共 {len(submission_data['answers'])} 个答案")
            logger.debug(f"完整提交数据: {json.dumps(submission_data)}")
            return submission_data
        except Exception as e:
            logger.error(f"准备提交数据时出错: {e}")
            import traceback
            logger.error(traceback.format_exc())
            raise

    # UI方法
    def show_error(self, message):
        """显示错误信息"""
        logger.error(message)
        try:
            MDSnackbar(
                MDSnackbarText(
                    text=str(message),
                ),
                y=dp(24),
                pos_hint={"center_x": 0.5},
                duration=3,
                md_bg_color=self._get_theme_color("ERROR_COLOR", [1, 0.7, 0.7, 1]),  # 使用安全访问
            ).open()
        except Exception as e:
            logger.error(f"显示错误信息失败: {e}")

    def show_success(self, message):
        """显示成功信息"""
        try:
            MDSnackbar(
                MDSnackbarText(
                    text=message,
                ),
                y=dp(24),
                pos_hint={"center_x": 0.5},
                duration=2,
                md_bg_color=self._get_theme_color("SUCCESS_COLOR", [0.7, 1, 0.7, 1]),  # 使用安全访问
            ).open()
        except Exception as e:
            logger.error(f"显示成功信息失败: {e}")

    def show_info(self, message):
        """显示信息提示"""
        try:
            MDSnackbar(
                MDSnackbarText(
                    text=message,
                ),
                y=dp(24),
                pos_hint={"center_x": 0.5},
                duration=2,
                md_bg_color=self._get_theme_color("INFO_COLOR", [0.7, 0.7, 1, 1]),  # 使用安全访问
            ).open()
        except Exception as e:
            logger.error(f"显示信息提示失败: {e}")

    def go_back(self, *args):
        """返回上一页"""
        app = MDApp.get_running_app()
        if app and hasattr(app, 'screen_manager') and app.screen_manager:
            # 使用screen_manager而不是root，因为root是MDBoxLayout
            app.screen_manager.transition.direction = 'right'
            app.screen_manager.current = 'survey_screen'
        elif app and hasattr(app, 'root') and hasattr(app.root, 'children') and app.root.children:
            # 如果screen_manager不可用，尝试从主布局中找到屏幕管理器
            for child in app.root.children:
                if hasattr(child, 'transition') and hasattr(child, 'current'):
                    child.transition.direction = 'right'
                    child.current = 'survey_screen'
                    break


# 加载KV字符串
from kivy.lang import Builder
Builder.load_string(FORM_SCREEN_KV)
Factory.register('FormScreen', cls=FormScreen)