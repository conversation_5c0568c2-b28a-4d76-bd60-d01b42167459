"""标准量表和问卷初始化模块

此模块负责将标准量表和问卷直接加载到数据库中，使其可以直接在管理界面中显示，
而不是作为模板使用。只有由自动生成器生成的量表和问卷才使用模板。
"""

import logging
import sys
import os
from datetime import datetime
from sqlalchemy.exc import SQLAlchemyError

# 创建日志记录器
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

# 添加项目根目录到 Python 路径
sys.path.append(os.path.dirname(os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))))

# 导入数据库连接
try:
    from app.core.db_connection import db_connection
except ImportError:
    try:
        from backend.app.core.db_connection import db_connection
    except ImportError:
        logger.error("无法导入 db_connection 模块，请确保该模块存在")
        raise

# 导入数据库模型
try:
    from app.models.assessment import Assessment, AssessmentItem
    from app.models.questionnaire import Questionnaire, QuestionnaireItem
    from app.models.enums import AssessmentType, QuestionnaireType
    logger.info("所有模型导入成功")
except ImportError as e:
    logger.error(f"导入模型时出错: {str(e)}")
    raise

# 导入标准量表和问卷
from app.clinical_scales.assessment import ALL_ASSESSMENT_TEMPLATES as STANDARD_ASSESSMENT_TEMPLATES
from app.clinical_scales.questionnaire import ALL_QUESTIONNAIRE_TEMPLATES as STANDARD_QUESTIONNAIRE_TEMPLATES

def init_standard_scales():
    """初始化标准量表和问卷"""
    try:
        # 创建会话
        db = db_connection.get_session()
        if not db:
            logger.error("无法获取数据库会话")
            return

        try:
            # 初始化标准量表
            init_standard_assessments(db)
            # 初始化标准问卷
            init_standard_questionnaires(db)
            
            logger.info("标准量表和问卷初始化完成")
        finally:
            # 关闭会话
            db.close()
    except Exception as e:
        logger.error(f"初始化标准量表和问卷出错: {e}")

def init_standard_assessments(db):
    """初始化标准量表"""
    try:
        # 检查是否已经初始化过标准量表
        existing_assessments = db.query(Assessment).filter(Assessment.custom_id == 1).all()
        existing_names = [a.title for a in existing_assessments]
        
        # 遍历标准量表模板
        for template in STANDARD_ASSESSMENT_TEMPLATES.values():
            # 如果已经存在同名量表，则跳过
            if template["name"] in existing_names:
                logger.info(f"标准量表 '{template['name']}' 已存在，跳过")
                continue
                
            # 创建量表
            assessment = Assessment(
                custom_id=1,  # 系统用户ID
                assessment_type=template["assessment_type"],
                name=template["name"],
                version=template.get("version", "标准版"),
                score=None,
                max_score=template.get("max_score"),
                result=None,
                conclusion=None,
                notes=template.get("description"),
                status="pending_review",  # 设置为待审核状态
                created_at=datetime.now()
            )
            db.add(assessment)
            db.flush()  # 获取ID
            
            # 创建量表项目
            for question in template["questions"]:
                item = AssessmentItem(
                    assessment_id=assessment.id,
                    question_id=question.get("question_id", f"q_{assessment.id}_{question.get('order', 0)}"),
                    question_text=question["question_text"],
                    answer=None,
                    score=None,
                    notes=None,
                    created_at=datetime.now()
                )
                db.add(item)
            
            logger.info(f"标准量表 '{template['name']}' 创建成功")
        
        db.commit()
        logger.info("标准量表初始化完成")
    except SQLAlchemyError as e:
        db.rollback()
        logger.error(f"初始化标准量表时出错: {str(e)}")

def init_standard_questionnaires(db):
    """初始化标准问卷"""
    try:
        # 检查是否已经初始化过标准问卷 - 使用custom_id代替user_id
        try:
            # 使用直接的SQL查询，只选择需要的列，避免提及user_id
            from sqlalchemy import text
            result = db.execute(text("SELECT id, name FROM questionnaires WHERE custom_id = '1'"))
            existing_names = [row[1] for row in result]
        except SQLAlchemyError as e:
            logger.warning(f"查询问卷时出错，使用备用方法: {str(e)}")
            # 备用方法：空列表
            existing_names = []
        
        # 遍历标准问卷模板
        for template in STANDARD_QUESTIONNAIRE_TEMPLATES.values():
            # 如果已经存在同名问卷，则跳过
            if template["name"] in existing_names:
                logger.info(f"标准问卷 '{template['name']}' 已存在，跳过")
                continue
                
            # 创建问卷 - 使用明确的列名插入，完全不涉及user_id字段
            from sqlalchemy import text
            
            # 构建插入语句，明确指定所有列名
            insert_query = text("""
                INSERT INTO questionnaires 
                (custom_id, questionnaire_type, name, version, notes, status, revision_reason, category, created_at, updated_at) 
                VALUES (:custom_id, :questionnaire_type, :name, :version, :notes, :status, :revision_reason, :category, :created_at, :updated_at)
                RETURNING id
            """)
            
            result = db.execute(
                insert_query, 
                {
                    "custom_id": "1",  # 系统用户ID，使用字符串类型
                    "questionnaire_type": str(template["questionnaire_type"]),
                    "name": template["name"],
                    "version": template.get("version", "标准版"),
                    "notes": template.get("description"),
                    "status": "pending_review",  # 设置为待审核状态
                    "revision_reason": None,
                    "category": template.get("category"),
                    "created_at": datetime.now(),
                    "updated_at": datetime.now()
                }
            )
            
            # 获取新问卷的ID
            questionnaire_id = result.fetchone()[0]
            
            # 创建问卷项目
            for question in template["questions"]:
                item = QuestionnaireItem(
                    questionnaire_id=questionnaire_id,
                    question_id=question.get("question_id", f"q_{questionnaire_id}_{question.get('order', 0)}"),
                    question_text=question["question_text"],
                    question_type=question.get("question_type", "single_choice"),
                    options=question.get("options"),
                    is_required=question.get("is_required", True),
                    order=question.get("order", 0),
                    created_at=datetime.now()
                )
                db.add(item)
            
            logger.info(f"标准问卷 '{template['name']}' 创建成功")
        
        db.commit()
        logger.info("标准问卷初始化完成")
    except SQLAlchemyError as e:
        db.rollback()
        logger.error(f"初始化标准问卷时出错: {str(e)}")

if __name__ == "__main__":
    logging.basicConfig(level=logging.INFO)
    init_standard_scales()