"""
评估量表API
"""
from typing import List, Optional, Dict, Any
from fastapi import APIRouter, Depends, HTTPException, Query, Path, Request, Body, status as http_status
from sqlalchemy.orm import Session
from datetime import datetime
from pydantic import BaseModel

from app.api.deps import get_db
from app.core.auth import get_current_active_user_custom
from app.models.user import User
from app.models.assessment import Assessment, AssessmentItem, AssessmentResponse
from app.models.assessment import AssessmentTemplate, AssessmentTemplateQuestion
# from app.utils.field_compatibility import ensure_field_compatibility, ensure_list_field_compatibility
# 注释：field_compatibility模块已删除，统一数据库结构后不再需要字段映射

router = APIRouter()

# 添加模板相关函数
def get_assessment_templates(db: Session) -> List[Dict[str, Any]]:
    """获取评估量表模板列表"""
    templates = db.query(AssessmentTemplate).filter(AssessmentTemplate.is_active == True).all()
    result = []
    for template in templates:
        # 获取模板题目
        questions = db.query(AssessmentTemplateQuestion).filter(
            AssessmentTemplateQuestion.template_id == template.id
        ).order_by(AssessmentTemplateQuestion.order).all()
        
        # 转换为API格式
        result.append({
            "id": template.id,
            "template_key": getattr(template, 'template_key', None),
            "name": template.name,
            "name_en": getattr(template, 'name_en', ''),
            "description": template.description,
            "instructions": template.instructions,
            "category": getattr(template, 'category', ''),
            "type": getattr(template, 'type', 'assessment'),
            "status": getattr(template, 'status', 'pending'),
            "version": template.version,
            "scoring_method": template.scoring_method,
            "max_score": template.max_score,
            "result_ranges": template.result_ranges,
            "questions": [
                {
                    "id": q.id,
                    "question_id": q.question_id,
                    "question_text": q.question_text,
                    "question_type": q.question_type,
                    "options": q.options,
                    "scoring": getattr(q, 'scoring', None),
                    "order": q.order,
                    "is_required": q.is_required
                } for q in questions
            ],
            "question_count": len(questions)
        })
    return result

def get_user_assessments(db: Session, custom_id: str) -> List[Dict[str, Any]]:
    """获取用户的评估量表列表"""
    assessments = db.query(Assessment).filter(Assessment.custom_id == custom_id).all()
    result = []
    for assessment in assessments:
        # 获取题目
        items = db.query(AssessmentItem).filter(AssessmentItem.assessment_id == assessment.id).all()
        # 获取模板信息
        template = assessment.template
        template_dict = None
        if template:
            template_questions = db.query(AssessmentTemplateQuestion).filter(
                AssessmentTemplateQuestion.template_id == template.id
            ).order_by(AssessmentTemplateQuestion.order).all()
            template_dict = {
                "id": template.id,
                "name": template.name,
                "assessment_type": template.assessment_type.name if hasattr(template.assessment_type, 'name') else str(template.assessment_type),
                "sub_type": template.sub_type,
                "version": template.version,
                "description": template.description,
                "instructions": template.instructions,
                "scoring_method": template.scoring_method,
                "max_score": template.max_score,
                "result_ranges": template.result_ranges,
                "questions": [
                    {
                        "id": q.id,
                        "question_id": q.question_id,
                        "question_text": q.question_text,
                        "question_type": q.question_type,
                        "options": q.options,
                        "scoring": q.scoring,
                        "order": q.order,
                        "is_required": q.is_required
                    } for q in template_questions
                ],
                "question_count": len(template_questions)
            }
        result.append({
            "id": assessment.id,
            "title": assessment.title,
            "description": assessment.notes,
            "assessment_type": assessment.assessment_type.name if hasattr(assessment.assessment_type, 'name') else str(assessment.assessment_type),
            "version": assessment.version,
            "created_at": assessment.created_at.isoformat() if assessment.created_at else None,
            "updated_at": assessment.updated_at.isoformat() if assessment.updated_at else None,
            "status": assessment.status,
            "score": assessment.score,
            "max_score": assessment.max_score,
            "questions": [
                {
                    "question_id": item.question_id,
                    "question_text": item.question_text,
                    "answer": item.answer,
                    "score": item.score
                } for item in items
            ],
            "template": template_dict
        })
    return result

class AssessmentQuestion(BaseModel):
    question_id: str
    question_text: str
    question_type: Optional[str] = None
    options: Optional[List[Dict[str, Any]]] = None
    order: Optional[int] = None
    is_required: Optional[bool] = True
    jump_logic: Optional[Dict[str, Any]] = None  # 题目逻辑跳转

class AssessmentCreate(BaseModel):
    assessment_type: str
    title: str
    version: Optional[str] = ""
    description: Optional[str] = ""
    max_score: Optional[float] = 0
    template_id: Optional[int] = None  # 添加template_id字段
    questions: List[AssessmentQuestion]

@router.get("", response_model=dict)
def get_assessments(
    request: Request,
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_active_user_custom),
    page: int = Query(1, ge=1),
    page_size: int = Query(10, ge=1, le=100),
):
    """
    获取量表列表（系统预置+当前用户自定义）
    """
    try:
        # 查询包括标准量表和用户自定义量表
        # custom_id=1 表示系统预置的标准量表，current_user.custom_id 表示当前用户创建的量表
        query = db.query(Assessment).filter(
            Assessment.custom_id.in_([1, getattr(current_user, 'custom_id', 0)])
        )
        total = query.count()
        # 支持 page_size=-1 返回全部量表
        if page_size == -1:
            assessments = query.order_by(Assessment.created_at.desc()).all()
        else:
            assessments = query.order_by(Assessment.created_at.desc()) \
                .offset((page - 1) * page_size).limit(page_size).all()

        result = []
        for a in assessments:
            # 分类字段（如有）
            category = getattr(a, 'category', None) if hasattr(a, 'category') else None
            # 问题数量
            question_count = len(a.items) if hasattr(a, 'items') else 0
            # 回复数量
            response_count = db.query(AssessmentResponse).filter(AssessmentResponse.assessment_id == a.id).count()
            result.append({
                "id": a.id,
                "title": a.title,
                "description": a.notes,
                "assessment_type": a.assessment_type.name if hasattr(a.assessment_type, 'name') else str(a.assessment_type),
                "version": a.version,
                "created_at": a.created_at.isoformat() if a.created_at else None,
                "updated_at": a.updated_at.isoformat() if a.updated_at else None,
                "is_system": a.custom_id == 1,
                "status": a.status,
                "category": category,
                "question_count": question_count,
                "response_count": response_count,
            })
        
        return {
            "status": "success",
            "data": result,
            "total": total
        }
    except Exception as e:
        print(f"获取量表列表出错: {str(e)}")
        return {
            "status": "error",
            "message": f"获取量表列表出错: {str(e)}",
            "data": []
        }

@router.get("/{assessment_id}", response_model=dict)
def get_assessment(
    assessment_id: int,
    request: Request,
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_active_user_custom),
):
    """
    获取评估量表详情
    """
    assessment = db.query(Assessment).filter(Assessment.id == assessment_id).first()
    if not assessment:
        raise HTTPException(status_code=http_status.HTTP_404_NOT_FOUND, detail="量表不存在")
    
    questions = []
    
    # 首先尝试从 AssessmentTemplate 获取问题（优先级最高）
    if assessment.template_id:
        template_questions = db.query(AssessmentTemplateQuestion).filter(
            AssessmentTemplateQuestion.template_id == assessment.template_id
        ).order_by(AssessmentTemplateQuestion.order).all()
        
        if template_questions:
            questions = [
                {
                    "question_id": item.question_id,
                    "question_text": item.question_text,
                    "question_type": item.question_type,
                    "options": item.options,
                    "order": item.order,
                    "is_required": item.is_required if item.is_required is not None else True,
                    "jump_logic": item.jump_logic,
                } for item in template_questions
            ]
            print(f"[评估详情] 从模板获取到 {len(questions)} 个问题")
    
    # 如果模板中没有问题，尝试从 AssessmentItem 获取（兼容性）
    if not questions:
        items = db.query(AssessmentItem).filter(AssessmentItem.assessment_id == assessment.id).all()
        questions = [
            {
                "question_id": item.question_id,
                "question_text": item.question_text,
                "question_type": getattr(item, 'question_type', None),
                "options": getattr(item, 'options', None),
                "order": getattr(item, 'order', None),
                "is_required": getattr(item, 'is_required', True),
                "jump_logic": getattr(item, 'jump_logic', None),
            } for item in items
        ]
        print(f"[评估详情] 从 AssessmentItem 获取到 {len(questions)} 个问题")
    
    return {
        "status": "success",
        "data": {
            "id": assessment.id,
            "title": assessment.title,
            "description": assessment.notes,
            "assessment_type": assessment.assessment_type.name if hasattr(assessment.assessment_type, 'name') else str(assessment.assessment_type),
            "version": assessment.version,
            "created_at": assessment.created_at.isoformat() if assessment.created_at else None,
            "updated_at": assessment.updated_at.isoformat() if assessment.updated_at else None,
            "is_system": assessment.custom_id == 1,
            "status": assessment.status,
            "questions": questions
        }
    }

@router.get("/{assessment_id}/questions", response_model=dict)
def get_assessment_questions(
    assessment_id: int,
    request: Request,
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_active_user_custom),
):
    """
    获取评估量表的问题列表
    """
    assessment = db.query(Assessment).filter(Assessment.id == assessment_id).first()
    if not assessment:
        raise HTTPException(status_code=http_status.HTTP_404_NOT_FOUND, detail="量表不存在")
    
    questions = []
    
    # 首先尝试从 AssessmentTemplate 获取问题（优先级最高）
    if assessment.template_id:
        template_questions = db.query(AssessmentTemplateQuestion).filter(
            AssessmentTemplateQuestion.template_id == assessment.template_id
        ).order_by(AssessmentTemplateQuestion.order).all()
        
        if template_questions:
            questions = [
                {
                    "id": item.id,
                    "question_id": item.question_id,
                    "text": item.question_text,
                    "question_text": item.question_text,
                    "type": item.question_type or 'radio',
                    "question_type": item.question_type or 'radio',
                    "options": item.options or [
                        {'value': '1', 'text': '没有或很少时间'},
                        {'value': '2', 'text': '小部分时间'},
                        {'value': '3', 'text': '相当多时间'},
                        {'value': '4', 'text': '绝大部分或全部时间'}
                    ],
                    "order": item.order,
                    "is_required": item.is_required if item.is_required is not None else True,
                    "jump_logic": item.jump_logic,
                } for item in template_questions
            ]
            print(f"[评估问题获取] 从模板获取到 {len(questions)} 个问题")
    
    # 如果模板中没有问题，尝试从 AssessmentItem 获取（兼容性）
    if not questions:
        items = db.query(AssessmentItem).filter(AssessmentItem.assessment_id == assessment.id).all()
        if items:
            questions = [
                {
                    "id": item.id,
                    "question_id": item.question_id,
                    "text": item.question_text,
                    "question_text": item.question_text,
                    "type": getattr(item, 'question_type', 'radio'),
                    "question_type": getattr(item, 'question_type', 'radio'),
                    "options": getattr(item, 'options', [
                        {'value': '1', 'text': '没有或很少时间'},
                        {'value': '2', 'text': '小部分时间'},
                        {'value': '3', 'text': '相当多时间'},
                        {'value': '4', 'text': '绝大部分或全部时间'}
                    ]),
                    "order": getattr(item, 'order', None),
                    "is_required": getattr(item, 'is_required', True),
                    "jump_logic": getattr(item, 'jump_logic', None),
                } for item in items
            ]
            print(f"[评估问题获取] 从 AssessmentItem 获取到 {len(questions)} 个问题")
    
    print(f"[评估问题获取] 最终返回 {len(questions)} 个问题")
    
    return {
        "status": "success",
        "questions": questions
    }

@router.post("", response_model=dict)
def create_assessment(
    data: AssessmentCreate,
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_active_user_custom),
):
    """
    创建评估量表（支持自定义字段，写入正式表）
    """
    assessment = Assessment(
        custom_id=getattr(current_user, 'custom_id', 1),
        assessment_type=data.assessment_type,
        name=data.title,
        version=data.version,
        notes=data.description,
        max_score=data.max_score,
        template_id=data.template_id,  # 添加template_id字段
    )
    db.add(assessment)
    db.flush()
    for q in data.questions:
        item = AssessmentItem(
            assessment_id=assessment.id,
            question_id=q.question_id,
            question_text=q.question_text,
            answer='',
            score=None,
            notes='',
            question_type=getattr(q, 'question_type', None),
            options=getattr(q, 'options', None),
            order_num=getattr(q, 'order', None),
            is_required=getattr(q, 'is_required', True),
            jump_logic=getattr(q, 'jump_logic', None),
        )
        db.add(item)
    db.commit()
    db.refresh(assessment)
    return {
        "status": "success",
        "message": "评估量表创建成功",
        "data": {
            "id": assessment.id,
            "title": assessment.title,
            "description": assessment.notes,
            "questions": [q.dict() for q in data.questions]
        }
    }

@router.put("/{assessment_id}", response_model=dict)
def update_assessment(
    assessment_id: int,
    request: Request,
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_active_user_custom),
):
    """
    更新评估量表（可根据需要完善）
    """
    # 这里只做演示，实际应实现字段更新和题目同步
    return {
        "status": "success",
        "message": "评估量表更新成功",
        "data": {
            "id": assessment_id,
            "title": "更新后的评估量表",
            "description": "更新后的评估量表描述",
            "questions": []
        }
    }

@router.delete("/{assessment_id}", response_model=dict)
def delete_assessment(
    assessment_id: int,
    request: Request,
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_active_user_custom),
):
    """
    删除量表（系统预置量表禁止删除）
    """
    assessment = db.query(Assessment).filter(Assessment.id == assessment_id).first()
    if not assessment:
        raise HTTPException(status_code=http_status.HTTP_404_NOT_FOUND, detail="量表不存在")
    if assessment.custom_id == 1:
        raise HTTPException(status_code=http_status.HTTP_403_FORBIDDEN, detail="系统预置量表禁止删除")
    db.delete(assessment)
    db.commit()
    return {
        "status": "success",
        "message": "量表删除成功"
    }

class ReviewRequest(BaseModel):
    status: Optional[str] = "published"
    revision_reason: Optional[str] = None

@router.post("/{assessment_id}/review", response_model=dict)
def review_assessment(
    assessment_id: int,
    review_data: Optional[ReviewRequest] = None,
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_active_user_custom),
):
    """
    审核量表（仅管理员可操作）
    审核通过后状态变更为已发布(published)
    返修时状态变更为草稿(draft)
    """
    if getattr(current_user, 'role', '') not in ["admin", "super_admin", "consultant"]:
        raise HTTPException(status_code=http_status.HTTP_403_FORBIDDEN, detail="无权限审核")
    assessment = db.query(Assessment).filter(Assessment.id == assessment_id).first()
    if not assessment:
        raise HTTPException(status_code=http_status.HTTP_404_NOT_FOUND, detail="量表不存在")
    
    # 默认为审核通过
    status = "published"
    message = "审核通过，量表已发布"
    
    # 如果提供了审核数据，则使用提供的状态
    if review_data:
        status = review_data.status
        if status == "draft":
            message = "已退回返修"
            # 如果有返修原因，可以记录到数据库中
            if review_data.revision_reason:
                assessment.revision_reason = review_data.revision_reason
    
    assessment.status = status
    db.commit()
    return {"status": "success", "message": message}

@router.post("/{assessment_id}/push", response_model=dict)
def push_assessment(
    assessment_id: int,
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_active_user_custom),
):
    """
    推送量表（仅管理员可操作）
    """
    if getattr(current_user, 'role', '') not in ["admin", "super_admin", "consultant"]:
        raise HTTPException(status_code=http_status.HTTP_403_FORBIDDEN, detail="无权限推送")
    assessment = db.query(Assessment).filter(Assessment.id == assessment_id).first()
    if not assessment:
        raise HTTPException(status_code=http_status.HTTP_404_NOT_FOUND, detail="量表不存在")
    assessment.status = 'pushed'
    db.commit()
    return {"status": "success", "message": "推送成功"}

@router.post("/{assessment_id}/import-questions", response_model=dict)
def import_assessment_questions(
    assessment_id: int,
    questions: List[AssessmentQuestion],
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_active_user_custom),
):
    """
    批量导入题目（仅管理员可操作）
    """
    if getattr(current_user, 'role', '') not in ["admin", "super_admin", "consultant"]:
        raise HTTPException(status_code=http_status.HTTP_403_FORBIDDEN, detail="无权限批量导入题目")
    assessment = db.query(Assessment).filter(Assessment.id == assessment_id).first()
    if not assessment:
        raise HTTPException(status_code=http_status.HTTP_404_NOT_FOUND, detail="量表不存在")
    # 先删除原有题目
    db.query(AssessmentItem).filter(AssessmentItem.assessment_id == assessment_id).delete()
    # 批量插入新题目
    for q in questions:
        item = AssessmentItem(
            assessment_id=assessment_id,
            question_id=q.question_id,
            question_text=q.question_text,
            answer='',
            score=None,
            notes='',
            question_type=getattr(q, 'question_type', None),
            options=getattr(q, 'options', None),
            order_num=getattr(q, 'order', None),
            is_required=getattr(q, 'is_required', True),
            jump_logic=getattr(q, 'jump_logic', None),
        )
        db.add(item)
    db.commit()
    return {"status": "success", "message": "批量导入题目成功"}

@router.get("/user/{custom_id}", response_model=Dict[str, Any])
def read_user_assessments(
    *,
    db: Session = Depends(get_db),
    custom_id: str = Path(..., description="用户ID"),
    assessment_type: Optional[str] = Query(None, description="量表类型"),
    start_date: Optional[datetime] = Query(None, description="开始日期"),
    end_date: Optional[datetime] = Query(None, description="结束日期"),
    skip: int = Query(0, ge=0, description="跳过记录数"),
    limit: int = Query(100, ge=1, le=1000, description="返回记录数"),
    current_user: User = Depends(get_current_active_user_custom)
) -> Any:
    """
    获取指定用户的评估量表记录列表
    """
    print(f"assessments.py - 获取用户评估量表记录 - 用户ID: {custom_id}")

    # 支持数字ID和自定义ID查找用户
    user = None
    if custom_id.isdigit():
        user = db.query(User).filter(User.id == int(custom_id)).first()
    else:
        user = db.query(User).filter(User.custom_id == custom_id).first()
    
    if not user:
        print(f"用户{custom_id}不存在，返回404异常")
        raise HTTPException(status_code=http_status.HTTP_404_NOT_FOUND, detail="用户不存在")
    
    try:
        # 构建查询
        query = db.query(Assessment).filter(Assessment.custom_id == user.custom_id)
        
        # 应用筛选条件
        if assessment_type:
            # 验证assessment_type是否为有效的枚举值
            try:
                from app.models.enums import AssessmentType
                valid_types = [e.value for e in AssessmentType]
                if assessment_type in valid_types:
                    # 将字符串转换为枚举对象
                    enum_type = AssessmentType(assessment_type)
                    query = query.filter(Assessment.assessment_type == enum_type)
                else:
                    print(f"assessment_type非法或未定义: {assessment_type}, 有效类型为: {valid_types}，返回空结果")
                    return {"status": "success", "total": 0, "items": [], "message": f"无效的assessment_type: {assessment_type}，有效类型为: {valid_types}"}
            except Exception as e:
                print(f"assessment_type枚举校验异常: {e}, 返回空结果")
                return {"status": "success", "total": 0, "items": [], "message": f"assessment_type参数异常: {str(e)}"}
        
        # 确保start_date是datetime对象
        if start_date:
            if isinstance(start_date, str):
                try:
                    start_date = datetime.fromisoformat(start_date.replace('Z', '+00:00'))
                except Exception:
                    try:
                        start_date = datetime.strptime(start_date, '%Y-%m-%d')
                    except Exception:
                        start_date = None
            if isinstance(start_date, datetime):
                query = query.filter(Assessment.created_at >= start_date)
        if end_date:
            # 确保end_date是datetime对象
            if isinstance(end_date, str):
                try:
                    end_date = datetime.fromisoformat(end_date.replace('Z', '+00:00'))
                except Exception:
                    try:
                        end_date = datetime.strptime(end_date, '%Y-%m-%d')
                    except Exception:
                        end_date = None
            if isinstance(end_date, datetime):
                query = query.filter(Assessment.created_at <= end_date)
        
        # 获取总数
        total = query.count()
        
        # 分页
        items = query.order_by(Assessment.created_at.desc()).offset(skip).limit(limit).all()
        
        # 格式化结果
        result = []
        for item in items:
            result.append({
                "id": item.id,
                "title": item.name,
                "description": item.notes,
                "assessment_type": item.assessment_type.name if hasattr(item.assessment_type, 'name') else str(item.assessment_type),
                "version": item.version,
                "created_at": item.created_at.isoformat() if item.created_at else None,
                "updated_at": item.updated_at.isoformat() if item.updated_at else None,
                "status": item.status,
            })
        
        return {
            "status": "success",
            "total": total,
            "items": result
        }
    except Exception as e:
        print(f"获取用户评估量表记录时出错: {str(e)}")
        # 返回空结果而不是抛出异常
        return {
            "status": "success",
            "total": 0,
            "items": []
        }

@router.delete("/{assessment_id}/questions", response_model=dict)
def delete_all_assessment_questions(
    assessment_id: int,
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_active_user_custom),
):
    """
    批量删除量表题目（仅管理员可操作）
    """
    if getattr(current_user, 'role', '') not in ["admin", "super_admin", "consultant"]:
        raise HTTPException(status_code=http_status.HTTP_403_FORBIDDEN, detail="无权限批量删除题目")
    assessment = db.query(Assessment).filter(Assessment.id == assessment_id).first()
    if not assessment:
        raise HTTPException(status_code=http_status.HTTP_404_NOT_FOUND, detail="量表不存在")
    db.query(AssessmentItem).filter(AssessmentItem.assessment_id == assessment_id).delete()
    db.commit()
    return {"status": "success", "message": "所有题目已删除"}

@router.post("/{assessment_id}/distribute", response_model=dict)
async def distribute_assessment(
    assessment_id: int,
    distributionData: dict = Body(...),
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_active_user_custom),
):
    """
    分发量表 - 支持按角色分发和指定用户分发
    """
    # 检查量表是否存在
    assessment = db.query(Assessment).filter(Assessment.id == assessment_id).first()
    if not assessment:
        raise HTTPException(status_code=http_status.HTTP_404_NOT_FOUND, detail="量表不存在")
    
    # 获取分发类型
    distribution_type = distributionData.get("distribution_type")
    if not distribution_type:
        raise HTTPException(status_code=http_status.HTTP_400_BAD_REQUEST, detail="分发类型不能为空")
    
    # 根据分发类型处理
    try:
        from app.models.distribution import AssessmentDistribution
        from datetime import datetime, timedelta
        
        # 截止日期处理
        due_date = None
        if "due_date" in distributionData and distributionData["due_date"]:
            try:
                due_date = datetime.fromisoformat(distributionData["due_date"].replace("Z", "+00:00"))
            except Exception as e:
                print(f"处理截止日期时出错: {str(e)}")
                due_date = datetime.now() + timedelta(days=7)  # 默认7天后
        
        # 分发给所有用户
        if distribution_type == "all":
            # 获取所有激活用户
            users = db.query(User).filter(User.is_active == True).all()
            for user in users:
                # 创建分发记录
                distribution = AssessmentDistribution(
                    assessment_id=assessment_id,
                    custom_id=user.custom_id,
                    distributor_custom_id=current_user.custom_id,
                    status="pending",
                    due_date=due_date,
                    message=distributionData.get("message", "")
                )
                db.add(distribution)
        
        # 分发给特定角色
        elif distribution_type == "role":
            roles = distributionData.get("roles", [])
            if not roles:
                raise HTTPException(status_code=http_status.HTTP_400_BAD_REQUEST, detail="角色列表不能为空")
            
            # 获取指定角色的用户
            users = db.query(User).filter(User.is_active == True).all()
            target_users = []
            
            for user in users:
                user_role = user.role
                if isinstance(user_role, str):
                    user_role_str = user_role.lower()
                elif hasattr(user_role, 'name'):
                    user_role_str = user_role.name.lower()
                elif hasattr(user_role, 'value'):
                    user_role_str = user_role.value.lower()
                else:
                    user_role_str = str(user_role).lower()
                
                # 检查用户角色是否在目标角色列表中
                if any(role.lower() == user_role_str for role in roles):
                    target_users.append(user)
            
            # 创建分发记录
            for user in target_users:
                distribution = AssessmentDistribution(
                    assessment_id=assessment_id,
                    custom_id=user.custom_id,
                    distributor_custom_id=current_user.custom_id,
                    status="pending",
                    due_date=due_date,
                    message=distributionData.get("message", "")
                )
                db.add(distribution)
        
        # 分发给特定用户
        elif distribution_type == "specific":
            custom_ids = distributionData.get("custom_ids", [])
            if not custom_ids:
                raise HTTPException(status_code=http_status.HTTP_400_BAD_REQUEST, detail="用户ID列表不能为空")
            
            # 处理用户ID
            for user_id in custom_ids:
                # 尝试查找用户
                user = None
                if isinstance(user_id, int):
                    user = db.query(User).filter(User.id == user_id).first()
                else:
                    # 可能是custom_id
                    user = db.query(User).filter(User.custom_id == user_id).first()
                    if not user and str(user_id).isdigit():
                        # 尝试将字符串解析为int
                        user = db.query(User).filter(User.id == int(user_id)).first()
                
                if user:
                    # 创建分发记录
                    distribution = AssessmentDistribution(
                        assessment_id=assessment_id,
                        custom_id=user.custom_id,
                        distributor_custom_id=current_user.custom_id,
                        status="pending",
                        due_date=due_date,
                        message=distributionData.get("message", "")
                    )
                    db.add(distribution)
        
        # 不支持的分发类型
        else:
            raise HTTPException(
                status_code=http_status.HTTP_400_BAD_REQUEST, 
                detail=f"不支持的分发类型: {distribution_type}"
            )
        
        # 提交事务
        db.commit()
        
        return {"status": "success", "message": "量表分发成功"}
    
    except HTTPException as he:
        # 重新抛出HTTP异常
        raise he
    except Exception as e:
        # 记录日志并回滚事务
        print(f"分发量表时出错: {str(e)}")
        import traceback
        traceback.print_exc()
        db.rollback()
        raise HTTPException(
            status_code=http_status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"分发量表失败: {str(e)}"
        )
