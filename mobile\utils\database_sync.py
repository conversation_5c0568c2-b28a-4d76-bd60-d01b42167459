# -*- coding: utf-8 -*-
"""
数据库同步脚本
修复移动端数据库编码问题并与后端数据库同步
"""

import sqlite3
import os
import logging
from pathlib import Path
from typing import Dict, List, Any, Optional

# 设置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

class DatabaseSync:
    """数据库同步管理器"""
    
    def __init__(self):
        """初始化数据库同步管理器"""
        self.project_root = Path(__file__).parent.parent.parent
        self.mobile_db_path = self.project_root / "mobile" / "data" / "health_data.db"
        self.backend_db_path = self.project_root / "YUN" / "backend" / "app.db"
        
    def create_mobile_database_with_utf8(self):
        """创建新的移动端数据库，确保UTF-8编码"""
        try:
            # 确保目录存在
            self.mobile_db_path.parent.mkdir(parents=True, exist_ok=True)
            
            # 删除旧数据库
            if self.mobile_db_path.exists():
                self.mobile_db_path.unlink()
                logger.info("删除旧的移动端数据库")
            
            # 创建新数据库连接
            conn = sqlite3.connect(str(self.mobile_db_path))
            conn.execute("PRAGMA encoding = 'UTF-8'")
            
            # 创建assessment_distributions表
            conn.execute("""
                CREATE TABLE IF NOT EXISTS assessment_distributions (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    title TEXT NOT NULL,
                    description TEXT,
                    type TEXT,
                    questions_count INTEGER DEFAULT 0,
                    estimated_time TEXT,
                    status TEXT DEFAULT 'active',
                    created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
                    updated_at DATETIME DEFAULT CURRENT_TIMESTAMP
                )
            """)
            
            # 创建questionnaire_distributions表
            conn.execute("""
                CREATE TABLE IF NOT EXISTS questionnaire_distributions (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    title TEXT NOT NULL,
                    description TEXT,
                    category TEXT,
                    questions_count INTEGER DEFAULT 0,
                    estimated_time TEXT,
                    status TEXT DEFAULT 'active',
                    created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
                    updated_at DATETIME DEFAULT CURRENT_TIMESTAMP
                )
            """)
            
            # 创建assessment_results表
            conn.execute("""
                CREATE TABLE IF NOT EXISTS assessment_results (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    assessment_id INTEGER,
                    custom_id TEXT NOT NULL,
                    answers TEXT,
                    score REAL,
                    result TEXT,
                    completed_at DATETIME DEFAULT CURRENT_TIMESTAMP,
                    created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
                    updated_at DATETIME DEFAULT CURRENT_TIMESTAMP,
                    FOREIGN KEY (assessment_id) REFERENCES assessment_distributions(id)
                )
            """)
            
            # 创建questionnaire_results表
            conn.execute("""
                CREATE TABLE IF NOT EXISTS questionnaire_results (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    questionnaire_id INTEGER,
                    custom_id TEXT NOT NULL,
                    answers TEXT,
                    score REAL,
                    result TEXT,
                    completed_at DATETIME DEFAULT CURRENT_TIMESTAMP,
                    created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
                    updated_at DATETIME DEFAULT CURRENT_TIMESTAMP,
                    FOREIGN KEY (questionnaire_id) REFERENCES questionnaire_distributions(id)
                )
            """)
            
            # 创建索引
            conn.execute("CREATE INDEX IF NOT EXISTS idx_assessment_distributions_status ON assessment_distributions (status)")
            conn.execute("CREATE INDEX IF NOT EXISTS idx_questionnaire_distributions_status ON questionnaire_distributions (status)")
            conn.execute("CREATE INDEX IF NOT EXISTS idx_assessment_results_custom_id ON assessment_results (custom_id)")
            conn.execute("CREATE INDEX IF NOT EXISTS idx_questionnaire_results_custom_id ON questionnaire_results (custom_id)")
            
            conn.commit()
            conn.close()
            
            logger.info("成功创建新的移动端数据库")
            return True
            
        except Exception as e:
            logger.error(f"创建移动端数据库失败: {e}")
            return False
    
    def sync_data_from_backend(self):
        """从后端数据库同步数据到移动端"""
        try:
            # 连接后端数据库
            backend_conn = sqlite3.connect(str(self.backend_db_path))
            backend_conn.row_factory = sqlite3.Row
            
            # 连接移动端数据库
            mobile_conn = sqlite3.connect(str(self.mobile_db_path))
            mobile_conn.execute("PRAGMA encoding = 'UTF-8'")
            
            # 同步评估量表数据
            self._sync_assessments(backend_conn, mobile_conn)
            
            # 同步问卷数据
            self._sync_questionnaires(backend_conn, mobile_conn)
            
            backend_conn.close()
            mobile_conn.close()
            
            logger.info("数据同步完成")
            return True
            
        except Exception as e:
            logger.error(f"数据同步失败: {e}")
            return False
    
    def _sync_assessments(self, backend_conn: sqlite3.Connection, mobile_conn: sqlite3.Connection):
        """同步评估量表数据"""
        try:
            # 从后端获取评估量表数据
            backend_cursor = backend_conn.cursor()
            backend_cursor.execute("""
                SELECT a.id, a.name, a.assessment_type, 
                       COUNT(ai.id) as questions_count
                FROM assessments a
                LEFT JOIN assessment_items ai ON a.id = ai.assessment_id
                WHERE a.status = 'active'
                GROUP BY a.id, a.name, a.assessment_type
                ORDER BY a.created_at DESC
            """)
            
            assessments = backend_cursor.fetchall()
            
            # 插入到移动端数据库
            mobile_cursor = mobile_conn.cursor()
            
            for assessment in assessments:
                mobile_cursor.execute("""
                    INSERT OR REPLACE INTO assessment_distributions 
                    (id, title, description, type, questions_count, estimated_time, status)
                    VALUES (?, ?, ?, ?, ?, ?, ?)
                """, (
                    assessment['id'],
                    assessment['name'],
                    f"{assessment['name']} - {assessment['assessment_type']}评估",
                    assessment['assessment_type'],
                    assessment['questions_count'],
                    f"{max(5, assessment['questions_count'] // 2)}分钟",
                    'active'
                ))
            
            mobile_conn.commit()
            logger.info(f"同步了 {len(assessments)} 个评估量表")
            
        except Exception as e:
            logger.error(f"同步评估量表失败: {e}")
            raise
    
    def _sync_questionnaires(self, backend_conn: sqlite3.Connection, mobile_conn: sqlite3.Connection):
        """同步问卷数据"""
        try:
            # 从后端获取问卷数据
            backend_cursor = backend_conn.cursor()
            backend_cursor.execute("""
                SELECT q.id, q.title, q.description, q.category,
                       COUNT(qi.id) as questions_count
                FROM questionnaires q
                LEFT JOIN questionnaire_items qi ON q.id = qi.questionnaire_id
                WHERE q.status = 'active'
                GROUP BY q.id, q.title, q.description, q.category
                ORDER BY q.created_at DESC
            """)
            
            questionnaires = backend_cursor.fetchall()
            
            # 插入到移动端数据库
            mobile_cursor = mobile_conn.cursor()
            
            for questionnaire in questionnaires:
                mobile_cursor.execute("""
                    INSERT OR REPLACE INTO questionnaire_distributions 
                    (id, title, description, category, questions_count, estimated_time, status)
                    VALUES (?, ?, ?, ?, ?, ?, ?)
                """, (
                    questionnaire['id'],
                    questionnaire['title'],
                    questionnaire['description'] or f"{questionnaire['title']}调查问卷",
                    questionnaire['category'] or 'general',
                    questionnaire['questions_count'],
                    f"{max(5, questionnaire['questions_count'] // 2)}分钟",
                    'active'
                ))
            
            mobile_conn.commit()
            logger.info(f"同步了 {len(questionnaires)} 个问卷")
            
        except Exception as e:
            logger.error(f"同步问卷失败: {e}")
            raise
    
    def insert_sample_data(self):
        """插入示例数据用于测试"""
        try:
            conn = sqlite3.connect(str(self.mobile_db_path))
            conn.execute("PRAGMA encoding = 'UTF-8'")
            cursor = conn.cursor()
            
            # 插入评估量表示例数据
            assessment_data = [
                ('抑郁自评量表(SDS)', '评估抑郁症状的严重程度', 'self_assessment', 20, '10分钟', 'active'),
                ('焦虑自评量表(SAS)', '评估焦虑症状的严重程度', 'self_assessment', 20, '10分钟', 'active'),
                ('生活质量评估量表', '评估个人生活质量和幸福感', 'quality_of_life', 36, '15分钟', 'active'),
                ('蒙特利尔认知评估量表(MoCA)', '评估认知功能状态', 'cognitive_assessment', 30, '20分钟', 'active'),
                ('汉密尔顿抑郁量表(HAMD)', '专业抑郁症状评估工具', 'professional_assessment', 17, '15分钟', 'active')
            ]
            
            cursor.executemany("""
                INSERT OR REPLACE INTO assessment_distributions 
                (title, description, type, questions_count, estimated_time, status)
                VALUES (?, ?, ?, ?, ?, ?)
            """, assessment_data)
            
            # 插入问卷示例数据
            questionnaire_data = [
                ('健康生活方式调查', '了解您的日常生活习惯和健康行为', 'lifestyle', 15, '8分钟', 'active'),
                ('饮食习惯调查问卷', '评估您的饮食结构和营养摄入情况', 'nutrition', 12, '6分钟', 'active'),
                ('运动习惯调查', '了解您的运动频率和运动类型', 'exercise', 10, '5分钟', 'active'),
                ('睡眠质量调查', '评估您的睡眠模式和质量', 'sleep', 14, '7分钟', 'active'),
                ('压力管理调查', '了解您的压力来源和应对方式', 'stress', 18, '9分钟', 'active')
            ]
            
            cursor.executemany("""
                INSERT OR REPLACE INTO questionnaire_distributions 
                (title, description, category, questions_count, estimated_time, status)
                VALUES (?, ?, ?, ?, ?, ?)
            """, questionnaire_data)
            
            conn.commit()
            conn.close()
            
            logger.info("成功插入示例数据")
            return True
            
        except Exception as e:
            logger.error(f"插入示例数据失败: {e}")
            return False
    
    def verify_data(self):
        """验证数据是否正确插入"""
        try:
            conn = sqlite3.connect(str(self.mobile_db_path))
            conn.row_factory = sqlite3.Row
            cursor = conn.cursor()
            
            # 检查评估量表数据
            cursor.execute("SELECT id, title, description FROM assessment_distributions")
            assessments = cursor.fetchall()
            
            logger.info("评估量表数据:")
            for assessment in assessments:
                logger.info(f"  ID: {assessment['id']}, 标题: {assessment['title']}, 描述: {assessment['description']}")
            
            # 检查问卷数据
            cursor.execute("SELECT id, title, description FROM questionnaire_distributions")
            questionnaires = cursor.fetchall()
            
            logger.info("问卷数据:")
            for questionnaire in questionnaires:
                logger.info(f"  ID: {questionnaire['id']}, 标题: {questionnaire['title']}, 描述: {questionnaire['description']}")
            
            conn.close()
            return True
            
        except Exception as e:
            logger.error(f"验证数据失败: {e}")
            return False

def main():
    """主函数"""
    sync = DatabaseSync()
    
    logger.info("开始数据库同步和修复...")
    
    # 1. 创建新的移动端数据库
    if not sync.create_mobile_database_with_utf8():
        logger.error("创建数据库失败，退出")
        return False
    
    # 2. 插入示例数据（如果后端数据库不可用）
    if not sync.insert_sample_data():
        logger.error("插入示例数据失败")
        return False
    
    # 3. 验证数据
    if not sync.verify_data():
        logger.error("验证数据失败")
        return False
    
    logger.info("数据库同步和修复完成！")
    return True

if __name__ == "__main__":
    main()