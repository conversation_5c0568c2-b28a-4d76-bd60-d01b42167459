# -*- coding: utf-8 -*-
"""
Medication Management Screen (Refactored, compact)
--------------------------------------------------
• Focus: 目前用药 & 既往用药 两个Tab
• Cards:
    - 功能卡片（目前用药：修正/删除/提醒/停用/增加；既往用药：查询）
    - 药物卡片（current: 常规；history: 增加停药原因与停药日期）
• 目标：更优/更高效/更简洁，尽量消除重复与分散逻辑
• 兼容：无 services 也可运行（提供内存兜底服务）；有项目服务则优先使用
"""

from __future__ import annotations

import logging
from typing import Any, Dict, List, Optional, Callable
from datetime import datetime

from kivy.app import App
from kivy.clock import Clock
from kivy.metrics import dp
from kivy.properties import (
    StringProperty, ListProperty, ObjectProperty, BooleanProperty, NumericProperty
)
from kivy.utils import platform

from kivymd.uix.boxlayout import MDBoxLayout
from kivymd.uix.card import MDCard
from kivymd.uix.label import MDLabel
from kivymd.uix.button import MDButton, MDButtonText, MDIconButton
from kivymd.uix.scrollview import MDScrollView
from kivymd.uix.textfield import MDTextField
from kivymd.uix.selectioncontrol import MDCheckbox

# ------------------------------- Logging ----------------------------------
logger = logging.getLogger(__name__)
logger.setLevel(logging.INFO)


# ------------------------------- Theme safe --------------------------------
def _get_theme():
    """获取主题配置，兼容KivyMD 2.0.1 dev0规范"""
    try:
        from mobile.theme import AppTheme
        return AppTheme
    except ImportError:
        try:
            from theme import AppTheme
            return AppTheme
        except ImportError:
            # 如果无法导入主题配置，创建默认配置
            class _FallbackTheme:
                PRIMARY_COLOR = [0.133, 0.46, 0.82, 1]
                CARD_BACKGROUND = [1, 1, 1, 1]
                TEXT_PRIMARY = [0, 0, 0, 1]
                TEXT_SECONDARY = [0.45, 0.45, 0.45, 1]
                HEALTH_PURPLE = [0.612, 0.153, 0.69, 1]  # 药物管理主色调
                MEDICATION_COLOR = [0.612, 0.153, 0.69, 1]
                SUCCESS_COLOR = [0.298, 0.686, 0.314, 1]
                WARNING_COLOR = [1, 0.596, 0, 1]
                ERROR_COLOR = [0.957, 0.263, 0.212, 1]
                SURFACE_COLOR = [0.98, 0.98, 0.98, 1]
                BORDER_COLOR = [0.878, 0.878, 0.878, 1]
                
                # KivyMD 2.0.1 dev0 兼容属性
                @property
                def surface_container_low(self):
                    return [0.97, 0.97, 0.97, 1]
                    
                @property
                def on_surface(self):
                    return [0, 0, 0, 1]
                    
                @property
                def primary(self):
                    return self.PRIMARY_COLOR
                    
                @property
                def surface(self):
                    return [1, 1, 1, 1]
                    
            return _FallbackTheme()

THEME = _get_theme()


# --------------------------- Toast safe (fallback) -------------------------
def _toast(msg: str):
    try:
        from utils.toast import toast as md_toast
        md_toast(str(msg))
    except Exception:
        try:
            from kivymd.toast import toast as md_toast
            md_toast(str(msg))
        except Exception:
            print(f"[Toast] {msg}")


# --------------------------- BaseScreen safe import ------------------------
def _import_base_screen():
    # 尽最大可能兼容项目路径
    try:
        from screens.base_screen import BaseScreen
        return BaseScreen
    except Exception:
        try:
            from base_screen import BaseScreen  # 同目录兜底
            return BaseScreen
        except Exception:
            # 极限兜底：给一个极简BaseScreen，避免崩溃
            from kivymd.uix.screen import MDScreen
            class _Base(MDScreen):
                screen_title = StringProperty("")
                show_top_bar = BooleanProperty(True)
                def __init__(self, **kw):
                    super().__init__(**kw)
                    self.ids = {}
                    root = MDBoxLayout(orientation="vertical", spacing=dp(8), padding=[dp(12), dp(8)])
                    self.add_widget(root)
                    main = MDBoxLayout(orientation="vertical", adaptive_height=True, id="main_layout")
                    self.ids["main_layout"] = main
                    root.add_widget(main)
                def init_ui(self, dt=0):
                    return
                def on_action(self):
                    return
                def refresh_data(self):
                    return
            return _Base
BaseScreen = _import_base_screen()


# --------------------------- Dialogs integration ---------------------------
def _import_dialogs():
    """导入对话框函数，支持多种导入路径的兜底处理
    
    Returns:
        tuple: 包含所有对话框函数的元组
    """
    # 打开新增、编辑、提醒、停药、删除确认等对话框
    def _stub(*args, **kwargs):
        _toast("未找到 dialogs.py，使用占位回调")

    try:
        # 首先尝试从当前screens目录导入（相对导入）
        from .dialogs import (
            open_add_medication_dialog,
            open_edit_medication_dialog,
            open_reminder_dialog,
            open_stop_medication_dialog,
            open_delete_confirm_dialog,
        )
        return (open_add_medication_dialog, open_edit_medication_dialog,
                open_reminder_dialog, open_stop_medication_dialog,
                open_delete_confirm_dialog)
    except ImportError:
        try:
            # 尝试从screens目录导入（绝对导入）
            from screens.dialogs import (
                open_add_medication_dialog,
                open_edit_medication_dialog,
                open_reminder_dialog,
                open_stop_medication_dialog,
                open_delete_confirm_dialog,
            )
            return (open_add_medication_dialog, open_edit_medication_dialog,
                    open_reminder_dialog, open_stop_medication_dialog,
                    open_delete_confirm_dialog)
        except ImportError:
            try:
                # 尝试从mobile.screens导入
                from mobile.screens.dialogs import (
                    open_add_medication_dialog,
                    open_edit_medication_dialog,
                    open_reminder_dialog,
                    open_stop_medication_dialog,
                    open_delete_confirm_dialog,
                )
                return (open_add_medication_dialog, open_edit_medication_dialog,
                        open_reminder_dialog, open_stop_medication_dialog,
                        open_delete_confirm_dialog)
            except ImportError:
                # 最后尝试直接导入
                try:
                    from dialogs import (
                        open_add_medication_dialog,
                        open_edit_medication_dialog,
                        open_reminder_dialog,
                        open_stop_medication_dialog,
                        open_delete_confirm_dialog,
                    )
                    return (open_add_medication_dialog, open_edit_medication_dialog,
                            open_reminder_dialog, open_stop_medication_dialog,
                            open_delete_confirm_dialog)
                except ImportError:
                    # 所有导入都失败，返回占位符函数
                    return (_stub, _stub, _stub, _stub, _stub)


(OPEN_ADD_DIALOG,
 OPEN_EDIT_DIALOG,
 OPEN_REMINDER_DIALOG,
 OPEN_STOP_DIALOG,
 OPEN_DELETE_CONFIRM_DIALOG) = _import_dialogs()


# ------------------------------- Data Service ------------------------------
class _InMemoryMedicationService:
    """内存兜底服务，保证无外部依赖也能运行"""
    def __init__(self):
        self._current: List[Dict[str, Any]] = []
        self._history: List[Dict[str, Any]] = []
        self._id_seq = 1

    def _mk(self, **kw):
        data = {
            "id": self._id_seq,
            "name": kw.get("name", f"药物{self._id_seq}"),
            "dosage": kw.get("dosage", "100mg"),
            "frequency": kw.get("frequency", "一天一次"),
            "start_date": kw.get("start_date", datetime.now().strftime("%Y-%m-%d")),
            "instructions": kw.get("instructions", ""),
            "reason": kw.get("reason", ""),
            "notes": kw.get("notes", ""),
            "reminder_text": self._generate_reminder_display_text(kw),
            "stop_date": kw.get("stop_date", ""),
            "stop_reason": kw.get("stop_reason", ""),
        }
        self._id_seq += 1
        return data

    # Public API (尽量贴合现有项目命名)
    def list_current(self) -> List[Dict[str, Any]]:
        """返回当前用药列表"""
        # 添加一些示例数据用于测试
        if not hasattr(self, '_current_data'):
            self._current_data = [
                self._mk(name="阿司匹林", dosage="100mg", frequency="每日一次", start_date="2024-01-01", status="active"),
                self._mk(name="美托洛尔", dosage="25mg", frequency="每日两次", start_date="2024-01-02", status="active"),
                self._mk(name="氨氯地平", dosage="5mg", frequency="每日一次", start_date="2024-01-03", status="active"),
            ]
        return [med for med in self._current_data if med.get('status', 'active') == 'active']

    def list_history(self, keyword: str | None = None) -> List[Dict[str, Any]]:
        if not self._history:
            self._history.append(self._mk(name="氯沙坦", dosage="50mg", frequency="一天一次",
                                          stop_date="2024-12-01", stop_reason="医生建议"))
        data = list(self._history)
        if keyword:
            kw = keyword.strip()
            data = [x for x in data if kw in x.get("name", "")]
        return data

    def add(self, data: Dict[str, Any]):
        self._current.append(self._mk(**data))

    def update(self, med_id: int, data: Dict[str, Any]):
        for coll in (self._current, self._history):
            for row in coll:
                if row["id"] == med_id:
                    row.update(data)
                    return

    def stop(self, med_id: int, reason: str, stop_date: str | None = None):
        stop_date = stop_date or datetime.now().strftime("%Y-%m-%d")
        for idx, row in enumerate(self._current):
            if row["id"] == med_id:
                row["stop_date"] = stop_date
                row["stop_reason"] = reason or ""
                self._history.append(row)
                del self._current[idx]
                return

    def delete(self, med_id: int):
        for coll in (self._current, self._history):
            for i, row in enumerate(coll):
                if row["id"] == med_id:
                    del coll[i]
                    return


def _get_service():
    # 优先项目内service
    try:
        from services.medication_service import get_medication_service
        svc = get_medication_service()
        if svc:
            return svc
    except Exception:
        pass
    return _InMemoryMedicationService()


# ------------------------------- MedicationCard ----------------------------
class MedicationCard(MDCard):
    """统一的药物卡片：mode in {"current","history"}"""
    mode = StringProperty("current")
    data = ObjectProperty(allownone=True)

    selected = BooleanProperty(False)   # 仅 current 模式用于批量操作
    index = NumericProperty(0)

    def __init__(self, **kwargs):
        kwargs.setdefault("style", "elevated")
        kwargs.setdefault("size_hint_y", None)
        kwargs.setdefault("adaptive_height", True)  # 使用自适应高度
        kwargs.setdefault("padding", [dp(16), dp(14)])  # 增加内边距
        kwargs.setdefault("spacing", dp(10))  # 增加内部间距
        kwargs.setdefault("radius", [dp(12)])
        kwargs.setdefault("elevation", 2)
        kwargs.setdefault("md_bg_color", THEME.CARD_BACKGROUND)
        super().__init__(**kwargs)

        self._build()

    # --- UI ---
    def _build(self):
        self.clear_widgets()
        row = MDBoxLayout(orientation="horizontal", adaptive_height=True, spacing=dp(10))
        self.add_widget(row)

        # 左：选择框（仅 current）
        if self.mode == "current":
            # 创建一个可点击的容器来包装checkbox，增加点击区域
            checkbox_container = MDBoxLayout(
                size_hint=(None, None), 
                size=(dp(48), dp(48)),
                padding=[dp(10), dp(10)],
                pos_hint={'center_y': 0.5}
            )
            
            self.checkbox = MDCheckbox(
                size_hint=(None, None), 
                size=(dp(28), dp(28)),
                pos_hint={'center_x': 0.5, 'center_y': 0.5}
            )
            # 绑定checkbox状态到selected属性
            self.checkbox.bind(active=self._on_checkbox_change)
            
            # 让容器也能响应点击事件
            def _on_container_click(instance, *args):
                self.checkbox.active = not self.checkbox.active
            
            checkbox_container.bind(on_release=_on_container_click)
            checkbox_container.add_widget(self.checkbox)
            row.add_widget(checkbox_container)

        # 右：信息两列
        info = MDBoxLayout(orientation="horizontal", adaptive_height=True, spacing=dp(12))
        row.add_widget(info)

        left = MDBoxLayout(orientation="vertical", adaptive_height=True, spacing=dp(2))
        right = MDBoxLayout(orientation="vertical", adaptive_height=True, spacing=dp(2))
        info.add_widget(left)
        info.add_widget(right)

        def add_label(container, text, primary=False, hint=False):
            """添加标签到容器中，根据类型设置不同的样式和高度"""
            if not text:
                return
            role = "medium" if primary else ("small" if hint else "medium")
            theme_color = "Primary" if primary else ("Hint" if hint else "Secondary")
            # 增加标签高度，主要标签更高
            label_height = dp(28) if primary else dp(24)
            container.add_widget(MDLabel(text=text, font_style="Body", role=role,
                                         theme_text_color=theme_color, size_hint_y=None, 
                                         height=label_height, adaptive_height=True))

        def format_date_only(date_str):
            """格式化日期，只显示日期部分，不显示时间"""
            if not date_str:
                return ""
            try:
                # 处理各种可能的日期格式
                if 'T' in date_str:  # ISO格式 2025-08-31T00:00:00
                    return date_str.split('T')[0]
                elif ' ' in date_str:  # 包含空格的格式 2025-08-31 00:00:00
                    return date_str.split(' ')[0]
                else:  # 已经是纯日期格式
                    return date_str
            except Exception:
                return date_str  # 如果解析失败，返回原始字符串

        d = self.data or {}
        add_label(left, d.get("name", ""), primary=True)
        dosage = " ".join([x for x in [d.get("dosage", ""), d.get("frequency", "")] if x]).strip()
        add_label(left, dosage)
        add_label(left, f"开始: {format_date_only(d.get('start_date',''))}", hint=True)
        add_label(left, f"原因: {d.get('reason','')}", hint=True)

        # history 独有
        if self.mode == "history":
            add_label(right, f"停药: {format_date_only(d.get('stop_date',''))}", hint=True)
            add_label(right, f"原因: {d.get('stop_reason','')}", hint=True)

        add_label(right, f"说明: {d.get('instructions','')}", hint=True)
        add_label(right, f"注意: {d.get('notes','')}", hint=True)
        
        # 修复：确保提醒文本正确显示，即使为空也显示
        rem = self._generate_reminder_display_text(d)
        # 如果提醒文本为空或"未设置提醒"，则不显示
        if rem and rem != "未设置提醒":
            add_label(right, f"提醒: {rem}", primary=True)

    def _generate_reminder_display_text(self, medication_data: Dict[str, Any]) -> str:
        """生成提醒显示文本 - 替代reminder_text字段"""
        try:
            import json
            
            # 优先从reminder_settings中获取提醒信息
            reminder_settings = medication_data.get('reminder_settings')
            if reminder_settings:
                try:
                    if isinstance(reminder_settings, str):
                        settings_data = json.loads(reminder_settings)
                    else:
                        settings_data = reminder_settings
                    
                    if isinstance(settings_data, dict) and settings_data.get('enabled'):
                        times = settings_data.get('times', [])
                        if times and isinstance(times, list):
                            return f"每日提醒: {'、'.join(times)}"
                except (json.JSONDecodeError, TypeError) as e:
                    logger.warning(f"解析reminder_settings失败: {e}")
            
            # 如果reminder_settings中没有找到，检查reminder_times字段
            reminder_times = medication_data.get('reminder_times')
            if reminder_times:
                try:
                    if isinstance(reminder_times, str):
                        times_data = json.loads(reminder_times)
                    else:
                        times_data = reminder_times
                    
                    if isinstance(times_data, list) and times_data:
                        return f"每日提醒: {'、'.join(times_data)}"
                except (json.JSONDecodeError, TypeError) as e:
                    logger.warning(f"解析reminder_times失败: {e}")
            
            return "未设置提醒"
            
        except Exception as e:
            logger.error(f"生成提醒显示文本失败: {e}")
            return "未设置提醒"

    def _on_checkbox_change(self, checkbox, active):
        """处理checkbox状态变化"""
        self.selected = active
        
    # 对外API：用于外部更新data时刷新UI
    def set_data(self, data: Dict[str, Any]):
        self.data = data
        self._build()

    # 修复：添加更新数据并刷新显示的方法
    def update_data(self, data: Dict[str, Any]):
        """更新数据并刷新显示"""
        if self.data:
            self.data.update(data)
        else:
            self.data = data
        self._build()


# -------------------------- MedicationManagementScreen ---------------------
class MedicationManagementScreen(BaseScreen):
    # tabs
    current_tab = StringProperty("current")

    # data
    current_list = ListProperty([])
    history_list = ListProperty([])

    # cache + flags
    _cache: Dict[str, Any] = {}
    _service = None

    def __init__(self, **kwargs):
        kwargs.setdefault("screen_title", "用药管理")
        kwargs.setdefault("show_top_bar", True)
        kwargs.setdefault("top_bar_action_icon", "refresh")
        super().__init__(**kwargs)
        # main layout created by BaseScreen; defer build to do_content_setup
        self._service = _get_service()
        self._containers_built = False

    # ------------------------- Lifecycle -------------------------
    def do_content_setup(self):
        """设置页面内容 - 在content_container中添加内容"""
        try:
            # 增加Clock最大迭代次数以避免警告
            Clock.max_iteration = 50
            
            # 获取content_container
            content_container = self.ids.get('content_container')
            if not content_container:
                logger.error("[MedicationManagementScreen] 无法找到content_container")
                return
            
            # 清空现有内容
            content_container.clear_widgets()
            
            # 创建主内容布局 - 直接添加到content_container，不使用ScrollView嵌套
            main_layout = MDBoxLayout(
                orientation='vertical',
                adaptive_height=True,  # 改为自适应高度
                spacing=dp(12),
                padding=[dp(16), dp(8), dp(16), dp(8)]  # 增加上下边距
            )

            # Tab bar
            self._build_tabs(main_layout)
            
            # 添加Tab与功能卡片之间的间距
            main_layout.add_widget(MDBoxLayout(size_hint_y=None, height=dp(8)))

            # Function card placeholder (switch with tab)
            self.function_card_container = MDBoxLayout(orientation="vertical", adaptive_height=True)
            main_layout.add_widget(self.function_card_container)
            
            # 添加功能卡片与药物卡片之间的间距
            main_layout.add_widget(MDBoxLayout(size_hint_y=None, height=dp(8)))

            # 药物卡片容器 - 直接添加，不使用额外的ScrollView
            self.content_stack = MDBoxLayout(
                orientation="vertical", 
                adaptive_height=True, 
                spacing=dp(12),
                padding=[dp(4), dp(4), dp(4), dp(4)]
            )
            main_layout.add_widget(self.content_stack)

            # Build containers once
            self.current_container = MDBoxLayout(orientation="vertical", adaptive_height=True, spacing=dp(12))
            self.history_search_box = self._build_history_search_box()
            self.history_container = MDBoxLayout(orientation="vertical", adaptive_height=True, spacing=dp(12))

            self._containers_built = True

            # 将主布局添加到content_container - content_container本身就是ScrollView
            content_container.add_widget(main_layout)

            # Default to current tab - 延迟执行以避免布局冲突
            Clock.schedule_once(lambda dt: self.switch_tab("current"), 0.2)
            
            logger.info("[MedicationManagementScreen] 成功设置页面内容")
            
        except Exception as e:
            logger.error(f"[MedicationManagementScreen] 设置页面内容失败: {e}")
            import traceback
            traceback.print_exc()

    def on_action(self):
        # 顶部右上角动作（一般用于刷新）
        self.reload_all()

    def refresh_data(self):
        self.reload_all()

    # ------------------------- UI Builders -----------------------
    def _get_main_layout(self) -> Optional[MDBoxLayout]:
        # 由于改造后不再使用main_layout id，这个方法保留用于兼容性
        # 但实际上不会被调用，因为UI构建已经在do_content_setup中完成
        return None

    def _build_tabs(self, parent: MDBoxLayout):
        bar = MDBoxLayout(orientation="horizontal", size_hint_y=None, height=dp(44), padding=[0, dp(4)], spacing=dp(8))
        # current
        self.btn_current = MDButton(style="filled", size_hint=(None, None), width=dp(96), height=dp(36),
                                    on_release=lambda *_: self.switch_tab("current"))
        self.btn_current.add_widget(MDButtonText(text="目前用药", theme_text_color="Custom", text_color=(1,1,1,1)))
        # history
        self.btn_history = MDButton(style="outlined", size_hint=(None, None), width=dp(96), height=dp(36),
                                    on_release=lambda *_: self.switch_tab("history"))
        self.btn_history.add_widget(MDButtonText(text="既往用药"))
        # center
        spacer = MDBoxLayout(size_hint_x=None, width=dp(8))
        inner = MDBoxLayout(orientation="horizontal", size_hint=(None, None), width=dp(220), height=dp(36),
                            pos_hint={"center_x": .5}, spacing=dp(8))
        inner.add_widget(self.btn_current)
        inner.add_widget(self.btn_history)
        bar.add_widget(MDBoxLayout())  # left flex
        bar.add_widget(inner)
        bar.add_widget(MDBoxLayout())  # right flex
        parent.add_widget(bar)

    def _build_history_search_box(self):
        box = MDBoxLayout(orientation="horizontal", adaptive_height=True, spacing=dp(6))
        self.history_search_field = MDTextField(mode="filled", size_hint_y=None, height=dp(40),
                                                hint_text="请输入药物名称进行查询")
        btn = MDIconButton(icon="magnify", on_release=lambda *_: self._on_history_search())
        box.add_widget(self.history_search_field)
        box.add_widget(btn)
        return box

    def _build_function_card_current(self):
        """构建当前用药功能按钮卡片"""
        card = MDCard(orientation="horizontal", size_hint_y=None, height=dp(40), 
                      padding=[dp(4), dp(2), dp(4), dp(2)],
                      radius=[dp(8)], elevation=1,
                      md_bg_color=THEME.CARD_BACKGROUND)
        row = MDBoxLayout(orientation="horizontal", spacing=dp(2), padding=[dp(2), dp(1), dp(2), dp(1)])
        card.add_widget(row)

        def btn(text, cb, filled=False, danger=False, w=36):
            """创建功能按钮，使用更小的尺寸"""
            b = MDButton(style=("filled" if filled else "outlined"), size_hint=(None, None), 
                         width=dp(w), height=dp(20),  # 进一步减小按钮高度
                         on_release=lambda *_: cb())
            t = MDButtonText(text=text, theme_text_color="Custom", font_size=dp(10))  # 减小字体
            if filled:
                t.text_color = (1,1,1,1)
            if danger:
                t.theme_text_color = "Error"
            b.add_widget(t)
            return b

        # 四个功能按钮，使用更小的宽度
        row.add_widget(btn("修正", self._on_edit_selected, w=36))
        row.add_widget(btn("提醒", self._on_remind_selected, w=36))
        row.add_widget(btn("停用", self._on_stop_selected, w=36))
        row.add_widget(btn("删除", self._on_delete_selected, danger=True, w=36))
        
        # 添加弹性空间
        spacer = MDBoxLayout(size_hint_x=1)
        row.add_widget(spacer)
        
        # 简化的"+"文本，不使用按钮
        add_label = MDLabel(text="+", 
                           theme_text_color="Custom",
                           text_color=THEME.PRIMARY_COLOR,
                           font_size=dp(24),
                           size_hint=(None, None),
                           width=dp(30),
                           height=dp(30),
                           halign="center",
                           valign="center")
        # 添加点击事件
        add_label.bind(on_touch_down=lambda instance, touch: 
                      self._open_add_dialog() if instance.collide_point(*touch.pos) else None)
        row.add_widget(add_label)
        
        return card

    def _build_function_card_history(self):
        # 仅包含查询框（已在 content_stack 顶部添加 history_search_box）
        card = MDCard(orientation="horizontal", size_hint_y=None, height=dp(0), opacity=0, elevation=0)
        return card

    # -------------------------- Tab switch & data ---------------------------
    def switch_tab(self, mode: str):
        self.current_tab = mode

        # Tab visual
        if mode == "current":
            self.btn_current.style = "filled"
            self.btn_history.style = "outlined"
            try:
                self.btn_current.children[0].text_color = (1,1,1,1)
            except Exception:
                pass
        else:
            self.btn_current.style = "outlined"
            self.btn_history.style = "filled"
            try:
                self.btn_history.children[0].text_color = (1,1,1,1)
            except Exception:
                pass

        # Function card
        self.function_card_container.clear_widgets()
        if mode == "current":
            self.function_card_container.add_widget(self._build_function_card_current())
        else:
            self.function_card_container.add_widget(self._build_function_card_history())

        # Content
        self.content_stack.clear_widgets()
        if mode == "current":
            self.content_stack.add_widget(self.current_container)
            self._load_current()
        else:
            self.content_stack.add_widget(self.history_search_box)
            self.content_stack.add_widget(self.history_container)
            self._load_history()

    def reload_all(self):
        if self.current_tab == "current":
            self._load_current(force=True)
        else:
            self._load_history(force=True)

    # -------------------------- Loaders & populate --------------------------
    def _load_current(self, force: bool=False):
        if (not force) and self._cache.get("current"):
            self._populate(self.current_container, self._cache["current"], "current")
            return
        try:
            data = self._service.list_current()
            self._cache["current"] = data
            self._populate(self.current_container, data, "current")
        except Exception as e:
            logger.error(f"加载当前用药失败: {e}")
            _toast("加载当前用药失败")

    def _load_history(self, force: bool=False, keyword: Optional[str]=None):
        cache_key = f"history:{keyword or ''}"
        if (not force) and cache_key in self._cache:
            self._populate(self.history_container, self._cache[cache_key], "history")
            return
        try:
            data = self._service.list_history(keyword=keyword)
            self._cache[cache_key] = data
            self._populate(self.history_container, data, "history")
        except Exception as e:
            logger.error(f"加载既往用药失败: {e}")
            _toast("加载既往用药失败")

    def _populate(self, container: MDBoxLayout, data: List[Dict[str, Any]], mode: str):
        """填充药物卡片到容器中，并添加适当的间距"""
        # 使用Clock.schedule_once避免频繁的布局计算
        def _do_populate(dt):
            container.clear_widgets()
            if not data:
                empty = MDLabel(text="暂无数据", halign="center", size_hint_y=None, height=dp(48),
                                theme_text_color="Hint")
                container.add_widget(empty)
                return
            
            for idx, row in enumerate(data):
                # 添加卡片间距（除了第一个卡片）
                if idx > 0:
                    spacer = MDBoxLayout(size_hint_y=None, height=dp(12))
                    container.add_widget(spacer)
                
                card = MedicationCard(mode=mode, data=row, index=idx)
                container.add_widget(card)
        
        # 延迟执行以避免Clock迭代警告
        Clock.schedule_once(_do_populate, 0)

    # -------------------------- Action handlers -----------------------------
    def _get_selected_current_cards(self) -> List[MedicationCard]:
        cards = []
        for w in self.current_container.children[:]:
            if isinstance(w, MedicationCard) and w.mode == "current" and w.selected:
                cards.append(w)
        return list(reversed(cards))  # children是倒序

    def _on_edit_selected(self):
        cards = self._get_selected_current_cards()
        if not cards:
            _toast("请先勾选要修改的药物")
            return
        for c in cards:
            self._open_edit_dialog(c.data)

    def _on_remind_selected(self):
        cards = self._get_selected_current_cards()
        if not cards:
            _toast("请先勾选要设置提醒的药物")
            return
        for c in cards:
            self._open_reminder_dialog(c.data)

    def _on_stop_selected(self):
        cards = self._get_selected_current_cards()
        if not cards:
            _toast("请先勾选要停用的药物")
            return
        for c in cards:
            self._open_stop_dialog(c.data)

    def _on_delete_selected(self):
        cards = self._get_selected_current_cards()
        if not cards:
            _toast("请先勾选要删除的药物")
            return
        for c in cards:
            self._open_delete_confirm(c.data)

    def _on_history_search(self):
        keyword = (self.history_search_field.text or "").strip()
        self._load_history(force=True, keyword=keyword)

    # -------------------------- Dialog bridges ------------------------------
    def _open_add_dialog(self):
        """打开添加药物对话框"""
        def on_submit(payload: Dict[str, Any]):
            try:
                # 修复：确保payload不为None
                if not payload:
                    payload = {}
                
                self._service.save_medication(payload)
                _toast("已添加")
                self._cache.pop("current", None)
                
                # 修复：立即刷新UI显示新添加的药物
                Clock.schedule_once(lambda dt: self._load_current(force=True), 0.1)
            except Exception as e:
                logger.error(f"添加失败: {e}")
                _toast("添加失败")

        # 使用正确的参数名称
        OPEN_ADD_DIALOG(screen_instance=self, save_callback=on_submit)

    def _open_edit_dialog(self, row: Dict[str, Any]):
        """打开编辑药物对话框"""
        def on_submit(payload: Dict[str, Any]):
            try:
                # 添加id到payload中，因为update_medication需要id字段
                update_data = dict(payload or {})
                update_data['id'] = row.get("id")
                self._service.update_medication(update_data)
                _toast("已更新")
                # 修复：立即清空缓存
                self._cache.pop("current", None)
                # 修复：立即更新UI中的卡片数据，而不是重新加载整个列表
                for card in self.current_container.children:
                    if isinstance(card, MedicationCard) and card.data and card.data.get("id") == update_data['id']:
                        # 使用新的更新方法更新卡片数据并刷新显示
                        card.update_data(update_data)
                        break
                else:
                    # 如果没有找到对应的卡片，重新加载整个列表
                    self._load_current(force=True)
            except Exception as e:
                logger.error(f"更新失败: {e}")
                _toast("更新失败")

        # 使用正确的参数名称
        OPEN_EDIT_DIALOG(screen_instance=self, medication=row, save_callback=on_submit)

    def _open_reminder_dialog(self, row: Dict[str, Any]):
        """打开提醒设置对话框"""
        def on_submit(payload: Dict[str, Any]):
            # 处理提醒设置数据
            try:
                medication_id = row.get("id")
                if not medication_id:
                    _toast("药物ID无效")
                    return
                
                # 如果payload包含reminder_settings，使用专门的方法
                if 'reminder_settings' in (payload or {}):
                    self._service.update_reminder_settings(medication_id, payload['reminder_settings'])
                else:
                    # 否则使用通用更新方法
                    update_data = dict(payload or {})
                    update_data['id'] = medication_id
                    self._service.update_medication(update_data)
                
                _toast("提醒已设置")
                self._cache.pop("current", None)
                
                # 更新单个药物卡片的提醒信息（如果找到对应卡片）
                updated_medication = self._service.get_medication_by_id(medication_id)
                if updated_medication:
                    # 查找并更新对应的药物卡片
                    for card in self.current_container.children:
                        if isinstance(card, MedicationCard) and card.data and card.data.get("id") == medication_id:
                            # 更新卡片数据并刷新显示
                            card.update_data(updated_medication)
                            break
                    else:
                        # 如果没有找到对应的卡片，重新加载整个列表
                        self._load_current(force=True)
                else:
                    # 如果无法获取更新后的药物信息，重新加载整个列表
                    self._load_current(force=True)
                    
            except Exception as e:
                logger.error(f"提醒设置失败: {e}")
                _toast("提醒设置失败")

        # 使用正确的参数名称
        OPEN_REMINDER_DIALOG(screen_instance=self, medication=row, save_callback=on_submit)

    def _open_stop_dialog(self, row: Dict[str, Any]):
        """打开停药对话框"""
        def on_submit(payload: Dict[str, Any]):
            # 期望 payload: {"stop_reason": "...", "stop_date": "YYYY-MM-DD"}
            try:
                self._service.stop_medication(row.get("id"),
                                   stop_reason=(payload or {}).get("stop_reason", ""),
                                   stop_date=(payload or {}).get("stop_date"))
                _toast("已停药")
                # 清空所有缓存
                self._cache.clear()
                # 刷新当前和历史列表
                self._load_current(force=True)
                self._load_history(force=True)
            except Exception as e:
                logger.error(f"停药失败: {e}")
                _toast("停药失败")

        # 使用正确的参数名称
        OPEN_STOP_DIALOG(screen_instance=self, name=row.get('name', ''), confirm_callback=on_submit)

    def _open_delete_confirm(self, row: Dict[str, Any]):
        """打开删除确认对话框"""
        def on_confirm(*_):
            try:
                self._service.delete_medication(row.get("id"))
                _toast("已删除")
                self._cache.pop("current", None)
                self._load_current(force=True)
            except Exception as e:
                logger.error(f"删除失败: {e}")
                _toast("删除失败")

        # 使用正确的参数名称
        OPEN_DELETE_CONFIRM_DIALOG(screen_instance=self, name=row.get('name', ''), confirm_callback=on_confirm)