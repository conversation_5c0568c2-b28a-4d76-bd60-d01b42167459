"""
健康资料管理屏幕
提供健康资料的上传、删除和管理功能，支持直接文件上传、二维码扫描和拍照上传
遵循 KivyMD 2.0.1 dev0 规范，使用 theme.py 配置
完全继承BaseScreen基类，使用统一的UI结构
"""

from kivy.logger import Logger as Kivy<PERSON>ogger
from kivymd.app import MDApp
from kivy.metrics import dp
from kivy.properties import StringProperty, ObjectProperty, BooleanProperty, ListProperty, DictProperty, NumericProperty
from mobile.screens.base_screen import BaseScreen
from kivy.clock import Clock
from kivy.lang import Builder
from kivy.core.window import Window
import os
import json
import logging
import threading
import time
from datetime import datetime
from typing import Optional, Any, Literal

from kivymd.uix.boxlayout import MDBoxLayout
from kivymd.uix.button import MDIconButton, MDButtonText, MDButton
from kivymd.uix.label import MDLabel, MDIcon
from kivymd.uix.card import MDCard
from kivymd.uix.scrollview import MDScrollView
from kivymd.uix.textfield import MDTextField
from kivymd.uix.selectioncontrol import MDCheckbox
from kivymd.uix.menu import MDDropdownMenu
from kivymd.uix.list import MDList, MDListItem, MDListItemHeadlineText, MDListItemSupportingText
from kivymd.uix.dialog import MDDialog
from kivy.uix.progressbar import ProgressBar
from kivymd.uix.chip import MDChip, MDChipLeadingIcon, MDChipText
from kivymd.uix.snackbar import MDSnackbar, MDSnackbarText

# 导入主题和字体样式
try:
    from mobile.theme import AppTheme, AppMetrics, FontStyles
except ImportError:
    try:
        from theme import AppTheme, AppMetrics, FontStyles
    except ImportError:
        # 如果无法导入主题配置，创建默认配置
        class AppTheme:
            PRIMARY_COLOR = [0.133, 0.46, 0.82, 1]
            TEXT_PRIMARY = [0, 0, 0, 1]
            TEXT_SECONDARY = [0.45, 0.45, 0.45, 1]
            CARD_BACKGROUND = [0.95, 0.95, 0.95, 1]
        
        class AppMetrics:
            NAVBAR_HEIGHT = 56
        
        class FontStyles:
            pass

# 导入工具类
from utils.health_data_aggregator import get_health_data_aggregator
from utils.user_manager import get_user_manager
from utils.cloud_api import get_cloud_api
from utils.file_upload_download_manager import FileUploadDownloadManager, get_file_upload_download_manager

# 设置日志
logger = logging.getLogger(__name__)

# 定义KV语言字符串（仅保留组件类的KV定义，移除页面级别的KV定义）
KV = '''
<DocumentCard>:
    orientation: 'vertical'
    size_hint_y: None
    height: dp(70)  # 减小卡片高度以优化移动端显示
    md_bg_color: app.theme_cls.surfaceContainerColor
    radius: [dp(8)]  # 减小圆角
    elevation: 1
    padding: [dp(10), dp(6), dp(10), dp(6)]  # 减小内边距
    spacing: dp(3)  # 减小间距
    ripple_behavior: True
    # 添加宽度限制，使其与搜索卡片保持一致
    size_hint_x: None
    width: dp(320)  # 减小卡片宽度
    pos_hint: {'center_x': 0.5}  # 居中显示

    MDBoxLayout:
        orientation: 'horizontal'
        size_hint_y: None
        height: dp(24)  # 减小高度
        spacing: dp(6)  # 减小间距

        MDIcon:
            icon: root.icon
            size_hint_x: None
            width: dp(20)  # 减小图标大小
            theme_icon_color: "Custom"
            icon_color: app.theme_cls.primaryColor
            pos_hint: {"center_y": 0.5}

        MDBoxLayout:
            orientation: 'vertical'
            size_hint_x: 0.65
            spacing: dp(1)  # 减小间距

            MDLabel:
                text: root.title
                font_style: "Label"  # 改为更小的字体样式
                role: "small"  # 改为small以减小字体
                theme_text_color: "Primary"
                size_hint_y: None
                height: self.texture_size[1]
                shorten: True
                shorten_from: 'right'

            MDLabel:
                text: root.date
                font_style: "Label"
                role: "small"  # 减小字体
                theme_text_color: "Secondary"
                size_hint_y: None
                height: self.texture_size[1]

        MDBoxLayout:
            orientation: 'horizontal'
            size_hint_x: 0.35
            spacing: dp(3)  # 减小间距

            MDIconButton:
                icon: "eye"
                font_size: dp(16)  # 减小按钮图标大小
                theme_icon_color: "Custom"
                icon_color: app.theme_cls.primaryColor
                on_release: root.on_view()
                size_hint_x: None
                width: dp(32)  # 减小按钮宽度

            MDIconButton:
                icon: "delete"
                font_size: dp(16)  # 减小按钮图标大小
                theme_icon_color: "Custom"
                icon_color: app.theme_cls.errorColor
                on_release: root.on_delete()
                size_hint_x: None
                width: dp(28)  # 减小按钮宽度

    MDLabel:
        text: root.summary
        font_style: "Label"
        role: "small"  # 减小字体
        theme_text_color: "Secondary"
        size_hint_y: None
        height: self.texture_size[1]
        shorten: True
        shorten_from: 'right'
'''

class DocumentCard(MDCard):
    """健康资料卡片组件"""
    title = StringProperty("")
    date = StringProperty("")
    summary = StringProperty("")
    icon = StringProperty("file-document")
    document_id = StringProperty("")
    document_type = StringProperty("")
    document_data = DictProperty({})

    def __init__(self, **kwargs):
        super().__init__(**kwargs)
        self.parent_screen: Optional['HealthDocumentScreen'] = None

    def on_view(self):
        """查看文档"""
        logger.info(f"查看文档: {self.document_id}, 类型: {self.document_type}")
        if self.parent_screen:
            # 确保document_id是字符串类型
            doc_id = str(self.document_id) if self.document_id is not None else ""
            self.parent_screen.on_view_document(self, doc_id, self.document_type)
        else:
            logger.error("未设置父屏幕，无法处理查看事件")

    def on_delete(self):
        """删除文档"""
        logger.info(f"删除文档: {self.document_id}, 类型: {self.document_type}")
        if self.parent_screen:
            # 确保document_id是字符串类型
            doc_id = str(self.document_id) if self.document_id is not None else ""
            self.parent_screen.on_delete_document(self, doc_id, self.document_type)
        else:
            logger.error("未设置父屏幕，无法处理删除事件")

    def on_touch_up(self, touch):
        """处理触摸事件，仅处理按钮点击，不自动触发查看功能"""
        if self.collide_point(*touch.pos):
            # 只处理按钮点击事件，不自动触发查看功能
            for child in self.walk(restrict=True):
                if isinstance(child, MDIconButton) and child.collide_point(*touch.pos):
                    return super().on_touch_up(touch)
            # self.on_view()  # 已注释掉自动查看功能
        return super().on_touch_up(touch)

# 在类定义后注册Factory
from kivy.factory import Factory
Factory.register('DocumentCard', cls=DocumentCard)

class HealthDocumentScreen(BaseScreen):
    """健康资料管理屏幕"""
    document_type = StringProperty("all")  # 当前文档类型，默认显示所有类型
    documents = ListProperty([])  # 文档列表 - 存储所有已加载的文档
    filtered_documents = ListProperty([])  # 过滤后的文档列表 - 存储所有过滤后的文档
    is_loading = BooleanProperty(False)  # 是否正在加载
    upload_progress = NumericProperty(0)  # 上传进度
    filter_category = StringProperty(None, allownone=True)  # 当前过滤的分类
    filter_subcategory = StringProperty(None, allownone=True)  # 当前过滤的子分类
    
    # 分页相关属性
    current_page = NumericProperty(1)
    total_pages = NumericProperty(1)
    page_size = NumericProperty(10)  # 每页显示10个文档
    total_document_count = NumericProperty(0)  # 总文档数

    def __init__(self, **kwargs):
        # 设置导航栏属性
        kwargs['screen_title'] = '健康资料传阅'
        kwargs['show_top_bar'] = True
        kwargs['top_bar_action_icon'] = 'refresh'
        super().__init__(**kwargs)
        
        self.app = MDApp.get_running_app()
        self.aggregator = get_health_data_aggregator()
        self.dialog: Optional[MDDialog] = None
        self.filter_dialog: Optional[MDDialog] = None
        self.active_filters = {}
        self.loading_dialog: Optional[MDDialog] = None
        
        # 文档类型映射定义 - 五大类及其子分类
        self.document_types = {
            "hospital_records": {
                "title": "住院资料",
                "icon": "hospital-building",
                "description": "住院记录和病历资料",
                "subcategories": {
                    "admission_record": "入院记录",
                    "discharge_summary": "出院小结",
                    "surgery_record": "手术记录",
                    "other_record": "其它记录"
                }
            },
            "outpatient_records": {
                "title": "门诊资料",
                "icon": "doctor",
                "description": "门诊就诊记录和处方信息",
                "subcategories": {
                    "outpatient_medical_record": "门诊病历",
                    "treatment_record": "治疗记录",
                    "prescription_record": "处方记录",
                    "other_record": "其它记录"
                }
            },
            "lab_reports": {
                "title": "检验报告",
                "icon": "flask",
                "description": "各类检验检查报告",
                "subcategories": {
                    "blood_test": "血液检验",
                    "urine_test": "尿液检验",
                    "biochemical_test": "生化检验",
                    "immune_test": "免疫检验",
                    "other_test": "其它检验"
                }
            },
            "tech_diagnosis_reports": {
                "title": "技诊报告",
                "icon": "stethoscope",
                "description": "超声、CT、核磁等技术诊断报告",
                "subcategories": {
                    "ecg": "心电图",
                    "chest_xray": "胸片",
                    "chest_ct": "胸部CT",
                    "brain_mr": "头颅MR",
                    "endoscopy": "胃肠镜",
                    "ultrasound": "超声检查",
                    "other_exam": "其它检查"
                }
            },
            "physical_exam_reports": {
                "title": "体检报告",
                "icon": "clipboard-check",
                "description": "年度体检报告和健康评估",
                "subcategories": {
                    "annual_checkup": "年度体检",
                    "comprehensive_exam": "全面体检",
                    "specialized_exam": "专项体检",
                    "other_exam": "其它体检"
                },
                "requires_year": True  # 标记需要输入年度
            }
        }

        # 上传方式定义
        self.upload_methods = {
            "direct_upload": {
                "title": "直接上传",
                "icon": "upload",
                "description": "选择文件直接上传"
            },
            "qr_upload": {
                "title": "二维码上传",
                "icon": "qrcode",
                "description": "扫描二维码获取文档链接"
            },
            "camera_upload": {
                "title": "拍照上传",
                "icon": "camera",
                "description": "调用摄像头拍照上传"
            }
        }

        # 当前选择的分类和子分类
        self.selected_category: Optional[str] = None
        self.selected_subcategory: Optional[str] = None
        self.selected_upload_method = "direct_upload"

        # 过滤器状态
        self.filter_category: Optional[str] = None
        self.filter_subcategory: Optional[str] = None

        # 初始化文件上传下载管理器
        self.file_upload_manager = get_file_upload_download_manager()
        self._current_upload_metadata = None  # 存储当前上传的元数据
        if self.file_upload_manager:
            self.file_upload_manager.bind(on_upload_complete=self.on_upload_complete)
            self.file_upload_manager.bind(on_upload_progress=self.on_upload_progress)
            self.file_upload_manager.bind(on_error=self.on_upload_error)
            # 绑定文件选择事件，只绑定一次
            self.file_upload_manager.bind(on_file_selected=self._on_file_selected_handler)
    
    def on_enter(self, *args):
        """进入屏幕时调用"""
        # 调用父类方法确保基础结构已初始化
        super().on_enter(*args)
        
        # 强制校验登录状态
        app = MDApp.get_running_app()

        # 检查是否已登录
        is_logged_in = False
        user_data = getattr(app, 'user_data', None)
        if user_data is not None and user_data.get('username'):
            # 检查cloud_api的认证状态
            try:
                from utils.cloud_api import get_cloud_api
                cloud_api = get_cloud_api()
                if cloud_api and cloud_api.is_authenticated():
                    is_logged_in = True
            except Exception as e:
                logger.error(f"检查cloud_api认证状态时出错: {e}")

        if not is_logged_in:
            # 未登录或认证无效，强制跳转回登录页并提示
            if self.manager:
                self.manager.current = 'login_screen'
            # 显示提示信息
            snackbar = MDSnackbar(MDSnackbarText(text="请先登录"))
            snackbar.open()
            return

        # 延迟加载数据以确保UI已完全初始化
        Clock.schedule_once(lambda dt: self.load_documents(), 0.1)
    
    def do_content_setup(self):
        """设置页面内容 - 在content_container中添加内容"""
        try:
            # 获取content_container
            content_container = self.ids.get('content_container')
            if not content_container:
                logger.error("[HealthDocumentScreen] 无法找到content_container")
                return
            
            # 清空现有内容
            content_container.clear_widgets()
            
            # 创建主内容布局
            main_layout = MDBoxLayout(
                orientation='vertical',
                size_hint_y=None,
                spacing=dp(12),
                padding=[dp(16), dp(0), dp(16), dp(0)]  # 左右边距16dp，符合BaseScreen规范
            )
            main_layout.bind(minimum_height=main_layout.setter('height'))
            
            # 添加各个功能卡片
            self.add_upload_card(main_layout)
            self.add_filter_card(main_layout) 
            self.add_progress_card(main_layout)
            
            # 创建文档列表区域
            self.add_documents_section(main_layout)
            
            # 将主布局添加到content_container
            content_container.add_widget(main_layout)
            
            logger.info("[HealthDocumentScreen] 成功设置页面内容")
            
        except Exception as e:
            logger.error(f"[HealthDocumentScreen] 设置页面内容失败: {e}")
            import traceback
            traceback.print_exc()
        
    def _get_document_icon(self, doc_type) -> str:
        """根据文档类型获取图标"""
        icon_map = {
            'medical_record': 'hospital-building',
            'lab_report': 'flask',
            'prescription': 'pill',
            'document': 'file-document',
            'image': 'image',
            'pdf': 'file-pdf-box',
            'text': 'file-document-outline'
        }
        return icon_map.get(str(doc_type).lower(), 'file-document')
    
    def _normalize_document(self, doc):
        """标准化文档数据格式"""
        if isinstance(doc, dict):
            return self._create_normalized_doc(doc)
        elif isinstance(doc, str):
            return self._parse_and_normalize_string_doc(doc)
        else:
            return None
    
    def _create_normalized_doc(self, doc_data):
        """创建标准化的文档对象"""
        return {
            'id': str(doc_data.get('id', '')),
            'title': doc_data.get('title', '') or doc_data.get('file_name', '') or '未知标题',
            'file_name': doc_data.get('file_name', '') or doc_data.get('filename', ''),
            'date': doc_data.get('created_at', '') or doc_data.get('uploaded_at', '') or doc_data.get('date', '未知日期'),
            'summary': doc_data.get('description', '') or doc_data.get('summary', ''),
            'icon': self._get_document_icon(doc_data.get('document_type', '') or doc_data.get('type', '')),
            'document_type': doc_data.get('document_type', '') or doc_data.get('type', ''),
            'type': doc_data.get('type', '') or doc_data.get('document_type', ''),
            'document_data': doc_data
        }
    
    def _parse_and_normalize_string_doc(self, doc_str):
        """解析字符串文档并标准化"""
        try:
            parsed_doc = json.loads(doc_str)
            if isinstance(parsed_doc, dict):
                return self._create_normalized_doc(parsed_doc)
            else:
                logger.debug(f"跳过非字典类型的解析结果: {type(parsed_doc)}")
                return None
        except (json.JSONDecodeError, TypeError):
            logger.debug(f"跳过无法解析的字符串数据: {doc_str[:50]}...")
            return None

    def add_documents_section(self, parent_layout) -> None:
        """添加文档列表区域（使用滚动视图包装）"""
        # 创建文档区域容器
        documents_section = MDBoxLayout()
        documents_section.orientation = 'vertical'
        documents_section.size_hint_y = None
        documents_section.spacing = dp(8)
        documents_section.bind(minimum_height=documents_section.setter('height'))
        
        # 添加分页控件到文档区域容器
        self.add_pagination_controls(documents_section)
        
        # 创建滚动视图，用于文档列表
        self.documents_scroll_view = MDScrollView()
        self.documents_scroll_view.do_scroll_x = False
        self.documents_scroll_view.do_scroll_y = True
        self.documents_scroll_view.size_hint_y = None
        self.documents_scroll_view.height = dp(400)  # 设置固定高度，确保滚动视图可见
        
        # 创建文档容器布局
        documents_container = MDBoxLayout()
        documents_container.orientation = 'vertical'
        documents_container.size_hint_y = None
        documents_container.bind(minimum_height=documents_container.setter('height'))
        documents_container.padding = [dp(0), dp(12), dp(0), dp(8)]  # 移除左右边距，由主布局统一控制
        documents_container.spacing = dp(12)  # 文档卡片间距
        
        self.documents_container = documents_container
        self.documents_scroll_view.add_widget(documents_container)
        
        # 将滚动视图添加到文档区域容器
        documents_section.add_widget(self.documents_scroll_view)
        
        # 将文档区域容器添加到父布局
        parent_layout.add_widget(documents_section)
    
    def add_pagination_controls(self, parent_layout) -> None:
        """添加分页控件"""
        pagination_layout = MDBoxLayout(
            orientation='horizontal',
            size_hint_y=None,
            height=dp(48),
            padding=[dp(16), dp(8), dp(16), dp(8)],
            spacing=dp(8),
            pos_hint={'center_x': 0.5}  # 修正语法错误: 使用等号而不是冒号
        )
        
        # 上一页按钮
        self.prev_page_button = MDButton(
            MDButtonText(text="上一页", font_style="Label", role="large"),
            style="outlined",
            size_hint_x=None,
            width=dp(80),
            on_release=self.go_to_prev_page
        )
        
        # 页码信息
        self.page_info_label = MDLabel(
            text="第 1 页 / 共 1 页",
            font_style="Body",
            role="small",
            halign="center",
            size_hint_x=None,
            width=dp(120)
        )
        
        # 下一页按钮
        self.next_page_button = MDButton(
            MDButtonText(text="下一页", font_style="Label", role="large"),
            style="outlined",
            size_hint_x=None,
            width=dp(80),
            on_release=self.go_to_next_page
        )
        
        # 删除页码输入框和跳转按钮，只保留上一页和下一页按钮
        pagination_layout.add_widget(self.prev_page_button)
        pagination_layout.add_widget(self.page_info_label)
        pagination_layout.add_widget(self.next_page_button)
        
        parent_layout.add_widget(pagination_layout)
        
        # 初始化分页控件状态
        self.update_pagination_controls()
    
    def update_pagination_controls(self) -> None:
        """更新分页控件状态"""
        logger.info(f"更新分页控件 - 当前页: {self.current_page}, 总页数: {self.total_pages}")
        
        # 确保总页数至少为1
        total_pages = max(1, self.total_pages)
        
        if hasattr(self, 'prev_page_button'):
            self.prev_page_button.disabled = (self.current_page <= 1)
            logger.info(f"上一页按钮状态: {'启用' if self.current_page > 1 else '禁用'}")
        
        if hasattr(self, 'next_page_button'):
            self.next_page_button.disabled = (self.current_page >= total_pages)
            logger.info(f"下一页按钮状态: {'启用' if self.current_page < total_pages else '禁用'}")
        
        if hasattr(self, 'page_info_label'):
            self.page_info_label.text = f"第 {self.current_page} 页 / 共 {total_pages} 页"
            logger.info(f"页码信息: {self.page_info_label.text}")
    
    def go_to_prev_page(self, *args) -> None:
        """跳转到上一页"""
        if self.current_page > 1:
            self.current_page -= 1
            # 修复：区分筛选状态和正常状态的页面加载
            if self.filter_category or self.filter_subcategory or (hasattr(self, 'search_field') and self.search_field.text):
                # 筛选状态下，只需要更新显示，不需要重新加载
                self.update_documents_list()
            else:
                # 正常状态下，需要加载新页面的数据
                self.load_documents()
            self.update_pagination_controls()
    
    def go_to_next_page(self, *args) -> None:
        """跳转到下一页"""
        # 确保总页数至少为1
        total_pages = max(1, self.total_pages)
        if self.current_page < total_pages:
            self.current_page += 1
            # 修复：区分筛选状态和正常状态的页面加载
            if self.filter_category or self.filter_subcategory or (hasattr(self, 'search_field') and self.search_field.text):
                # 筛选状态下，只需要更新显示，不需要重新加载
                self.update_documents_list()
            else:
                # 正常状态下，需要加载新页面的数据
                self.load_documents()
            self.update_pagination_controls()
    
    def add_upload_card(self, parent_layout) -> None:
        """添加上传卡片（按照选择类型->选择子类->上传方式的顺序）"""
        upload_card = MDCard(
            orientation='vertical',
            size_hint_y=None,
            height=dp(160),  # 调整高度以适应内容
            md_bg_color=self.app.theme_cls.surfaceContainerColor if self.app else (0.9, 0.9, 0.9, 1),
            radius=[dp(12)],
            elevation=1,
            padding=[dp(16), dp(12), dp(16), dp(12)]
        )
        
        title_label = MDLabel(
            text="📤 文档上传",
            font_style="Title",
            role="small",
            theme_text_color="Primary",
            size_hint_y=None,
            height=dp(32)
        )
        
        # 第一行：选择类型和体检年度下拉菜单
        first_row = MDBoxLayout(
            orientation='horizontal',
            size_hint_y=None,
            height=dp(48),
            spacing=dp(8)  # 减小间距
        )
        
        category_button = MDButton(
            MDButtonText(text="选择类型", font_style="Label", role="medium"),  # 减小字体
            style="outlined",
            size_hint_x=0.5,
            size_hint_y=None,
            height=dp(36),  # 减小高度
            on_release=self.show_category_menu
        )
        self.category_button = category_button
        
        # 体检年度下拉菜单（近5年）- 初始隐藏
        year_button = MDButton(
            MDButtonText(text="选择年度", font_style="Label", role="medium"),  # 减小字体
            style="outlined",
            size_hint_x=0.5,
            size_hint_y=None,
            height=dp(36),  # 减小高度
            on_release=self.show_year_menu,
            opacity=0,  # 初始隐藏
            disabled=True
        )
        self.year_button = year_button
        
        first_row.add_widget(category_button)
        first_row.add_widget(year_button)
        
        # 第二行：选择子类、上传方式和操作按钮
        second_row = MDBoxLayout(
            orientation='horizontal',
            size_hint_y=None,
            height=dp(48),
            spacing=dp(8)  # 减小间距
        )
        
        subcategory_button = MDButton(
            MDButtonText(text="选择子类", font_style="Label", role="medium"),  # 减小字体
            style="outlined",
            size_hint_x=0.3,  # 减小宽度比例
            size_hint_y=None,
            height=dp(36),  # 减小高度
            disabled=True,
            on_release=self.show_subcategory_menu
        )
        self.subcategory_button = subcategory_button
        
        upload_method_button = MDButton(
            MDButtonText(text="上传方式", font_style="Label", role="medium"),  # 减小字体
            style="outlined",
            size_hint_x=0.35,  # 调整宽度比例
            size_hint_y=None,
            height=dp(36),  # 减小高度
            on_release=self.show_upload_method_menu
        )
        self.upload_method_button = upload_method_button
        
        upload_button = MDButton(
            MDButtonText(text="选择文件", font_style="Label", role="medium"),  # 减小字体
            style="outlined",
            size_hint_x=0.35,  # 调整宽度比例
            size_hint_y=None,
            height=dp(36),  # 减小高度
            disabled=True,
            on_release=self.start_upload
        )
        self.upload_button = upload_button
        
        second_row.add_widget(subcategory_button)
        second_row.add_widget(upload_method_button)
        second_row.add_widget(upload_button)
        
        # 组装卡片
        upload_card.add_widget(title_label)
        upload_card.add_widget(first_row)
        upload_card.add_widget(second_row)
        parent_layout.add_widget(upload_card)
        
        # 初始化年度菜单选项
        self.init_year_options()
    
    def init_year_options(self) -> None:
        """初始化年度选项（近5年）"""
        from datetime import datetime
        current_year = datetime.now().year
        self.year_options = [str(current_year - i) for i in range(5)]
        self.selected_year = None
    
    def select_year(self, year) -> None:
        """选择年度"""
        try:
            self.selected_year = year
            
            # 更新按钮文本
            if hasattr(self, 'year_button') and self.year_button:
                button_text = self.year_button.children[0]
                if hasattr(button_text, 'text'):
                    button_text.text = f"{year}年"
            
            # 关闭菜单
            if hasattr(self, 'year_menu') and self.year_menu:
                self.year_menu.dismiss()
                
            logger.info(f"已选择年度: {year}")
            
        except Exception as e:
            logger.error(f"选择年度失败: {e}")
            self.show_error("选择年度失败")
    
    def add_filter_card(self, parent_layout) -> None:
        """添加筛选卡片（支持自定义输入和下拉选择）"""
        filter_card = MDCard(
            orientation='vertical',
            size_hint_y=None,
            height=dp(140),  # 增加高度以容纳下拉菜单
            md_bg_color=self.app.theme_cls.surfaceContainerColor if self.app else (0.9, 0.9, 0.9, 1),
            radius=[dp(12)],
            elevation=1,
            padding=[dp(16), dp(12), dp(16), dp(12)]
        )
        
        title_label = MDLabel(
            text="🔍 搜索筛选",
            font_style="Title",
            role="small",
            theme_text_color="Primary",
            size_hint_y=None,
            height=dp(32)
        )
        
        # 第一行：搜索框和操作按钮
        first_row = MDBoxLayout(
            orientation='horizontal',
            size_hint_y=None,
            height=dp(48),
            spacing=dp(6)  # 减小间距
        )
        
        search_field = MDTextField(
            hint_text="搜索健康资料名称",
            mode="filled",
            size_hint_x=0.65,  # 增加搜索框宽度
            font_style="Body",
            role="small",
            on_text=self.on_search_text_change  # 修改为实时搜索
        )
        self.search_field = search_field
        
        clear_button = MDButton(
            MDButtonText(text="清空", font_style="Label", role="medium"),  # 减小字体
            style="outlined",
            size_hint_x=0.175,  # 减小宽度
            size_hint_y=None,
            height=dp(36),  # 减小高度
            on_release=self.clear_search
        )
        
        filter_button = MDButton(
            MDButtonText(text="筛选", font_style="Label", role="medium"),  # 减小字体
            style="outlined",
            size_hint_x=0.175,  # 减小宽度
            size_hint_y=None,
            height=dp(36),  # 减小高度
            on_release=self.apply_type_filters
        )
        
        first_row.add_widget(search_field)
        first_row.add_widget(clear_button)
        first_row.add_widget(filter_button)
        
        # 第二行：类型和子类型下拉菜单
        second_row = MDBoxLayout(
            orientation='horizontal',
            size_hint_y=None,
            height=dp(48),
            spacing=dp(6)  # 减小间距
        )
        
        # 类型下拉菜单
        type_button = MDButton(
            MDButtonText(text="选择类型", font_style="Label", role="medium"),  # 减小字体
            style="outlined",
            size_hint_x=0.5,
            size_hint_y=None,
            height=dp(36),  # 减小高度
            on_release=self.show_filter_type_menu
        )
        self.filter_type_button = type_button
        
        # 子类型下拉菜单
        subtype_button = MDButton(
            MDButtonText(text="选择子类型", font_style="Label", role="medium"),  # 减小字体
            style="outlined",
            size_hint_x=0.5,
            size_hint_y=None,
            height=dp(36),  # 减小高度
            disabled=True,
            on_release=self.show_filter_subtype_menu
        )
        self.filter_subtype_button = subtype_button
        
        second_row.add_widget(type_button)
        second_row.add_widget(subtype_button)
        
        filter_card.add_widget(title_label)
        filter_card.add_widget(first_row)
        filter_card.add_widget(second_row)
        parent_layout.add_widget(filter_card)
    
    def add_category_chips(self, parent_layout) -> None:
        """添加分类标签区域（优化：使用更小的Chip尺寸）"""
        scroll_view = MDScrollView(
            size_hint_y=None,
            height=dp(40),  # 进一步减小高度到40dp
            do_scroll_x=True,
            do_scroll_y=False
        )
        
        category_chips_container = MDBoxLayout(
            orientation="horizontal",
            size_hint_x=None,
            spacing=dp(6),  # 减小间距到6dp
            padding=[dp(8), dp(2), dp(8), dp(2)]  # 减小内边距
        )
        category_chips_container.bind(minimum_width=category_chips_container.setter('width'))
        
        self.category_chips_container = category_chips_container
        
        scroll_view.add_widget(category_chips_container)
        parent_layout.add_widget(scroll_view)
    
    def add_subcategory_chips(self, parent_layout) -> None:
        """添加子分类标签区域（优化：使用更小的Chip尺寸）"""
        scroll_view = MDScrollView(
            size_hint_y=None,
            height=dp(40) if self.filter_category else 0,  # 进一步减小高度到40dp
            do_scroll_x=True,
            do_scroll_y=False,
            opacity=1 if self.filter_category else 0
        )
        
        subcategory_chips_container = MDBoxLayout(
            orientation="horizontal",
            size_hint_x=None,
            spacing=dp(6),  # 减小间距到6dp
            padding=[dp(8), dp(2), dp(8), dp(2)]  # 减小内边距
        )
        subcategory_chips_container.bind(minimum_width=subcategory_chips_container.setter('width'))
        
        self.subcategory_chips_container = subcategory_chips_container
        
        scroll_view.add_widget(subcategory_chips_container)
        parent_layout.add_widget(scroll_view)
    
    def add_progress_card(self, parent_layout) -> None:
        """添加进度卡片"""
        progress_card = MDCard(
            id="progress_card",
            orientation='vertical',
            size_hint_y=None,
            height=0,  # 初始隐藏
            opacity=0,
            md_bg_color=self.app.theme_cls.surfaceContainerColor if self.app else (0.9, 0.9, 0.9, 1),
            radius=[dp(12)],
            elevation=1,
            padding=[dp(16), dp(12), dp(16), dp(12)]
        )
        self.ids.progress_card = progress_card  # 绑定ID

        progress_label = MDLabel(
            text="上传进度",
            font_style="Body",
            role="small",  # 减小字体
            theme_text_color="Primary",
            size_hint_y=None,
            height=dp(24)
        )

        progress_bar = ProgressBar(
            value=self.upload_progress,
            size_hint_y=None,
            height=dp(8)
        )
        self.progress_bar = progress_bar

        progress_card.add_widget(progress_label)
        progress_card.add_widget(progress_bar)
        parent_layout.add_widget(progress_card)
    
    def update_category_chips(self) -> None:
        """更新分类标签（适应KivyMD 2.0 API，移除filter类型Chip的前置图标，减小字体）"""
        self.category_chips_container.clear_widgets()
        for category, data in self.document_types.items():
            chip = MDChip(
                type="filter",
                active=False,  # 初始未激活
                on_active=lambda chip, active, cat=category: self.on_category_chip_active(chip, active, cat)
            )
            # 根据KivyMD 2.0.1 dev0规范，filter类型的Chip不应使用前置图标
            text_label = MDChipText(
                text=data["title"],
                font_style="Label",
                role="small"  # 使用更小的字体
            )
            chip.add_widget(text_label)
            self.category_chips_container.add_widget(chip)

    def update_subcategory_chips(self) -> None:
        """更新子分类标签（适应KivyMD 2.0 API，减小字体）"""
        self.subcategory_chips_container.clear_widgets()
        if self.filter_category:
            subcategories = self.document_types.get(self.filter_category, {}).get("subcategories", {})
            for subcat, title in subcategories.items():
                chip = MDChip(
                    type="filter",
                    active=False,
                    on_active=lambda chip, active, sub=subcat: self.on_subcategory_chip_active(chip, active, sub)
                )
                text_label = MDChipText(
                    text=title,
                    font_style="Label",
                    role="small"  # 使用更小的字体
                )
                chip.add_widget(text_label)
                self.subcategory_chips_container.add_widget(chip)

    def on_category_chip_active(self, chip, active, category) -> None:
        """处理分类芯片激活"""
        if active:
            self.filter_category = category
            self.update_subcategory_chips()
        else:
            self.filter_category = None
            self.subcategory_chips_container.clear_widgets()
        self.filter_documents()

    def on_subcategory_chip_active(self, chip, active, subcategory) -> None:
        """处理子分类芯片激活"""
        if active:
            self.filter_subcategory = subcategory
        else:
            self.filter_subcategory = None
        self.filter_documents()

    def show_category_menu(self, instance) -> None:
        """显示分类菜单"""
        try:
            menu_items = []
            for category, data in self.document_types.items():
                # 使用默认参数来正确捕获变量
                menu_items.append({
                    "text": data["title"],
                    "leading_icon": data["icon"],
                    "on_release": lambda cat=category: self.select_category(cat)
                })
            
            # 关闭之前的菜单
            if hasattr(self, 'category_menu') and self.category_menu:
                self.category_menu.dismiss()
                
            self.category_menu = MDDropdownMenu(
                caller=instance,
                items=menu_items,
                width=dp(240),
                position="center"  # 改为中心位置显示
            )
            self.category_menu.open()
        except Exception as e:
            logger.error(f"显示分类菜单失败: {e}")
            self.show_error("显示分类菜单失败")

    def select_category(self, category) -> None:
        """选择分类"""
        try:
            self.selected_category = category
            category_title = self.document_types[category]["title"]
            
            # 更新按钮文本
            if hasattr(self, 'category_button') and self.category_button:
                button_text = self.category_button.children[0]
                if hasattr(button_text, 'text'):
                    button_text.text = category_title
                    
            # 启用子类按钮并设置为active状态
            if hasattr(self, 'subcategory_button') and self.subcategory_button:
                self.subcategory_button.disabled = False
                # 重置子类按钮文本
                subcategory_text = self.subcategory_button.children[0]
                if hasattr(subcategory_text, 'text'):
                    subcategory_text.text = "选择子类"
                    
            # 重置子分类选择
            self.selected_subcategory = None
            
            # 重置上传按钮状态
            if hasattr(self, 'upload_button') and self.upload_button:
                self.upload_button.disabled = True
                button_text = self.upload_button.children[0]
                if hasattr(button_text, 'text'):
                    button_text.text = "开始上传"
            
            # 处理体检报告年度选择
            if hasattr(self, 'year_button') and self.year_button:
                if category == "physical_exam_reports":
                    # 显示并启用年度选择按钮
                    self.year_button.opacity = 1
                    self.year_button.disabled = False
                    # 重置年度选择
                    self.selected_year = None
                    button_text = self.year_button.children[0]
                    if hasattr(button_text, 'text'):
                        button_text.text = "选择年度"
                else:
                    # 隐藏年度选择按钮
                    self.year_button.opacity = 0
                    self.year_button.disabled = True
                    self.selected_year = None
                    button_text = self.year_button.children[0]
                    if hasattr(button_text, 'text'):
                        button_text.text = "选择年度"
            
            # 关闭菜单
            if hasattr(self, 'category_menu') and self.category_menu:
                self.category_menu.dismiss()
                
            logger.info(f"已选择分类: {category_title}")
            
        except Exception as e:
            logger.error(f"选择分类失败: {e}")
            self.show_error("选择分类失败")

    def show_subcategory_menu(self, instance) -> None:
        """显示子分类菜单"""
        if not self.selected_category:
            self.show_info("请先选择文档类型")
            return
            
        try:
            if self.selected_category:
                subcategories = self.document_types[self.selected_category]["subcategories"]
                menu_items = []
                for subcat, title in subcategories.items():
                    # 使用默认参数来正确捕获变量
                    menu_items.append({
                        "text": title,
                        "on_release": lambda sub=subcat: self.select_subcategory(sub)
                    })
                
                # 关闭之前的菜单
                if hasattr(self, 'subcategory_menu') and self.subcategory_menu:
                    self.subcategory_menu.dismiss()
                    
                self.subcategory_menu = MDDropdownMenu(
                    caller=instance,
                    items=menu_items,
                    width=dp(240),
                    position="center"  # 改为中心位置显示
                )
                self.subcategory_menu.open()
        except Exception as e:
            logger.error(f"显示子分类菜单失败: {e}")
            self.show_error("显示子分类菜单失败")

    def select_subcategory(self, subcategory) -> None:
        """选择子分类"""
        try:
            self.selected_subcategory = subcategory
            if self.selected_category:
                subcategories = self.document_types[self.selected_category]["subcategories"]
                subcategory_title = subcategories[subcategory]
                
                # 更新按钮文本
                if hasattr(self, 'subcategory_button') and self.subcategory_button:
                    button_text = self.subcategory_button.children[0]
                    if hasattr(button_text, 'text'):
                        button_text.text = subcategory_title
                        
                # 启用上传按钮（如果已选择上传方式）
                if hasattr(self, 'upload_button') and self.upload_button:
                    # 检查是否已选择分类和子分类
                    if self.selected_category and self.selected_subcategory:
                        self.upload_button.disabled = False
                        # 根据上传方式更新按钮文本
                        button_text = self.upload_button.children[0]
                        if hasattr(button_text, 'text'):
                            if self.selected_upload_method == "direct_upload":
                                button_text.text = "选择文件"
                            elif self.selected_upload_method == "qr_upload":
                                button_text.text = "扫描二维码"
                            elif self.selected_upload_method == "camera_upload":
                                button_text.text = "拍照上传"
                            else:
                                button_text.text = "开始上传"
                    else:
                        # 如果还没有选择分类或子分类，禁用上传按钮
                        self.upload_button.disabled = True
                        button_text = self.upload_button.children[0]
                        if hasattr(button_text, 'text'):
                            button_text.text = "开始上传"
                        
                # 关闭菜单
                if hasattr(self, 'subcategory_menu') and self.subcategory_menu:
                    self.subcategory_menu.dismiss()
                    
                logger.info(f"已选择子分类: {subcategory_title}")
            
        except Exception as e:
            logger.error(f"选择子分类失败: {e}")
            self.show_error("选择子分类失败")

    def show_upload_method_menu(self, instance) -> None:
        """显示上传方式菜单"""
        try:
            menu_items = []
            for method, data in self.upload_methods.items():
                # 使用默认参数来正确捕获变量
                menu_items.append({
                    "text": data["title"],
                    "leading_icon": data["icon"],
                    "on_release": lambda m=method: self.select_upload_method(m)
                })
            
            # 关闭之前的菜单
            if hasattr(self, 'upload_method_menu') and self.upload_method_menu:
                self.upload_method_menu.dismiss()
                
            self.upload_method_menu = MDDropdownMenu(
                caller=instance,
                items=menu_items,
                width=dp(240),
                position="center"  # 改为中心位置显示
            )
            self.upload_method_menu.open()
        except Exception as e:
            logger.error(f"显示上传方式菜单失败: {e}")
            self.show_error("显示上传方式菜单失败")

    def select_upload_method(self, method) -> None:
        """选择上传方式"""
        try:
            self.selected_upload_method = method
            upload_method_title = self.upload_methods[method]["title"]
            
            # 更新按钮文本
            if hasattr(self, 'upload_method_button') and self.upload_method_button:
                button_text = self.upload_method_button.children[0]
                if hasattr(button_text, 'text'):
                    button_text.text = upload_method_title
                    
            # 启用上传按钮（如果已选择分类和子分类）
            if hasattr(self, 'upload_button') and self.upload_button:
                # 检查是否已选择分类和子分类
                if self.selected_category and self.selected_subcategory:
                    self.upload_button.disabled = False
                    # 根据上传方式更新按钮文本
                    button_text = self.upload_button.children[0]
                    if hasattr(button_text, 'text'):
                        if self.selected_upload_method == "direct_upload":
                            button_text.text = "选择文件"
                        elif self.selected_upload_method == "qr_upload":
                            button_text.text = "扫描二维码"
                        elif self.selected_upload_method == "camera_upload":
                            button_text.text = "拍照上传"
                        else:
                            button_text.text = "开始上传"
                else:
                    # 如果还没有选择分类或子分类，禁用上传按钮
                    self.upload_button.disabled = True
                    button_text = self.upload_button.children[0]
                    if hasattr(button_text, 'text'):
                        button_text.text = "开始上传"
                        
            # 关闭菜单
            if hasattr(self, 'upload_method_menu') and self.upload_method_menu:
                self.upload_method_menu.dismiss()
                
            logger.info(f"已选择上传方式: {upload_method_title}")
            
        except Exception as e:
            logger.error(f"选择上传方式失败: {e}")
            self.show_error("选择上传方式失败")

    def show_year_menu(self, instance) -> None:
        """显示年度选择菜单"""
        try:
            menu_items = []
            for year in self.year_options:
                # 使用默认参数来正确捕获变量
                menu_items.append({
                    "text": year,
                    "on_release": lambda y=year: self.select_year(y)
                })
            
            # 关闭之前的菜单
            if hasattr(self, 'year_menu') and self.year_menu:
                self.year_menu.dismiss()
                
            self.year_menu = MDDropdownMenu(
                caller=instance,
                items=menu_items,
                width=dp(120),
                position="center"  # 统一使用center位置
            )
            self.year_menu.open()
        except Exception as e:
            logger.error(f"显示年度菜单失败: {e}")
            self.show_error("显示年度菜单失败")

    def show_filter_type_menu(self, instance) -> None:
        """显示筛选类型菜单"""
        try:
            menu_items = [
                {
                    "text": "全部类型",
                    "on_release": lambda: self.select_filter_type(None)
                }
            ]
            
            # 添加所有文档类型
            for category, data in self.document_types.items():
                # 使用默认参数来正确捕获变量
                menu_items.append({
                    "text": data["title"],
                    "on_release": lambda cat=category: self.select_filter_type(cat)
                })
            
            # 关闭之前的菜单
            if hasattr(self, 'filter_type_menu') and self.filter_type_menu:
                self.filter_type_menu.dismiss()
            
            self.filter_type_menu = MDDropdownMenu(
                caller=instance,
                items=menu_items,
                width=dp(240),
                position="center"  # 统一使用center位置
            )
            self.filter_type_menu.open()
        except Exception as e:
            logger.error(f"显示筛选类型菜单失败: {e}")
            self.show_error("显示筛选类型菜单失败")

    def show_filter_subtype_menu(self, instance) -> None:
        """显示筛选子类型菜单"""
        if not self.filter_category:
            self.show_info("请先选择类型")
            return
            
        try:
            subcategories = self.document_types[self.filter_category]["subcategories"]
            menu_items = [
                {
                    "text": "全部子类型",
                    "on_release": lambda: self.select_filter_subtype(None)
                }
            ]
            
            # 添加所有子类型
            for subcat, title in subcategories.items():
                # 使用默认参数来正确捕获变量
                menu_items.append({
                    "text": title,
                    "on_release": lambda sub=subcat: self.select_filter_subtype(sub)
                })
            
            # 关闭之前的菜单
            if hasattr(self, 'filter_subtype_menu') and self.filter_subtype_menu:
                self.filter_subtype_menu.dismiss()
                
            self.filter_subtype_menu = MDDropdownMenu(
                caller=instance,
                items=menu_items,
                width=dp(240),
                position="center"  # 统一使用center位置
            )
            self.filter_subtype_menu.open()
        except Exception as e:
            logger.error(f"显示筛选子类型菜单失败: {e}")
            self.show_error("显示筛选子类型菜单失败")

    # 已移除重复的show_filter_options方法定义
    pass

    def on_search_text_change(self, instance, value) -> None:
        """搜索文本变化时实时过滤文档"""
        # 添加轻微延迟以避免过于频繁的搜索
        if hasattr(self, '_search_timer'):
            self._search_timer.cancel()
        
        from kivy.clock import Clock
        self._search_timer = Clock.schedule_once(lambda dt: self.filter_documents(), 0.3)
    
    def on_filter_documents(self, instance) -> None:
        """筛选文档（已修改为直接应用类型筛选而不弹出对话框）"""
        # 直接应用当前的类型筛选条件，而不是弹出对话框
        self.filter_documents()

    def apply_type_filters(self, *args) -> None:
        """应用类型筛选（直接触发筛选而不弹出对话框）"""
        self.filter_documents()
    
    def show_filter_options(self, *args) -> None:
        """显示筛选选项（已弃用，保持空实现以避免调用错误）"""
        pass
    
    def set_filter_category(self, checkbox, value, category) -> None:
        """设置筛选分类（已弃用，保持空实现以避免调用错误）"""
        pass
    
    def apply_filters(self) -> None:
        """应用筛选（已弃用，保持空实现以避免调用错误）"""
        pass

    def _update_documents(self, documents) -> None:
        """更新文档列表"""
        # 过滤和验证文档数据
        valid_documents = []
        total_count = 0
        docs_data = []  # 确保docs_data在所有情况下都定义
    
        if documents:
            # 处理不同格式的响应数据
            if isinstance(documents, dict):
                if documents.get("status") == "success" and "data" in documents:
                    data = documents["data"]
                    if "documents" in data:
                        docs_data = data["documents"]
                    elif "records" in data:
                        docs_data = data["records"]
                    elif isinstance(data, list):
                        docs_data = data
                
                    # 获取总文档数用于分页
                    total_count = data.get("total", len(docs_data)) if isinstance(data, dict) else len(docs_data)
                elif "documents" in documents:
                    docs_data = documents["documents"]
                    total_count = documents.get("total", len(docs_data))
                elif "records" in documents:
                    docs_data = documents["records"]
                    total_count = documents.get("total", len(docs_data))
            elif isinstance(documents, list):
                docs_data = documents
                total_count = len(documents)
        else:
            # 如果documents为None或空，使用空列表
            logger.info("没有收到文档数据，使用空列表")
            docs_data = []
            total_count = 0
            
        logger.info(f"解析得到 {len(docs_data)} 个文档，总数: {total_count}, 当前页: {self.current_page}")
    
        # 处理每个文档，确保字段格式正确
        for i, doc in enumerate(docs_data):
            normalized_doc = self._normalize_document(doc)
            if normalized_doc:
                valid_documents.append(normalized_doc)
    
        # 修复累积逻辑：正确处理文档列表的累积而不是替换
        if self.current_page == 1:
            # 第一页：清空现有文档列表并设置新文档
            self.documents = valid_documents
            self.total_document_count = total_count
            logger.info("第一页加载，清空现有文档列表")
        else:
            # 后续页：累积添加新文档，避免重复
            for doc in valid_documents:
                doc_id = doc.get('id') or doc.get('document_id')
                # 检查文档是否已存在（避免重复）
                if doc_id and not any(existing.get('id') == doc_id or existing.get('document_id') == doc_id for existing in self.documents):
                    self.documents.append(doc)
                    logger.debug(f"累积添加文档: {doc.get('title', '未知标题')}")
            
            # 更新总文档数（使用服务器返回的总数）
            if total_count > self.total_document_count:
                self.total_document_count = total_count
        
        # 应用当前过滤器到所有文档
        self.apply_filters_to_all_documents()
        
        # 更新分页信息 - 确保正确计算总页数
        self.total_pages = max(1, (self.total_document_count + self.page_size - 1) // self.page_size) if self.total_document_count > 0 else 1
        logger.info(f"更新分页信息 - 文档总数: {self.total_document_count}, 总页数: {self.total_pages}, 当前页: {self.current_page}")
        self.update_pagination_controls()
    
        self.update_documents_list()
        self.is_loading = False

    def apply_filters_to_all_documents(self) -> None:
        """应用过滤器到所有文档"""
        try:
            search_text = self.search_field.text.lower() if hasattr(self, 'search_field') and self.search_field.text else ""
            
            self.filtered_documents = []
            for doc in self.documents:
                # 跳过空文档
                if not doc or not isinstance(doc, dict):
                    continue
                    
                # 检查搜索文本 - 使用正确的字段名
                doc_title = doc.get('title', '') or doc.get('file_name', '') or ''
                if search_text and search_text not in doc_title.lower():
                    continue
                
                # 检查分类筛选 - 使用正确的字段名
                if self.filter_category and doc.get('document_type') != self.filter_category:
                    continue
                
                # 检查子分类筛选 - 使用正确的字段名
                if self.filter_subcategory and doc.get('type') != self.filter_subcategory:
                    continue
                
                self.filtered_documents.append(doc)
            
            logger.info(f"应用过滤器后文档数量: {len(self.filtered_documents)}")
            
        except Exception as e:
            logger.error(f"应用过滤器到所有文档失败: {e}")
            self.filtered_documents = self.documents.copy()

    def filter_documents(self, *args) -> None:
        """过滤文档"""
        try:
            # 应用过滤器到所有文档
            self.apply_filters_to_all_documents()
            
            # 修复分页计算：区分筛选状态和正常状态的分页逻辑
            if self.filter_category or self.filter_subcategory or (hasattr(self, 'search_field') and self.search_field.text):
                # 有筛选条件时，基于筛选后的文档数量计算页数
                self.total_pages = max(1, (len(self.filtered_documents) + self.page_size - 1) // self.page_size) if len(self.filtered_documents) > 0 else 1
                logger.info(f"筛选模式 - 筛选后文档数量: {len(self.filtered_documents)}, 总页数: {self.total_pages}")
            else:
                # 无筛选条件时，基于总文档数量计算页数
                self.total_pages = max(1, (self.total_document_count + self.page_size - 1) // self.page_size) if self.total_document_count > 0 else 1
                logger.info(f"正常模式 - 总文档数量: {self.total_document_count}, 总页数: {self.total_pages}")
            
            # 重置到第一页并更新分页信息
            self.current_page = 1
            self.update_pagination_controls()
            
            self.update_documents_list()
            
        except Exception as e:
            logger.error(f"过滤文档失败: {e}")
            self.filtered_documents = self.documents.copy()
            self.update_documents_list()

    def update_documents_list(self) -> None:
        """更新文档列表显示（确保清空并添加，确保高度更新）"""
        try:
            if hasattr(self, 'documents_container'):
                self.documents_container.clear_widgets()
                
                # 计算当前页的文档范围
                start_index = (self.current_page - 1) * self.page_size
                end_index = start_index + self.page_size
                page_documents = self.filtered_documents[start_index:end_index]
                
                logger.info(f"更新文档列表显示 - 当前页: {self.current_page}, 文档总数: {len(self.filtered_documents)}, 本页显示: {len(page_documents)}")
                
                for i, doc in enumerate(page_documents):
                    try:
                        # 文档数据已经在_update_documents中验证过，直接使用
                        card = DocumentCard(
                            title=doc.get('title', '未知标题'),
                            date=doc.get('date', '未知日期'),
                            summary=doc.get('summary', ''),
                            icon=doc.get('icon', 'file-document'),
                            document_id=doc.get('id', ''),
                            document_type=doc.get('document_type', ''),
                            document_data=doc.get('document_data', {})
                        )
                        card.parent_screen = self
                        self.documents_container.add_widget(card)
                        logger.debug(f"添加文档卡片 {i+1}: {doc.get('title', '未知标题')}")
                    except Exception as e:
                        logger.error(f"添加文档卡片时出错: {e}")
                        continue
                
                # 强制更新布局
                self.documents_container.do_layout()
                
                # 记录更新完成
                logger.info(f"文档列表更新完成，添加了 {len(page_documents)} 个文档卡片")
            else:
                logger.error("documents_container 未初始化，无法更新文档列表")
        except Exception as e:
            logger.error(f"更新文档列表时出错: {e}")
            import traceback
            logger.error(traceback.format_exc())

    def clear_search(self, *args) -> None:
        """清空搜索和筛选"""
        try:
            # 清空搜索框
            if hasattr(self, 'search_field'):
                self.search_field.text = ""
            
            # 重置筛选条件
            self.filter_category = None
            self.filter_subcategory = None
            
            # 重置筛选按钮
            if hasattr(self, 'filter_type_button') and self.filter_type_button:
                button_text = self.filter_type_button.children[0]
                if hasattr(button_text, 'text'):
                    button_text.text = "选择类型"
            
            if hasattr(self, 'filter_subtype_button') and self.filter_subtype_button:
                self.filter_subtype_button.disabled = True
                button_text = self.filter_subtype_button.children[0]
                if hasattr(button_text, 'text'):
                    button_text.text = "选择子类型"
            
            # 重新应用筛选
            self.filter_documents()
            
            # 重置到第一页
            self.current_page = 1
            self.update_pagination_controls()
            
        except Exception as e:
            logger.error(f"清空搜索失败: {e}")
            self.show_error("清空搜索失败")

    def load_documents(self, force_refresh=False) -> None:
        """加载文档"""
        self.is_loading = True
        threading.Thread(target=self._load_documents_thread).start()

    def _load_documents_thread(self) -> None:
        """后台加载文档"""
        try:
            user_manager = get_user_manager()
            user = user_manager.get_current_user()
            custom_id = getattr(user, 'custom_id', None) if user else None
            if not custom_id:
                Clock.schedule_once(lambda dt: self.show_error("未获取到有效的custom_id，无法加载文档"))
                return
            cloud_api = get_cloud_api()
            # 使用分页参数加载文档
            documents = cloud_api.get_documents(
                page=self.current_page,
                page_size=self.page_size,
                custom_id=custom_id
            )
            logger.info(f"加载文档 - 页码: {self.current_page}, 每页大小: {self.page_size}, 用户ID: {custom_id}")
            Clock.schedule_once(lambda dt: self._update_documents(documents))
        except Exception as e:
            logger.error(f"加载文档出错: {e}")
            Clock.schedule_once(lambda dt: self.show_error("加载文档失败"))

    def clear_selection(self) -> None:
        """清除选择状态"""
        try:
            self.selected_category = None
            self.selected_subcategory = None
            self.selected_upload_method = "direct_upload"
            self.selected_year = None
            
            # 重置分类按钮
            if hasattr(self, 'category_button') and self.category_button:
                button_text = self.category_button.children[0]
                if hasattr(button_text, 'text'):
                    button_text.text = "选择类型"
                    
            # 重置子类按钮
            if hasattr(self, 'subcategory_button') and self.subcategory_button:
                self.subcategory_button.disabled = True
                button_text = self.subcategory_button.children[0]
                if hasattr(button_text, 'text'):
                    button_text.text = "选择子类"
                    
            # 重置上传方式按钮
            if hasattr(self, 'upload_method_button') and self.upload_method_button:
                button_text = self.upload_method_button.children[0]
                if hasattr(button_text, 'text'):
                    button_text.text = "上传方式"
                    
            # 重置上传按钮
            if hasattr(self, 'upload_button') and self.upload_button:
                self.upload_button.disabled = True
                button_text = self.upload_button.children[0]
                if hasattr(button_text, 'text'):
                    button_text.text = "开始上传"
                    
            # 重置年度按钮
            if hasattr(self, 'year_button') and self.year_button:
                self.year_button.opacity = 0
                self.year_button.disabled = True
                button_text = self.year_button.children[0]
                if hasattr(button_text, 'text'):
                    button_text.text = "选择年度"
                
            logger.info("已清除所有选择状态")
            
        except Exception as e:
            logger.error(f"清除选择状态失败: {e}")

    def start_upload(self, *args) -> None:
        """开始上传"""
        if not self.selected_category or not self.selected_subcategory:
            self.show_error("请先选择分类和子分类")
            return

        # 检查体检报告年度选择
        if self.selected_category == "physical_exam_reports":
            if not self.selected_year:
                self.show_error("请选择体检年度")
                return

        # 准备元数据
        metadata = {
            'category': self.selected_category,
            'subcategory': self.selected_subcategory,
            'upload_method': self.selected_upload_method
        }
        
        # 添加体检年度信息
        if self.selected_category == "physical_exam_reports":
            metadata['exam_year'] = self.selected_year

        try:
            # 根据上传方式执行相应操作
            if self.selected_upload_method == "direct_upload":
                self._handle_direct_upload(metadata)
            elif self.selected_upload_method == "qr_upload":
                self._handle_qr_upload(metadata)
            elif self.selected_upload_method == "camera_upload":
                self._handle_camera_upload(metadata)
            else:
                self.show_error("不支持的上传方式")
                
        except Exception as e:
            logger.error(f"开始上传失败: {e}")
            self.show_error(f"开始上传失败: {str(e)}")

    def _handle_direct_upload(self, metadata) -> None:
        """处理直接文件上传"""
        try:
            # 检查文件上传管理器是否可用
            if not self.file_upload_manager:
                self.show_error("文件上传管理器不可用")
                return
                
            # 存储当前上传的元数据，供文件选择回调使用
            self._current_upload_metadata = metadata
                
            # 尝试打开文件管理器
            if hasattr(self.file_upload_manager, 'open_file_manager'):
                success = self.file_upload_manager.open_file_manager()
                if success:
                    self.show_info("请选择要上传的文件")
                else:
                    self.show_error("无法打开文件管理器")
            else:
                # 如果没有文件管理器，使用备用方法
                self._show_file_selection_dialog(metadata)
                
        except Exception as e:
            logger.error(f"处理直接上传失败: {e}")
            self.show_error("文件选择失败")

    def _show_file_selection_dialog(self, metadata) -> None:
        """显示文件选择对话框（备用方法）"""
        try:
            from kivy.uix.filechooser import FileChooserListView
            from kivymd.uix.dialog import MDDialog
            from kivymd.uix.button import MDButton, MDButtonText
            
            content = MDBoxLayout(
                orientation='vertical',
                size_hint_y=None,
                height=dp(400),
                padding=[dp(16), dp(16), dp(16), dp(16)]
            )
            
            # 添加说明标签
            info_label = MDLabel(
                text="请选择要上传的文件",
                font_style="Body",
                role="medium",
                size_hint_y=None,
                height=dp(32)
            )
            content.add_widget(info_label)
            
            # 添加文件选择器
            file_chooser = FileChooserListView(
                path=os.path.expanduser("~"),
                filters=['*.pdf', '*.jpg', '*.jpeg', '*.png', '*.doc', '*.docx']
            )
            content.add_widget(file_chooser)
            
            def on_file_selected(instance):
                selected_files = file_chooser.selection
                if selected_files:
                    file_path = selected_files[0]
                    self._on_file_selected(file_path, metadata)
                if dialog:
                    dialog.dismiss()
            
            def on_cancel(instance):
                if dialog:
                    dialog.dismiss()
            
            # 使用KivyMD 2.0.1 dev0规范创建对话框
            from kivymd.uix.dialog import (
                MDDialog,
                MDDialogHeadlineText,
                MDDialogContentContainer,
                MDDialogButtonContainer
            )
            from kivymd.uix.button import MDButton, MDButtonText
            
            dialog = MDDialog(
                MDDialogHeadlineText(
                    text="选择文件"
                ),
                MDDialogContentContainer(
                    content
                ),
                MDDialogButtonContainer(
                    MDButton(
                        MDButtonText(text="取消"),
                        style="text",
                        on_release=on_cancel
                    ),
                    MDButton(
                        MDButtonText(text="选择"),
                        style="text",
                        on_release=on_file_selected
                    )
                )
            )
            dialog.open()
            
        except Exception as e:
            logger.error(f"显示文件选择对话框失败: {e}")
            self.show_error("文件选择功能不可用")

    def _handle_qr_upload(self, metadata) -> None:
        """处理二维码上传"""
        try:
            # 显示二维码扫描对话框
            content = MDBoxLayout(
                orientation='vertical',
                size_hint_y=None,
                height=dp(200),
                padding=[dp(16), dp(16), dp(16), dp(16)]
            )
            
            info_label = MDLabel(
                text="请扫描二维码获取文档链接",
                font_style="Body",
                role="medium",
                size_hint_y=None,
                height=dp(32)
            )
            content.add_widget(info_label)
            
            # 这里可以集成二维码扫描功能
            # 暂时显示提示信息
            scan_label = MDLabel(
                text="二维码扫描功能正在开发中...",
                font_style="Body",
                role="small",
                theme_text_color="Secondary",
                size_hint_y=None,
                height=dp(32)
            )
            content.add_widget(scan_label)
            
            def on_scan(instance):
                # 模拟扫描结果
                self.show_info("二维码扫描功能待实现")
                if dialog:
                    dialog.dismiss()
            
            def on_cancel(instance):
                if dialog:
                    dialog.dismiss()
            
            # 使用KivyMD 2.0.1 dev0规范创建对话框
            from kivymd.uix.dialog import (
                MDDialog,
                MDDialogHeadlineText,
                MDDialogContentContainer,
                MDDialogButtonContainer
            )
            from kivymd.uix.button import MDButton, MDButtonText
            
            dialog = MDDialog(
                MDDialogHeadlineText(
                    text="二维码扫描"
                ),
                MDDialogContentContainer(
                    content
                ),
                MDDialogButtonContainer(
                    MDButton(
                        MDButtonText(text="取消"),
                        style="text",
                        on_release=on_cancel
                    ),
                    MDButton(
                        MDButtonText(text="扫描"),
                        style="text",
                        on_release=on_scan
                    )
                )
            )
            dialog.open()
            
        except Exception as e:
            logger.error(f"二维码上传处理失败: {e}")
            self.show_error("二维码上传功能不可用")

    def _handle_camera_upload(self, metadata) -> None:
        """处理拍照上传"""
        try:
            # 显示拍照对话框
            content = MDBoxLayout(
                orientation='vertical',
                size_hint_y=None,
                height=dp(200),
                padding=[dp(16), dp(16), dp(16), dp(16)]
            )
            
            info_label = MDLabel(
                text="请使用摄像头拍照上传",
                font_style="Body",
                role="medium",
                size_hint_y=None,
                height=dp(32)
            )
            content.add_widget(info_label)
            
            # 这里可以集成摄像头功能
            # 暂时显示提示信息
            camera_label = MDLabel(
                text="摄像头拍照功能正在开发中...",
                font_style="Body",
                role="small",
                theme_text_color="Secondary",
                size_hint_y=None,
                height=dp(32)
            )
            content.add_widget(camera_label)
            
            def on_capture(instance):
                # 模拟拍照结果
                self.show_info("摄像头拍照功能待实现")
                if dialog:
                    dialog.dismiss()
            
            def on_cancel(instance):
                if dialog:
                    dialog.dismiss()
            
            # 使用KivyMD 2.0.1 dev0规范创建对话框
            from kivymd.uix.dialog import (
                MDDialog,
                MDDialogHeadlineText,
                MDDialogContentContainer,
                MDDialogButtonContainer
            )
            from kivymd.uix.button import MDButton, MDButtonText
            
            dialog = MDDialog(
                MDDialogHeadlineText(
                    text="拍照上传"
                ),
                MDDialogContentContainer(
                    content
                ),
                MDDialogButtonContainer(
                    MDButton(
                        MDButtonText(text="取消"),
                        style="text",
                        on_release=on_cancel
                    ),
                    MDButton(
                        MDButtonText(text="拍照"),
                        style="text",
                        on_release=on_capture
                    )
                )
            )
            dialog.open()
            
        except Exception as e:
            logger.error(f"拍照上传处理失败: {e}")
            self.show_error("拍照上传功能不可用")

    def _on_file_selected_handler(self, instance, file_path) -> None:
        """文件选择事件处理器（统一入口）"""
        try:
            # 使用存储的元数据
            metadata = self._current_upload_metadata
            if not metadata:
                logger.warning("没有找到上传元数据，可能是重复的文件选择事件")
                return
                
            # 清除元数据，防止重复使用
            self._current_upload_metadata = None
            
            # 调用实际的文件处理方法
            self._on_file_selected(file_path, metadata)
            
        except Exception as e:
            logger.error(f"处理文件选择事件失败: {e}")
            self.show_error("处理文件选择失败")
    
    def _on_file_selected(self, file_path, metadata) -> None:
        """处理文件选择后的上传"""
        try:
            if not file_path or not os.path.exists(file_path):
                self.show_error("选择的文件不存在")
                return
                
            # 显示上传进度
            self.show_upload_progress(True)
            
            # 使用文件上传管理器上传文件
            if hasattr(self.file_upload_manager, 'upload_file') and self.file_upload_manager:
                self.file_upload_manager.upload_file(
                    file_path=file_path,
                    metadata=metadata
                )
            else:
                # 如果没有上传方法，显示错误
                self.show_upload_progress(False)
                self.show_error("文件上传功能不可用")
                
        except Exception as e:
            self.show_upload_progress(False)
            logger.error(f"文件上传失败: {str(e)}")
            self.show_error(f"文件上传失败: {str(e)}")

    def show_upload_options(self) -> None:
        """显示上传选项（保留兼容性）"""
        self.start_upload()

    def open_camera(self, *args) -> None:
        """打开相机"""
        if self.dialog:
            self.dialog.dismiss()
        self.show_info("相机功能待实现")

    def scan_qr_code(self, *args) -> None:
        """扫描二维码"""
        if self.dialog:
            self.dialog.dismiss()
        self.show_info("二维码扫描功能待实现")



    def select_filter_type(self, category) -> None:
        """选择筛选类型"""
        try:
            self.filter_category = category
            
            # 更新按钮文本
            if hasattr(self, 'filter_type_button') and self.filter_type_button:
                button_text = self.filter_type_button.children[0]
                if hasattr(button_text, 'text'):
                    if category:
                        button_text.text = self.document_types[category]["title"]
                    else:
                        button_text.text = "选择类型"
            
            # 重置子类型筛选
            self.filter_subcategory = None
            if hasattr(self, 'filter_subtype_button') and self.filter_subtype_button:
                self.filter_subtype_button.disabled = not bool(category)
                button_text = self.filter_subtype_button.children[0]
                if hasattr(button_text, 'text'):
                    button_text.text = "选择子类型"
            
            # 关闭菜单
            if hasattr(self, 'filter_type_menu') and self.filter_type_menu:
                self.filter_type_menu.dismiss()
            
            # 应用筛选
            self.filter_documents()
            
        except Exception as e:
            logger.error(f"选择筛选类型失败: {e}")
            self.show_error("选择筛选类型失败")



    def select_filter_subtype(self, subcategory) -> None:
        """选择筛选子类型"""
        try:
            self.filter_subcategory = subcategory
            
            # 更新按钮文本
            if hasattr(self, 'filter_subtype_button') and self.filter_subtype_button:
                button_text = self.filter_subtype_button.children[0]
                if hasattr(button_text, 'text'):
                    if subcategory and self.filter_category:
                        subcategories = self.document_types[self.filter_category]["subcategories"]
                        button_text.text = subcategories[subcategory]
                    else:
                        button_text.text = "选择子类型"
            
            # 关闭菜单
            if hasattr(self, 'filter_subtype_menu') and self.filter_subtype_menu:
                self.filter_subtype_menu.dismiss()
            
            # 应用筛选
            self.filter_documents()
            
        except Exception as e:
            logger.error(f"选择筛选子类型失败: {e}")
            self.show_error("选择筛选子类型失败")

    def show_upload_progress(self, show=True) -> None:
        """显示或隐藏上传进度"""
        try:
            if hasattr(self, 'ids') and hasattr(self.ids, 'progress_card'):
                progress_card = self.ids.progress_card
                if show:
                    progress_card.opacity = 1
                    progress_card.height = dp(60)
                else:
                    progress_card.opacity = 0
                    progress_card.height = 0

            # 控制上传按钮状态
            if hasattr(self, 'upload_button') and self.upload_button:
                button_text = self.upload_button.children[0]
                if hasattr(button_text, 'text'):
                    if show:
                        button_text.text = "上传中..."
                        self.upload_button.disabled = True
                    else:
                        button_text.text = "开始上传"
                        self.upload_button.disabled = False
                        
        except Exception as e:
            logger.error(f"更新上传进度失败: {e}")

    def show_success(self, message) -> None:
        """显示成功信息提示"""
        try:
            from kivymd.uix.snackbar import MDSnackbar, MDSnackbarText
            MDSnackbar(
                MDSnackbarText(
                    text=message,
                ),
                y=dp(24),
                pos_hint={"center_x": 0.5},
                duration=2,
                md_bg_color=self.app.theme_cls.primaryColor if self.app else (0.2, 0.6, 1, 1),
            ).open()
        except Exception as e:
            logger.error(f"显示成功提示失败: {e}")
            # 降级为普通提示
            self.show_info(message)

    def on_upload_complete(self, instance, success, result) -> None:
        """文件上传完成回调"""
        try:
            if success:
                logger.info(f"文件上传成功: {result}")
                self.show_upload_progress(False)
                self.show_success("文件上传成功！")
                # 重新加载文档列表
                self.load_documents(force_refresh=True)
                # 清除选择状态
                self.clear_selection()
            else:
                logger.error(f"文件上传失败: {result}")
                self.show_upload_progress(False)
                self.show_error(f"文件上传失败: {result}")
        except Exception as e:
            logger.error(f"处理上传完成回调失败: {e}")
            self.show_upload_progress(False)
            self.show_error("处理上传结果失败")

    def on_upload_progress(self, instance, progress) -> None:
        """文件上传进度回调"""
        try:
            if hasattr(self, 'progress_bar') and self.progress_bar:
                self.progress_bar.value = progress
                logger.debug(f"上传进度: {progress}%")
        except Exception as e:
            logger.error(f"更新上传进度失败: {e}")

    def on_upload_error(self, instance, error_message) -> None:
        """文件上传错误回调"""
        try:
            logger.error(f"文件上传错误: {error_message}")
            self.show_upload_progress(False)
            self.show_error(f"上传错误: {error_message}")
        except Exception as e:
            logger.error(f"处理上传错误回调失败: {e}")
            self.show_upload_progress(False)
            self.show_error("处理上传错误失败")

    def on_view_document(self, instance, document_id, document_type) -> None:
        """查看文档"""
        # 强制document_id为str
        document_id = str(document_id) if document_id is not None else ""
        logger.info(f"查看文档: {document_id}, 类型: {document_type}")

        try:
            # 获取文档数据
            document_data = None
            for doc in self.documents:
                doc_id = str(doc.get('id', '')) if doc.get('id') is not None else ""
                if doc_id == document_id:
                    document_data = doc
                    break

            if not document_data:
                self.show_error("未找到文档数据")
                return

            # 显示加载对话框
            self.show_loading_dialog("正在加载文档...")

            # 在后台线程中下载文档
            threading.Thread(target=self._download_document_thread, args=(document_data,)).start()
        except Exception as e:
            logger.error(f"查看文档时出错: {e}")
            import traceback
            logger.error(traceback.format_exc())
            self.show_error("查看文档时出错")

    def _download_document_thread(self, document_data) -> None:
        """后台线程下载文档"""
        try:
            # 获取云API实例
            cloud_api = get_cloud_api()

            # 获取文档ID
            document_id = document_data.get('id')
            if not document_id:
                Clock.schedule_once(lambda dt: self.show_error("文档ID无效"))
                Clock.schedule_once(lambda dt: self.dismiss_loading_dialog())
                return

            logger.info(f"准备下载文档 ID: {document_id}")
            logger.info(f"文档数据: {document_data}")

            # 获取文档类型和文件名
            document_type = document_data.get('document_type', '') or document_data.get('type', '')
            file_name = document_data.get('file_name', '') or document_data.get('filename', f'document_{document_id}')

            # 创建临时文件夹
            temp_dir = os.path.join(os.path.dirname(os.path.dirname(__file__)), 'temp')
            os.makedirs(temp_dir, exist_ok=True)

            # 生成临时文件路径
            temp_file_path = os.path.join(temp_dir, file_name)

            # 下载文档
            logger.info(f"开始下载文档: {document_id}, 保存到: {temp_file_path}")

            # 尝试下载，最多重试3次
            max_retries = 3
            retry_count = 0
            download_result = False
            last_error = None

            while retry_count < max_retries and not download_result:
                try:
                    logger.info(f"尝试下载文档 (尝试 {retry_count + 1}/{max_retries})")
                    download_result = cloud_api.download_document(document_id, temp_file_path)
                    if download_result:
                        break

                    last_error = cloud_api.last_error if hasattr(cloud_api, 'last_error') else "未知错误"
                    logger.warning(f"下载失败，错误: {last_error}")

                    # 根据错误类型决定是否重试
                    if "502" in str(last_error):
                        logger.info("检测到502错误，等待后重试")
                        time.sleep(1)
                    elif "500" in str(last_error):
                        logger.info("检测到500错误，等待后重试")
                        time.sleep(1)
                    else:
                        # 其他错误不重试
                        break

                except Exception as e:
                    logger.error(f"下载过程中出错: {e}")
                    last_error = str(e)
                    time.sleep(1)

                retry_count += 1

            if not download_result or not os.path.exists(temp_file_path):
                error_msg = last_error if last_error else "未知错误"
                logger.error(f"下载文档失败: {document_id}, 错误: {error_msg}")

                # 显示更详细的错误信息
                if "502" in str(error_msg):
                    Clock.schedule_once(lambda dt: self.show_error("下载文档失败: 服务器暂时不可用 (502)，请稍后重试"))
                elif "500" in str(error_msg):
                    Clock.schedule_once(lambda dt: self.show_error("下载文档失败: 服务器内部错误 (500)，请稍后重试"))
                elif "404" in str(error_msg):
                    Clock.schedule_once(lambda dt: self.show_error("下载文档失败: 文档不存在或已被删除"))
                else:
                    Clock.schedule_once(lambda dt: self.show_error(f"下载文档失败: {error_msg}"))

                Clock.schedule_once(lambda dt: self.dismiss_loading_dialog())
                return

            logger.info(f"文档下载成功: {temp_file_path}")

            # 根据文件类型选择不同的查看方式
            file_ext = os.path.splitext(file_name)[1].lower()

            # 在主线程中打开文档查看器
            Clock.schedule_once(lambda dt: self._open_document_viewer(temp_file_path, file_ext, document_data))
        except Exception as e:
            logger.error(f"下载文档线程出错: {e}")
            import traceback
            logger.error(traceback.format_exc())
            Clock.schedule_once(lambda dt: self.show_error(f"下载文档时出错: {str(e)}"))
            Clock.schedule_once(lambda dt: self.dismiss_loading_dialog())

    def _open_document_viewer(self, file_path, file_ext, document_data) -> None:
        """打开文档查看器"""
        try:
            # 关闭加载对话框
            self.dismiss_loading_dialog()

            app = MDApp.get_running_app()

            # 根据文件类型选择不同的查看器
            if file_ext in ['.jpg', '.jpeg', '.png', '.gif', '.bmp']:
                # 图像文件
                if app and hasattr(app, 'root') and app.root:
                    if not app.root.has_screen('image_viewer_screen'):
                        from screens.image_viewer_screen import ImageViewerScreen
                        image_viewer = ImageViewerScreen(name='image_viewer_screen')
                        app.root.add_widget(image_viewer)

                    # 设置图像路径 - 在切换屏幕前设置，让on_enter方法处理更新
                    setattr(app, 'image_to_view', file_path)
                    setattr(app, 'image_title', document_data.get('file_name', '图像文件'))
                    # 确保属性已正确设置
                    logger.info(f"设置image_to_view属性: {getattr(app, 'image_to_view', '未设置')}")
                    logger.info(f"设置image_title属性: {getattr(app, 'image_title', '未设置')}")

                    # 切换到图像查看器
                    app.root.transition.direction = 'left'
                    app.root.current = 'image_viewer_screen'
            elif file_ext in ['.pdf']:
                # PDF文件 - 使用系统默认应用打开
                import subprocess
                import platform

                system = platform.system()

                if system == 'Windows':
                    os.startfile(file_path)
                elif system == 'Darwin':  # macOS
                    subprocess.call(['open', file_path])
                else:  # Linux
                    subprocess.call(['xdg-open', file_path])

                self.show_info(f"已使用系统默认应用打开 {os.path.basename(file_path)}")
            elif file_ext in ['.txt', '.md', '.csv', '.json', '.xml', '.html', '.htm']:
                # 文本文件
                if app and hasattr(app, 'root') and app.root:
                    if not app.root.has_screen('text_viewer_screen'):
                        from screens.text_viewer_screen import TextViewerScreen
                        text_viewer = TextViewerScreen(name='text_viewer_screen')
                        app.root.add_widget(text_viewer)

                    # 设置文本文件路径 - 在切换屏幕前设置，让on_enter方法处理更新
                    setattr(app, 'text_to_view', file_path)
                    setattr(app, 'text_title', document_data.get('file_name', '文本文件'))

                    # 切换到文本查看器
                    app.root.transition.direction = 'left'
                    app.root.current = 'text_viewer_screen'
            else:
                # 其他类型文件，尝试使用系统默认应用打开
                import subprocess
                import platform

                system = platform.system()

                try:
                    if system == 'Windows':
                        os.startfile(file_path)
                    elif system == 'Darwin':  # macOS
                        subprocess.call(['open', file_path])
                    else:  # Linux
                        subprocess.call(['xdg-open', file_path])

                    self.show_info(f"已使用系统默认应用打开 {os.path.basename(file_path)}")
                except Exception as e:
                    logger.error(f"无法打开文件: {e}")
                    self.show_error(f"无法打开此类型的文件: {file_ext}")
        except Exception as e:
            logger.error(f"打开文档查看器时出错: {e}")
            import traceback
            logger.error(traceback.format_exc())
            self.show_error(f"打开文档查看器时出错: {str(e)}")

    def show_loading_dialog(self, message="正在加载...") -> None:
        """显示加载对话框"""
        # 如果已有对话框，先关闭
        if hasattr(self, 'loading_dialog') and self.loading_dialog:
            self.loading_dialog.dismiss()
            self.loading_dialog = None

        # 使用简单的对话框，避免复杂的参数问题
        try:
            from kivymd.uix.dialog import MDDialog
            from kivymd.uix.button import MDButton, MDButtonText
            from kivymd.uix.boxlayout import MDBoxLayout
            from kivymd.uix.label import MDLabel

            # 创建内容布局
            content_box = MDBoxLayout(
                orientation='vertical',
                spacing=dp(10),
                padding=dp(20),
                size_hint_y=None,
                height=dp(100)
            )

            content_box.add_widget(MDLabel(
                text=message,
                halign='center',
                theme_text_color="Primary",
                font_style="Body",
                role="small"  # 减小字体
            ))

            # 添加简单的加载指示
            content_box.add_widget(MDLabel(
                text="⏳ 请稍候...",
                halign='center',
                theme_text_color="Secondary",
                font_style="Body",
                role="small"
            ))

            self.loading_dialog = MDDialog(
                content_box
            )
            
            # 设置对话框属性
            self.loading_dialog.auto_dismiss = False
            self.loading_dialog.size_hint = (None, None)
            self.loading_dialog.width = int(dp(300))
            self.loading_dialog.height = int(dp(150))
            
            self.loading_dialog.open()

        except Exception as e:
            logger.error(f"创建加载对话框失败: {e}")
            # 如果对话框创建失败，至少显示一个简单的提示
            self.show_info(message)

    def dismiss_loading_dialog(self) -> None:
        """关闭加载对话框"""
        if hasattr(self, 'loading_dialog') and self.loading_dialog:
            self.loading_dialog.dismiss()
            self.loading_dialog = None

    def on_delete_document(self, instance, document_id, document_type) -> None:
        """删除文档"""
        document_id = str(document_id) if document_id is not None else ""
        if self.dialog:
            self.dialog.dismiss()
        
        # 使用KivyMD 2.0.1 dev0规范创建对话框
        from kivymd.uix.dialog import (
            MDDialog,
            MDDialogHeadlineText,
            MDDialogSupportingText,
            MDDialogButtonContainer,
        )
        from kivymd.uix.button import MDButton, MDButtonText
        
        self.dialog = MDDialog(
            MDDialogHeadlineText(
                text="确认删除"
            ),
            MDDialogSupportingText(
                text="确定要删除这个文档吗？此操作不可撤销。"
            ),
            MDDialogButtonContainer(
                MDButton(
                    MDButtonText(text="取消"),
                    style="text",
                    on_release=lambda x: self.dialog.dismiss() if self.dialog else None
                ),
                MDButton(
                    MDButtonText(text="删除"),
                    style="text",
                    theme_text_color="Error",
                    on_release=lambda x: self.confirm_delete_document(document_id, document_type)
                )
            )
        )
        self.dialog.open()



    def confirm_delete_document(self, document_id, document_type) -> None:
        """确认删除文档"""
        document_id = str(document_id) if document_id is not None else ""
        if self.dialog:
            self.dialog.dismiss()
        threading.Thread(target=self._delete_document_thread, args=(document_id, document_type)).start()

    def _delete_document_thread(self, document_id, document_type) -> None:
        """后台线程删除文档"""
        document_id = str(document_id) if document_id is not None else ""
        try:
            cloud_api = get_cloud_api()
            user_manager = get_user_manager()
            user = user_manager.get_current_user()
            custom_id = getattr(user, 'custom_id', None) if user else None
            if not custom_id:
                Clock.schedule_once(lambda dt: self.show_error("未获取到有效的custom_id，无法删除"))
                return
                
            # 调用后端API删除文档
            result = cloud_api.delete_document(document_id=document_id, document_type=document_type, custom_id=custom_id)
            if result and result.get("status") == "success":
                # 后端删除成功，同时删除本地数据库中的记录
                try:
                    from utils.health_data_manager import HealthDataManager
                    health_manager = HealthDataManager()
                    # 删除本地数据库中的文档记录
                    health_manager.delete_document(document_id, sync_to_cloud=False)  # 不需要再次同步到云端
                    logger.info(f"已删除本地文档记录: {document_id}")
                except Exception as local_delete_error:
                    logger.error(f"删除本地文档记录失败: {local_delete_error}")
                    # 本地删除失败不影响整体删除操作，因为后端已经删除成功
                
                # 通过OptimizedSyncClient同步删除操作到后端
                try:
                    from utils.optimized_sync_client import get_sync_client
                    
                    # 获取同步客户端
                    sync_client = get_sync_client(custom_id)
                    if sync_client:
                        # 准备删除操作的数据
                        delete_data = {
                            'id': document_id,
                            'custom_id': custom_id,
                            'document_type': document_type,
                            'deleted_at': datetime.now().isoformat(),
                            'operation': 'delete'
                        }
                        
                        # 立即同步删除操作
                        sync_result = sync_client.sync_record_immediate('documents', 'delete', delete_data)
                        if sync_result and sync_result.get('success'):
                            logger.info(f"文档删除操作同步成功: {document_id}")
                        else:
                            error_msg = sync_result.get('error', '未知同步错误') if sync_result else '同步失败'
                            logger.warning(f"文档删除操作同步失败: {error_msg}")
                    else:
                        logger.warning("无法获取同步客户端，跳过删除操作同步")
                        
                except Exception as sync_error:
                    logger.error(f"同步删除操作失败: {str(sync_error)}")
                    import traceback
                    logger.error(traceback.format_exc())
                
                # 删除成功，更新UI
                Clock.schedule_once(lambda dt: self.show_info("文档已删除"))
                Clock.schedule_once(lambda dt: self.load_documents(), 1)
            else:
                error_msg = result.get("message", "未知错误") if result else "删除失败"
                Clock.schedule_once(lambda dt: self.show_error(f"删除文档失败: {error_msg}"))
        except Exception as e:
            logger.error(f"删除文档出错: {e}")
            import traceback
            logger.error(traceback.format_exc())
            Clock.schedule_once(lambda dt: self.show_error(f"删除文档失败: {str(e)}"))

    def navigate_to_document_type(self, document_type) -> None:
        """导航到特定类型的文档页面"""
        # 设置文档类型
        self.document_type = document_type

        # 设置标题
        if document_type in self.document_types:
            self.title = self.document_types[document_type]["title"]
        else:
            self.title = "健康资料管理"

        # 重新加载文档
        self.load_documents()

    def show_error(self, message) -> None:
        """显示错误信息

        Args:
            message (str): 错误信息
        """
        logger.error(message)
        try:
            from kivymd.uix.snackbar import MDSnackbar, MDSnackbarText

            MDSnackbar(
                MDSnackbarText(
                    text=message,
                ),
                y=dp(24),
                pos_hint={"center_x": 0.5},
                duration=3,
                md_bg_color=self.app.theme_cls.errorColor if self.app else (1, 0.2, 0.2, 1),
            ).open()
        except Exception as e:
            logger.error(f"显示错误信息失败: {e}")

            try:
                from kivymd.uix.dialog import (
                    MDDialog,
                    MDDialogHeadlineText,
                    MDDialogSupportingText,
                    MDDialogButtonContainer
                )
                from kivymd.uix.button import MDButton, MDButtonText

                dialog = MDDialog(
                    MDDialogHeadlineText(
                        text="错误"
                    ),
                    MDDialogSupportingText(
                        text=message
                    ),
                    MDDialogButtonContainer(
                        MDButton(
                            MDButtonText(text="确定"),
                            style="text",
                            on_release=lambda x: dialog.dismiss() if dialog else None
                        )
                    )
                )
                dialog.open()
            except Exception as e2:
                logger.error(f"显示错误对话框也失败: {e2}")

    def show_info(self, message) -> None:
        """显示信息提示"""
        try:
            from kivymd.uix.snackbar import MDSnackbar, MDSnackbarText
            MDSnackbar(
                MDSnackbarText(
                    text=message,
                ),
                y=dp(24),
                pos_hint={"center_x": 0.5},
                duration=2,
                md_bg_color=self.app.theme_cls.primaryColor if self.app else (0.2, 0.6, 1, 1),
            ).open()
        except Exception as e:
            logger.error(f"显示信息提示失败: {e}")
            # 降级处理，使用简单的对话框
            try:
                from kivymd.uix.dialog import (
                    MDDialog,
                    MDDialogHeadlineText,
                    MDDialogSupportingText,
                    MDDialogButtonContainer
                )
                from kivymd.uix.button import MDButton, MDButtonText

                dialog = MDDialog(
                    MDDialogHeadlineText(
                        text="提示"
                    ),
                    MDDialogSupportingText(
                        text=message
                    ),
                    MDDialogButtonContainer(
                        MDButton(
                            MDButtonText(text="确定"),
                            style="text",
                            on_release=lambda x: dialog.dismiss() if dialog else None
                        )
                    )
                )
                dialog.open()
            except Exception as e2:
                logger.error(f"显示信息对话框也失败: {e2}")
    
    def go_back(self, *args) -> None:
        """兼容KV和按钮事件的返回方法"""
        self.on_back()

    def on_back(self) -> None:
        """处理返回按钮点击事件

        返回到健康资料管理页面。
        """
        try:
            self.logger.info("[HealthDocumentScreen] 健康资料页面返回")
            # 获取应用实例和屏幕管理器
            from kivymd.app import MDApp
            app = MDApp.get_running_app()
            
            # 获取屏幕管理器实例
            screen_manager = None
            if app and hasattr(app, 'screen_manager') and app.screen_manager:
                screen_manager = app.screen_manager
                self.logger.info("[HealthDocumentScreen] 从app获取到screen_manager")
            elif app and hasattr(app, 'root') and app.root:
                # 如果root是主布局，尝试从其子组件中找到屏幕管理器
                for child in app.root.children:
                    if hasattr(child, 'go_back'):
                        screen_manager = child
                        self.logger.info("[HealthDocumentScreen] 从root的子组件中找到screen_manager")
                        break
            
            if screen_manager and hasattr(screen_manager, 'go_back'):
                try:
                    self.logger.info("[HealthDocumentScreen] 尝试调用screen_manager.go_back()")
                    success = screen_manager.go_back()
                    if success:
                        self.logger.info("[HealthDocumentScreen] 成功调用screen_manager.go_back()")
                        return
                    else:
                        self.logger.info("[HealthDocumentScreen] screen_manager.go_back()返回False")
                except Exception as e:
                    self.logger.error(f"[HealthDocumentScreen] 调用screen_manager.go_back()失败: {e}")
                    import traceback
                    self.logger.error(traceback.format_exc())
            else:
                self.logger.warning("[HealthDocumentScreen] 未找到有效的screen_manager")
            
            # 如果go_back失败，直接切换到健康资料管理页面
            if screen_manager:
                self.logger.info("[HealthDocumentScreen] 直接切换到健康资料管理页面")
                try:
                    screen_manager.current = 'health_data_management_screen'
                    self.logger.info("[HealthDocumentScreen] 成功切换到健康资料管理页面")
                    return
                except Exception as e:
                    self.logger.error(f"[HealthDocumentScreen] 切换到健康资料管理页面失败: {e}")
            
            # 最后的兜底方案：使用app.root直接切换
            if app and hasattr(app, 'root') and app.root:
                self.logger.info("[HealthDocumentScreen] 使用app.root直接切换")
                try:
                    app.root.transition.direction = 'right'
                    app.root.current = 'health_data_management_screen'
                    self.logger.info("[HealthDocumentScreen] 使用app.root成功切换")
                except Exception as e:
                    self.logger.error(f"[HealthDocumentScreen] 使用app.root切换失败: {e}")
            else:
                self.logger.error("[HealthDocumentScreen] 无法获取app.root，返回操作失败")
                
        except Exception as e:
            self.logger.error(f"[HealthDocumentScreen] on_back方法执行失败: {e}")
            import traceback
            self.logger.error(traceback.format_exc())

# 在类定义后注册Factory和加载KV，顺序与其它页面一致
Factory.register('HealthDocumentScreen', cls=HealthDocumentScreen)
from kivy.lang import Builder
Builder.load_string(KV)