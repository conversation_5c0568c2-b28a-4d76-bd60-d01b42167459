# screens/register_screen_optimized.py

# register_screen.py - 基于BaseScreen的优化实现，遵循UI改造规范并优化代码结构
from .base_screen import BaseScreen
from kivy.properties import StringProperty, BooleanProperty, ObjectProperty, ListProperty
from kivy.metrics import dp
from kivy.clock import Clock
from kivy.factory import Factory
from kivy.uix.gridlayout import GridLayout
from kivy.uix.spinner import Spinner
from mobile.theme import AppTheme
from mobile.api.api_client import APIClient
from mobile.utils.file_upload_download_manager import FileUploadDownloadManager
from mobile.utils.kivymd_date_picker import show_kivymd_date_picker
from mobile.utils.common_components import safe_bind_event
import re
import datetime
from kivymd.uix.dialog import MDDialog
from kivymd.uix.button import MDButton, MDButtonText
from kivymd.uix.label import MDLabel
from kivymd.app import MDApp
from kivymd.uix.boxlayout import MDBoxLayout
from kivymd.uix.card import MDCard
from kivymd.uix.textfield import MDTextField, MDTextFieldHintText
from kivymd.uix.snackbar import MDSnackbar, MDSnackbarText
try:
    from kivymd.uix.button import MDIconButton
except ImportError:
    # 兼容性处理
    from kivy.uix.widget import Widget
    class MDIconButton(Widget):
        icon = StringProperty('')
        def __init__(self, **kwargs):
            self.icon = kwargs.pop('icon', '')
            super(MDIconButton, self).__init__(**kwargs)

import logging
logger = logging.getLogger(__name__)


# 字符串常量定义
ROLE_UNIT_ADMIN = "单位管理员"
ROLE_SUPER_ADMIN = "超级管理员"
REGISTRATION_TYPE_FOR_OTHERS = "替他人注册"

class RegisterScreen(BaseScreen):
    """用户注册屏幕 - 优化版本"""
    
    # 角色配置常量
    ROLE_CONFIG = [
        {"name": "个人用户", "icon": "account", "default": True},
        {"name": ROLE_UNIT_ADMIN, "icon": "medical-bag", "default": False},
        {"name": "健康顾问", "icon": "heart-pulse", "default": False},
        {"name": ROLE_SUPER_ADMIN, "icon": "shield-account", "default": False, "exclusive": True},
        {"name": "陪诊师", "icon": "account-heart", "default": False}
    ]
    
    # 角色联合选择规则
    ROLE_COMBINATION_RULES = {
        "个人用户": [ROLE_UNIT_ADMIN, "健康顾问", "陪诊师"],  # 个人用户可以与这些角色联合
        ROLE_UNIT_ADMIN: ["个人用户"],  # 单位管理员只能与个人用户联合
        "健康顾问": ["个人用户"],   # 健康顾问只能与个人用户联合
        "陪诊师": ["个人用户"],     # 陪诊师只能与个人用户联合
        ROLE_SUPER_ADMIN: []           # 超级管理员不能与任何角色联合
    }
    
    # 属性定义
    selected_roles = ListProperty([])
    gender = StringProperty("男")
    api_client = ObjectProperty(None)
    id_card_verified = BooleanProperty(False)
    registration_type = StringProperty("本人注册")
    relationship = StringProperty("")
    identity = StringProperty("")
    selected_role_color = [0.2, 0.6, 1, 0.8]
    
    # 添加缺失的属性
    gender_field = ObjectProperty(None)
    relationship_input = ObjectProperty(None)
    
    # 证书上传相关属性
    medical_license_taken = BooleanProperty(False)
    medical_license_path = StringProperty("")
    practice_license_taken = BooleanProperty(False)
    practice_license_path = StringProperty("")
    certificate_document_ids = ListProperty([])
    
    # 出生日期相关属性
    birth_date = StringProperty("")

    def __init__(self, **kwargs):
        """初始化注册屏幕"""
        # 设置BaseScreen的导航栏属性
        kwargs.setdefault('screen_title', '用户注册')
        kwargs.setdefault('show_top_bar', True)
        kwargs.setdefault('skip_bottom_nav', False)

        super(RegisterScreen, self).__init__(**kwargs)
        
        # 初始化组件
        self.api_client = APIClient()
        self.file_manager = FileUploadDownloadManager()
        self.logger = logger
        self._loading_dialog = None
        self.current_certificate_type = None
        
        # 默认选中"个人用户"角色以简化UX
        self.selected_roles = ["个人用户"]
        
        # 初始化用户管理器
        try:
            from utils.user_manager import get_user_manager
            self.user_manager = get_user_manager()
        except ImportError:
            self.user_manager = None

        # 初始化主题
        from mobile.theme import AppTheme as AppThemeInstance
        self.theme = AppThemeInstance
        
        # 确保logo显示 - 延迟调用以确保UI完全初始化
        Clock.schedule_once(self._ensure_logo_display, 0.1)

    def do_content_setup(self):
        """设置页面内容 - 遵循BaseScreen规范"""
        try:
            # 获取内容容器
            content_container = self.ids.get('content_container')
            if not content_container:
                self.logger.error("无法获取content_container")
                return

            # 清空现有内容
            content_container.clear_widgets()

            # 添加注册类型选择
            self._add_registration_type_section(content_container)

            # 添加角色选择
            self._add_role_selection_section(content_container)

            # 添加关系选择（替他人注册时显示）
            self._add_relationship_section(content_container)

            # 添加资格证书上传（特定角色需要）
            self._add_certificate_section(content_container)

            # 添加基本信息输入
            self._add_basic_info_section(content_container)

            # 添加底部按钮
            self._add_bottom_buttons(content_container)

            # 初始化UI状态
            self._update_registration_ui()
            self._update_role_cards_visual_state()

            self.logger.info("注册页面内容设置完成")

        except Exception as e:
            self.logger.error(f"设置页面内容时出错: {e}")
            import traceback
            traceback.print_exc()

    def _add_registration_type_section(self, parent_layout):
        """添加注册类型选择区域"""
        # 注册类型选择卡片
        reg_type_card = self._create_card()
        parent_layout.add_widget(reg_type_card)

        # 注册类型选择按钮布局 - 水平居中
        reg_btn_layout = MDBoxLayout(
            orientation="horizontal",
            size_hint_y=None,
            height=dp(30),
            spacing=dp(12),
            size_hint_x=None,
            width=dp(240),  # 固定宽度以实现居中
            pos_hint={"center_x": 0.5}  # 水平居中
        )
        reg_type_card.add_widget(reg_btn_layout)

        # 本人注册按钮
        self.self_register_btn = self._create_toggle_button(
            "本人注册", 
            lambda x: self.set_registration_type("本人注册"),
            self.registration_type == "本人注册"
        )
        reg_btn_layout.add_widget(self.self_register_btn)

        # 替他人注册按钮
        self.other_register_btn = self._create_toggle_button(
            "替他人注册", 
            lambda x: self.set_registration_type("替他人注册"),
            self.registration_type == "替他人注册"
        )
        reg_btn_layout.add_widget(self.other_register_btn)

    def _add_role_selection_section(self, parent_layout):
        """添加角色选择区域"""
        # 角色选择标签
        role_label = MDLabel(
            text="选择注册身份：",
            size_hint_y=None,
            height=dp(25),
            theme_text_color="Primary",
            halign='left',
            font_style="Label",
            role="medium",
            bold=True
        )
        parent_layout.add_widget(role_label)

        # 角色选择卡片容器 - 优化高度以适配3列2行布局
        role_container = MDBoxLayout(
            orientation="vertical",
            size_hint_y=None,
            height=dp(280),  # 增加容器高度以适配2行布局
            spacing=dp(8)
        )
        parent_layout.add_widget(role_container)

        # 角色选择卡片
        role_card = self._create_card()
        role_container.add_widget(role_card)

        # 角色网格布局 - 优化为3列2行布局
        role_grid = GridLayout(
            cols=3,
            rows=2,  # 明确设置为2行
            spacing=dp(12),  # 统一间距
            size_hint_y=None,
            height=dp(240),  # 适配2行卡片高度 (2 * 110 + 间距)
            col_default_width=dp(100),  # 统一列宽
            row_default_height=dp(110),  # 统一行高，匹配卡片高度
            padding=[dp(15), dp(15), dp(15), dp(15)]  # 统一内边距
        )
        role_card.add_widget(role_grid)

        # 创建角色卡片
        roles = [
            ("个人用户", "account"),
            ("单位管理员", "medical-bag"),
            ("健康顾问", "heart-pulse"),
            ("超级管理员", "shield-account"),
            ("陪诊师", "account-heart")
        ]

        self.role_cards = {}
        for role_name, icon_name in roles:
            card = self._create_role_card(role_name, icon_name)
            self.role_cards[role_name] = card
            role_grid.add_widget(card)

        # 关系按钮（替他人注册时显示）- 统一样式
        self.relationship_button_card = self._create_relationship_button_card()
        role_grid.add_widget(self.relationship_button_card)

    def _add_relationship_section(self, parent_layout):
        """添加与被注册人关系选择区域"""
        self.relationship_container = MDBoxLayout(
            orientation="vertical",
            size_hint_y=None,
            height=dp(0),
            opacity=0,
            disabled=True,
            spacing=dp(8)
        )
        parent_layout.add_widget(self.relationship_container)

    def _add_certificate_section(self, parent_layout):
        """添加资格证书上传区域"""
        self.certificate_container = MDBoxLayout(
            orientation="vertical",
            size_hint_y=None,
            height=dp(0),
            opacity=0,
            disabled=True,
            spacing=dp(8)
        )
        parent_layout.add_widget(self.certificate_container)

        # 证书上传标签
        cert_label = MDLabel(
            text="上传资格证书：",
            size_hint_y=None,
            height=dp(30),
            theme_text_color="Primary",
            halign='left',
            font_style="Label",
            role="medium",
            bold=True
        )
        self.certificate_container.add_widget(cert_label)

        # 证书上传卡片
        cert_card = self._create_card()
        self.certificate_container.add_widget(cert_card)

        # 证书上传按钮布局
        cert_btn_layout = MDBoxLayout(
            orientation="horizontal",
            size_hint_y=None,
            height=dp(50),
            spacing=dp(12)
        )
        cert_card.add_widget(cert_btn_layout)

        # 医师资格证书上传按钮
        medical_btn = self._create_upload_button(
            "医师资格证书",
            "certificate",
            lambda x: self.upload_certificate("medical_license")
        )
        cert_btn_layout.add_widget(medical_btn)

        # 医师执业证书上传按钮
        practice_btn = self._create_upload_button(
            "医师执业证书",
            "certificate",
            lambda x: self.upload_certificate("practice_license")
        )
        cert_btn_layout.add_widget(practice_btn)

    def _add_basic_info_section(self, parent_layout):
        """添加基础信息部分 - 参考原版本布局结构"""
        # 基础信息卡片
        basic_info_card = MDCard(
            orientation="vertical",
            size_hint_y=None,
            padding=dp(16),
            spacing=dp(20),  # 增大垂直间距
            md_bg_color=getattr(AppTheme, "SURFACE_COLOR", [1, 1, 1, 1]),
            elevation=2,
            radius=[dp(12)]
        )
        basic_info_card.bind(minimum_height=basic_info_card.setter('height'))
        parent_layout.add_widget(basic_info_card)

        # 基础信息标签
        basic_label = MDLabel(
            text="基础信息",
            size_hint_y=None,
            height=dp(30),
            theme_text_color="Primary",
            halign='left',
            font_style="Title",
            role="small",
            bold=True
        )
        basic_info_card.add_widget(basic_label)

        # 创建输入字段
        self._create_input_fields(basic_info_card)

    def _create_input_fields(self, parent_layout):
        """创建输入字段 - 参考原版本的行布局方式"""
        # 第一行：用户名、姓名
        row1_grid = GridLayout(
            cols=2,
            spacing=dp(16),
            size_hint_y=None,
            height=dp(48)
        )
        parent_layout.add_widget(row1_grid)

        # 用户名
        self.username_input = MDTextField(
            mode="outlined",
            size_hint_y=None,
            height=dp(40),
            multiline=False,
            md_bg_color=getattr(AppTheme, "SURFACE_VARIANT_COLOR", [0.95, 0.95, 0.95, 1]),
            theme_text_color="Primary"
        )
        # 添加提示文本 - KivyMD 2.0.1 dev0规范
        self.username_input.add_widget(MDTextFieldHintText(
            text="用户名 *",
            font_size=dp(14)
        ))
        row1_grid.add_widget(self.username_input)

        # 姓名
        self.name_input = MDTextField(
            mode="outlined",
            size_hint_y=None,
            height=dp(40),
            multiline=False,
            md_bg_color=getattr(AppTheme, "SURFACE_VARIANT_COLOR", [0.95, 0.95, 0.95, 1]),
            theme_text_color="Primary"
        )
        # 添加提示文本 - KivyMD 2.0.1 dev0规范
        self.name_input.add_widget(MDTextFieldHintText(
            text="姓名 *",
            font_size=dp(14)
        ))
        row1_grid.add_widget(self.name_input)

        # 第二行：身份证号码（单独一行）
        id_card_container = MDBoxLayout(
            orientation="horizontal",
            spacing=dp(16),
            size_hint_y=None,
            height=dp(48)
        )
        parent_layout.add_widget(id_card_container)

        # 身份证输入框
        self.id_card_input = MDTextField(
            mode="outlined",
            size_hint_y=None,
            height=dp(40),
            multiline=False,
            md_bg_color=getattr(AppTheme, "SURFACE_VARIANT_COLOR", [0.95, 0.95, 0.95, 1]),
            theme_text_color="Primary",
            font_size=dp(14)
        )
        # 添加提示文本 - KivyMD 2.0.1 dev0规范
        self.id_card_input.add_widget(MDTextFieldHintText(
            text="身份证号码 * (18位)",
            font_size=dp(14)
        ))
        self.id_card_input.bind(text=self._on_id_card_input)
        id_card_container.add_widget(self.id_card_input)

        # 第三行：性别、出生日期
        row3_container = MDBoxLayout(
            orientation="horizontal",
            spacing=dp(16),
            size_hint_y=None,
            height=dp(56)
        )
        parent_layout.add_widget(row3_container)

        # 性别选择器
        self.gender_spinner = Spinner(
            text=self.gender,
            values=["男", "女"],
            size_hint_x=0.15,
            size_hint_y=None,
            height=dp(48),
            background_color=getattr(AppTheme, "SURFACE_COLOR", [1, 1, 1, 1]),
            color=getattr(AppTheme, "ON_SURFACE_COLOR", [0, 0, 0, 1])
        )
        self.gender_spinner.bind(text=self._on_gender_selected)
        row3_container.add_widget(self.gender_spinner)

        # 出生日期容器
        birth_container = MDBoxLayout(
            orientation="horizontal",
            spacing=dp(8),
            size_hint_x=0.85,
            size_hint_y=None,
            height=dp(48)
        )
        row3_container.add_widget(birth_container)

        birth_label = MDLabel(
            text="出生日期 *",
            size_hint_x=None,
            width=dp(60),
            size_hint_y=None,
            height=dp(48),
            theme_text_color="Primary",
            halign='left',
            valign='center',
            font_style="Label",
            role="medium",
            bold=True,
            font_size=dp(16)
        )
        birth_container.add_widget(birth_label)

        # 出生日期输入框和按钮容器
        birth_input_container = MDBoxLayout(
            orientation="horizontal",
            spacing=dp(4),
            size_hint_y=None,
            height=dp(40)
        )
        birth_container.add_widget(birth_input_container)

        self.birth_date_input = MDTextField(
            mode="outlined",
            size_hint_y=None,
            height=dp(48),
            size_hint_x=0.80,
            md_bg_color=getattr(AppTheme, "SURFACE_VARIANT_COLOR", [0.95, 0.95, 0.95, 1]),
            theme_text_color="Primary",
            font_size=dp(16)
        )
        # 添加提示文本
        self.birth_date_input.add_widget(MDTextFieldHintText(
            text="YYYY/MM/DD",
            font_size=dp(14)
        ))
        birth_input_container.add_widget(self.birth_date_input)

        # 日期选择按钮
        date_picker_button = MDIconButton(
            icon="calendar",
            size_hint_x=0.20,
            size_hint_y=None,
            height=dp(40),
            on_release=self.show_birth_date_picker
        )
        birth_input_container.add_widget(date_picker_button)

        # 第四行：手机号、邮箱
        row4_grid = GridLayout(
            cols=2,
            spacing=dp(16),
            size_hint_y=None,
            height=dp(48)
        )
        parent_layout.add_widget(row4_grid)

        # 手机号
        self.phone_input = MDTextField(
            mode="outlined",
            size_hint_y=None,
            height=dp(40),
            multiline=False,
            md_bg_color=getattr(AppTheme, "SURFACE_VARIANT_COLOR", [0.95, 0.95, 0.95, 1]),
            theme_text_color="Primary"
        )
        self.phone_input.add_widget(MDTextFieldHintText(
            text="手机号 * (11位)",
            font_size=dp(14)
        ))
        # 绑定手机号输入验证
        self.phone_input.bind(text=self._on_phone_input)
        row4_grid.add_widget(self.phone_input)

        # 邮箱
        self.email_input = MDTextField(
            mode="outlined",
            size_hint_y=None,
            height=dp(40),
            multiline=False,
            md_bg_color=getattr(AppTheme, "SURFACE_VARIANT_COLOR", [0.95, 0.95, 0.95, 1]),
            theme_text_color="Primary"
        )
        self.email_input.add_widget(MDTextFieldHintText(
            text="邮箱 (需包含@符号)",
            font_size=dp(14)
        ))
        # 绑定邮箱输入验证
        self.email_input.bind(text=self._on_email_input)
        row4_grid.add_widget(self.email_input)

        # 第五行：工作单位、职业
        row5_grid = GridLayout(
            cols=2,
            spacing=dp(16),
            size_hint_y=None,
            height=dp(48)
        )
        parent_layout.add_widget(row5_grid)

        # 工作单位
        self.workplace_input = MDTextField(
            mode="outlined",
            multiline=False,
            size_hint_y=None,
            height=dp(40),
            md_bg_color=getattr(AppTheme, "SURFACE_VARIANT_COLOR", [0.95, 0.95, 0.95, 1]),
            theme_text_color="Primary"
        )
        self.workplace_input.add_widget(MDTextFieldHintText(
            text="工作单位",
            font_size=dp(14)
        ))
        row5_grid.add_widget(self.workplace_input)

        # 职业
        self.occupation_input = MDTextField(
            mode="outlined",
            multiline=False,
            size_hint_y=None,
            height=dp(40),
            md_bg_color=getattr(AppTheme, "SURFACE_VARIANT_COLOR", [0.95, 0.95, 0.95, 1]),
            theme_text_color="Primary"
        )
        self.occupation_input.add_widget(MDTextFieldHintText(
            text="职业",
            font_size=dp(14)
        ))
        row5_grid.add_widget(self.occupation_input)

        # 第六行：联系地址（单独一行）
        self.address_input = MDTextField(
            mode="outlined",
            multiline=False,
            size_hint_y=None,
            height=dp(40),
            md_bg_color=getattr(AppTheme, "SURFACE_VARIANT_COLOR", [0.95, 0.95, 0.95, 1]),
            theme_text_color="Primary"
        )
        self.address_input.add_widget(MDTextFieldHintText(
            text="联系地址",
            font_size=dp(14)
        ))
        parent_layout.add_widget(self.address_input)

        # 第七行：紧急联系人、紧急联系人电话
        row7_grid = GridLayout(
            cols=2,
            spacing=dp(16),
            size_hint_y=None,
            height=dp(48)
        )
        parent_layout.add_widget(row7_grid)

        # 紧急联系人
        self.emergency_contact_input = MDTextField(
            mode="outlined",
            multiline=False,
            size_hint_y=None,
            height=dp(40),
            md_bg_color=getattr(AppTheme, "SURFACE_VARIANT_COLOR", [0.95, 0.95, 0.95, 1]),
            theme_text_color="Primary"
        )
        self.emergency_contact_input.add_widget(MDTextFieldHintText(
            text="紧急联系人 *",
            font_size=dp(14)
        ))
        row7_grid.add_widget(self.emergency_contact_input)

        # 紧急联系人电话
        self.emergency_phone_input = MDTextField(
            mode="outlined",
            multiline=False,
            size_hint_y=None,
            height=dp(40),
            md_bg_color=getattr(AppTheme, "SURFACE_VARIANT_COLOR", [0.95, 0.95, 0.95, 1]),
            theme_text_color="Primary"
        )
        self.emergency_phone_input.add_widget(MDTextFieldHintText(
            text="紧急联系人电话 *",
            font_size=dp(14)
        ))
        row7_grid.add_widget(self.emergency_phone_input)

        # 第八行：民族、教育程度
        row8_grid = GridLayout(
            cols=2,
            spacing=dp(16),
            size_hint_y=None,
            height=dp(48)
        )
        parent_layout.add_widget(row8_grid)

        # 民族
        self.ethnicity_spinner = Spinner(
            text="民族 *",
            values=["汉族", "蒙古族", "回族", "藏族", "维吾尔族", "苗族", "彝族", "壮族", "布依族", "朝鲜族", "满族", "侗族", "瑶族", "白族", "土家族", "哈尼族", "哈萨克族", "傣族", "黎族", "傈僳族", "佤族", "畲族", "高山族", "拉祜族", "水族", "东乡族", "纳西族", "景颇族", "柯尔克孜族", "土族", "达斡尔族", "仫佬族", "羌族", "Brown族", "撒拉族", "毛南族", "仡佬族", "锡伯族", "阿昌族", "普米族", "塔吉克族", "怒族", "乌孜别克族", "俄罗斯族", "鄂温克族", "德昂族", "保安族", "裕固族", "京族", "塔塔尔族", "独龙族", "鄂伦春族", "赫哲族", "门巴族", "珞巴族", "基诺族", "其他"],
            size_hint_y=None,
            height=dp(40),
            background_color=getattr(AppTheme, "SURFACE_COLOR", [1, 1, 1, 1]),
            color=getattr(AppTheme, "ON_SURFACE_COLOR", [0, 0, 0, 1])
        )
        row8_grid.add_widget(self.ethnicity_spinner)

        # 教育程度
        self.education_spinner = Spinner(
            text="教育程度 *",
            values=["小学", "初中", "高中", "中专", "大专", "本科", "硕士", "博士", "其他"],
            size_hint_y=None,
            height=dp(40),
            background_color=getattr(AppTheme, "SURFACE_COLOR", [1, 1, 1, 1]),
            color=getattr(AppTheme, "ON_SURFACE_COLOR", [0, 0, 0, 1])
        )
        row8_grid.add_widget(self.education_spinner)

        # 第九行：密码、确认密码
        row9_grid = GridLayout(
            cols=2,
            spacing=dp(16),
            size_hint_y=None,
            height=dp(48)
        )
        parent_layout.add_widget(row9_grid)

        # 密码
        self.password_input = MDTextField(
            mode="outlined",
            size_hint_y=None,
            height=dp(40),
            multiline=False,
            password=True,
            md_bg_color=getattr(AppTheme, "SURFACE_VARIANT_COLOR", [0.95, 0.95, 0.95, 1]),
            theme_text_color="Primary"
        )
        self.password_input.add_widget(MDTextFieldHintText(
            text="密码 *",
            font_size=dp(14)
        ))
        row9_grid.add_widget(self.password_input)

        # 确认密码
        self.confirm_password_input = MDTextField(
            mode="outlined",
            size_hint_y=None,
            height=dp(40),
            multiline=False,
            password=True,
            md_bg_color=getattr(AppTheme, "SURFACE_VARIANT_COLOR", [0.95, 0.95, 0.95, 1]),
            theme_text_color="Primary"
        )
        self.confirm_password_input.add_widget(MDTextFieldHintText(
            text="确认密码 *",
            font_size=dp(14)
        ))
        row9_grid.add_widget(self.confirm_password_input)

    def _on_gender_selected(self, instance, value):
        """性别选择回调"""
        self.gender = value

    def _add_bottom_buttons(self, parent_layout):
        """添加底部按钮"""
        # 按钮布局 - 水平居中
        button_layout = MDBoxLayout(
            orientation="horizontal",
            size_hint_y=None,
            height=dp(50),
            spacing=dp(12),
            size_hint_x=None,
            width=dp(280),  # 固定宽度以实现居中
            pos_hint={"center_x": 0.5}  # 水平居中
        )
        parent_layout.add_widget(button_layout)

        # 返回登录按钮
        back_btn = MDButton(
            MDButtonText(text="返回登录"),
            style="outlined",
            size_hint_x=0.4,
            on_release=self.back_to_login
        )
        button_layout.add_widget(back_btn)

        # 注册按钮
        register_btn = MDButton(
            MDButtonText(text="注册"),
            style="filled",
            size_hint_x=0.6,
            on_release=self.register
        )
        button_layout.add_widget(register_btn)

    # 辅助方法
    def _create_card(self, role_name=None, title=None, icon_name=None):
        """
        创建标准卡片或角色卡片

        Args:
            role_name (str, optional): 角色名称，如果提供则创建角色卡片
            title (str, optional): 卡片标题
            icon_name (str, optional): 图标名称

        Returns:
            MDCard: 配置好的卡片组件
        """
        if role_name and icon_name:
            # 创建角色卡片 - 简化布局结构
            card = MDCard(
                orientation="vertical",
                size_hint=(1, 1),
                size_hint_min=(dp(100), dp(110)),  # 最小尺寸
                elevation=2,
                radius=[dp(8)],
                md_bg_color=getattr(AppTheme, "CARD_BACKGROUND", [1, 1, 1, 1]),
                padding=dp(8),
                spacing=dp(8),  # 直接在卡片上设置间距
                ripple_behavior=True,
                theme_bg_color="Custom",
                shadow_softness=2,
                shadow_offset=(0, 1)
            )

            # 角色图标 - 直接添加到卡片
            icon = MDIconButton(
                icon=icon_name,
                pos_hint={"center_x": 0.5},
                size_hint=(None, None),
                size=(dp(36), dp(36)),
                theme_icon_color="Custom",
                icon_color=getattr(AppTheme, "PRIMARY_COLOR", [0.12, 0.58, 0.95, 1]),
                disabled=True
            )
            card.add_widget(icon)

            # 角色名称标签 - 直接添加到卡片
            label = MDLabel(
                text=role_name,
                size_hint_y=None,
                height=dp(40),
                theme_text_color="Primary",
                font_style="Body",
                role="medium",
                halign="center",
                valign="middle",
                text_size=(dp(84), None),
                markup=True
            )
            card.add_widget(label)

            # 绑定点击事件
            card.bind(on_release=lambda x: self.select_role(role_name))

            return card
        else:
            # 创建标准卡片
            return MDCard(
                orientation='vertical',
                size_hint_y=None,
                padding=dp(16),
                spacing=dp(12),
                radius=[dp(16)],
                elevation=2,
                md_bg_color=getattr(AppTheme, 'CARD_BACKGROUND', [0.95, 0.95, 0.95, 1])
            )

    def _create_toggle_button(self, text, callback, is_selected=False):
        """创建切换按钮"""
        return MDButton(
            MDButtonText(text=text),
            style="filled",
            theme_bg_color="Custom",
            md_bg_color=getattr(AppTheme, "PRIMARY_COLOR", [0.12, 0.58, 0.95, 1]) if is_selected else getattr(AppTheme, "PRIMARY_LIGHT", [0.89, 0.95, 0.99, 1]),
            size_hint_y=None,
            height=dp(40),
            on_release=callback
        )

    def _create_role_card(self, role_name, icon_name):
        """
        创建角色选择卡片
        
        Args:
            role_name (str): 角色名称
            icon_name (str): 图标名称
            
        Returns:
            MDCard: 配置好的角色卡片组件
        """
        # 使用新的卡片创建方法
        card = self._create_card(role_name, role_name, icon_name)
        return card

    def _create_relationship_button_card(self):
        """创建关系按钮卡片 - 与角色卡片保持一致的样式"""
        card = MDCard(
            orientation="vertical",
            size_hint=(1, 1),
            size_hint_min=(dp(100), dp(110)),  # 与角色卡片相同的最小尺寸
            elevation=2,
            radius=[dp(8)],
            md_bg_color=getattr(AppTheme, "CARD_BACKGROUND", [1, 1, 1, 1]),
            padding=dp(8),
            spacing=dp(8),  # 与角色卡片相同的间距
            ripple_behavior=True,
            theme_bg_color="Custom",
            opacity=0,
            disabled=True,
            shadow_softness=2,
            shadow_offset=(0, 1)
        )

        # 关系选择下拉菜单
        from kivymd.uix.menu import MDDropdownMenu

        # 关系图标 - 与角色卡片保持一致
        relationship_icon = MDIconButton(
            icon="account-group",  # 关系图标
            pos_hint={"center_x": 0.5},
            size_hint=(None, None),
            size=(dp(36), dp(36)),
            theme_icon_color="Custom",
            icon_color=getattr(AppTheme, "PRIMARY_COLOR", [0.12, 0.58, 0.95, 1]),
            disabled=True
        )
        card.add_widget(relationship_icon)

        # 关系选择按钮 - 样式与角色标签一致
        self.relationship_button = MDLabel(
            text="选择关系",
            size_hint_y=None,
            height=dp(40),
            theme_text_color="Primary",
            font_style="Body",
            role="medium",
            halign="center",
            valign="middle",
            text_size=(dp(84), None),
            markup=True
        )
        card.add_widget(self.relationship_button)

        # 创建下拉菜单项
        relationship_items = [
            {
                "text": "父亲",
                "on_release": lambda x="父亲": self._select_relationship(x),
            },
            {
                "text": "母亲",
                "on_release": lambda x="母亲": self._select_relationship(x),
            },
            {
                "text": "配偶",
                "on_release": lambda x="配偶": self._select_relationship(x),
            },
            {
                "text": "子女",
                "on_release": lambda x="子女": self._select_relationship(x),
            },
            {
                "text": "其他亲属",
                "on_release": lambda x="其他亲属": self._select_relationship(x),
            },
            {
                "text": "朋友",
                "on_release": lambda x="朋友": self._select_relationship(x),
            },
            {
                "text": "同事",
                "on_release": lambda x="同事": self._select_relationship(x),
            }
        ]

        # 创建下拉菜单
        self.relationship_menu = MDDropdownMenu(
            caller=card,  # 使用整个卡片作为调用者
            items=relationship_items,
            width=dp(200),
            max_height=dp(200),
            position="bottom"
        )

        # 绑定卡片点击事件 - 与角色卡片保持一致的交互方式
        card.bind(on_release=lambda x: self.relationship_menu.open())

        return card

    def _select_relationship(self, relationship):
        """选择关系回调函数"""
        self.relationship = relationship  # 使用正确的属性名
        self.relationship_button.text = relationship  # 直接显示关系名称
        self.relationship_menu.dismiss()

        # 记录日志
        logger.info(f"选择关系: {relationship}")

    def _create_upload_button(self, text, icon, callback):
        """创建上传按钮"""
        return MDButton(
            MDButtonText(text=text),
            style="outlined",
            size_hint_y=None,
            height=dp(50),
            on_release=callback
        )

    def _create_text_field(self, label_text, hint_text, password=False):
        """创建文本输入字段"""
        return MDTextField(
            MDTextFieldHintText(text=hint_text),
            mode="outlined",
            size_hint_y=None,
            height=dp(56),
            password=password
        )

    # 业务逻辑方法
    def set_registration_type(self, reg_type):
        """设置注册类型"""
        self.registration_type = reg_type
        self._update_registration_ui()

    def _update_registration_ui(self):
        """更新注册UI"""
        # 更新按钮颜色
        if hasattr(self, 'self_register_btn'):
            self.self_register_btn.md_bg_color = getattr(AppTheme, "PRIMARY_COLOR", [0.12, 0.58, 0.95, 1]) if self.registration_type == "本人注册" else getattr(AppTheme, "PRIMARY_LIGHT", [0.89, 0.95, 0.99, 1])
        
        if hasattr(self, 'other_register_btn'):
            self.other_register_btn.md_bg_color = getattr(AppTheme, "PRIMARY_COLOR", [0.12, 0.58, 0.95, 1]) if self.registration_type == "替他人注册" else getattr(AppTheme, "PRIMARY_LIGHT", [0.89, 0.95, 0.99, 1])

        # 更新关系选择按钮可见性
        self._update_relationship_container_visibility()

    def _update_relationship_container_visibility(self):
        """更新关系选择容器和关系按钮卡片的显示状态"""
        # 更新关系按钮卡片的显示状态
        if hasattr(self, 'relationship_button_card'):
            if self.registration_type == "替他人注册":
                # 显示关系按钮卡片，高度与角色卡片一致
                self.relationship_button_card.size_hint_y = 1
                self.relationship_button_card.opacity = 1
                self.relationship_button_card.disabled = False
            else:
                # 隐藏关系按钮卡片
                self.relationship_button_card.size_hint_y = None
                self.relationship_button_card.height = 0
                self.relationship_button_card.opacity = 0
                self.relationship_button_card.disabled = True

    def select_role(self, role):
        """
        选择角色 - 实现互斥和联合选择规则
        
        Args:
            role (str): 要选择的角色名称
        """
        try:
            # 如果是替他人注册，只能选择个人用户
            if self.registration_type == "替他人注册" and role != "个人用户":
                logger.info(f"替他人注册时只能选择个人用户，忽略选择: {role}")
                return
            
            # 检查是否是互斥角色（超级管理员）
            role_config = next((r for r in self.ROLE_CONFIG if r["name"] == role), None)
            if role_config and role_config.get("exclusive", False):
                # 如果选择超级管理员，清空其他所有角色
                if role not in self.selected_roles:
                    self.selected_roles = [role]
                    logger.info(f"选择互斥角色 {role}，清空其他角色")
                else:
                    # 取消选择超级管理员
                    self.selected_roles.remove(role)
                    # 恢复默认选中个人用户
                    if not self.selected_roles:
                        self.selected_roles = ["个人用户"]
                    logger.info(f"取消选择互斥角色 {role}")
            else:
                # 普通角色选择逻辑
                if role in self.selected_roles:
                    # 取消选择角色
                    self.selected_roles.remove(role)
                    # 如果取消选择后没有角色，默认选中个人用户
                    if not self.selected_roles:
                        self.selected_roles = ["个人用户"]
                    logger.info(f"取消选择角色: {role}")
                else:
                    # 选择新角色 - 检查联合选择规则
                    if self._can_combine_roles(role):
                        # 如果当前选中了超级管理员，先清空
                        if "超级管理员" in self.selected_roles:
                            self.selected_roles = []
                        
                        self.selected_roles.append(role)
                        logger.info(f"选择角色: {role}")
                    else:
                        logger.warning(f"角色 {role} 不能与当前选中的角色联合")
                        return
            
            # 更新角色卡片视觉状态
            self._update_role_cards_visual_state()
            
            # 更新证书上传区域可见性
            show_certificates = "健康顾问" in self.selected_roles
            self._update_certificate_container(show_certificates)
            
            logger.info(f"当前选中角色: {self.selected_roles}")
            
        except Exception as e:
            logger.error(f"选择角色时发生错误: {e}")
    
    def _can_combine_roles(self, new_role):
        """
        检查新角色是否可以与当前选中的角色联合
        
        Args:
            new_role (str): 要检查的新角色
            
        Returns:
            bool: 是否可以联合选择
        """
        try:
            # 如果没有选中任何角色，可以选择任何角色
            if not self.selected_roles:
                return True
            
            # 获取联合规则
            combination_rules = self.ROLE_COMBINATION_RULES.get(new_role, [])
            
            # 检查当前选中的每个角色是否都在新角色的联合规则中
            for selected_role in self.selected_roles:
                if selected_role not in combination_rules:
                    return False
            
            # 反向检查：检查新角色是否在当前选中角色的联合规则中
            for selected_role in self.selected_roles:
                selected_rules = self.ROLE_COMBINATION_RULES.get(selected_role, [])
                if new_role not in selected_rules:
                    return False
            
            return True
            
        except Exception as e:
            logger.error(f"检查角色联合规则时发生错误: {e}")
            return False

    def _update_role_cards_visual_state(self):
        """更新角色卡片视觉状态"""
        if not hasattr(self, 'role_cards'):
            return
            
        for role_name, card in self.role_cards.items():
            if role_name in self.selected_roles:
                card.md_bg_color = self.selected_role_color
            else:
                card.md_bg_color = getattr(AppTheme, "CARD_BACKGROUND", [1, 1, 1, 1])

    def _update_certificate_container(self, show_certificates):
        """更新证书容器可见性"""
        if hasattr(self, 'certificate_container'):
            if show_certificates:
                self.certificate_container.height = dp(160)
                self.certificate_container.opacity = 1
                self.certificate_container.disabled = False
            else:
                self.certificate_container.height = dp(0)
                self.certificate_container.opacity = 0
                self.certificate_container.disabled = True

    def _show_gender_dialog(self, *args):
        """显示性别选择对话框"""
        def set_gender(gender):
            self.gender = gender
            self.gender_spinner.text = gender
            dialog.dismiss()

        dialog = MDDialog(
            title="选择性别",
            buttons=[
                MDButton(
                    MDButtonText(text="男"),
                    on_release=lambda x: set_gender("男")
                ),
                MDButton(
                    MDButtonText(text="女"),
                    on_release=lambda x: set_gender("女")
                ),
            ],
        )
        dialog.open()

    def _show_relationship_dialog(self):
        """显示关系选择对话框"""
        from kivymd.uix.dialog import (
            MDDialog,
            MDDialogHeadlineText,
            MDDialogContentContainer,
            MDDialogButtonContainer
        )
        from kivymd.uix.boxlayout import MDBoxLayout
        
        relationships = ["本人", "配偶", "子女", "父母", "其他亲属", "朋友", "同事"]
        
        def set_relationship(rel):
            self.relationship = rel
            self.relationship_button.text = rel
            dialog.dismiss()

        # 创建内容容器
        content = MDBoxLayout(
            orientation="vertical",
            spacing=dp(8),
            size_hint_y=None,
            adaptive_height=True,
            padding=[dp(16), dp(8), dp(16), dp(8)]
        )

        # 添加关系选择按钮
        for rel in relationships:
            button = MDButton(
                MDButtonText(text=rel),
                style="outlined",
                size_hint_y=None,
                height=dp(48),
                on_release=lambda x, r=rel: set_relationship(r)
            )
            content.add_widget(button)

        # 创建对话框 - 符合KivyMD 2.0.1规范
        dialog = MDDialog(
            MDDialogHeadlineText(
                text="选择与被注册人的关系",
                halign="center"
            ),
            MDDialogContentContainer(
                content,
                orientation="vertical"
            ),
            MDDialogButtonContainer(
                MDButton(
                    MDButtonText(text="取消"),
                    style="text",
                    on_release=lambda x: dialog.dismiss()
                ),
                spacing=dp(8)
            ),
            size_hint=(0.9, None),
            height=dp(500),
            auto_dismiss=False
        )
        dialog.open()

    def show_birth_date_picker(self, *args):
        """显示出生日期选择器"""
        def on_date_selected(selected_date):
            self.birth_date = selected_date.strftime("%Y-%m-%d")
            self.birth_date_input.text = self.birth_date

        show_kivymd_date_picker(
            callback=on_date_selected,
            title="选择出生日期"
        )

    def _on_phone_input(self, instance, value):
        """手机号输入验证 - 实时检查11位数字格式"""
        if value and not re.match(r'^\d{0,11}$', value):
            # 如果输入不是纯数字或超过11位，恢复到上一个有效值
            instance.text = re.sub(r'[^\d]', '', value)[:11]
        
        # 实时验证手机号格式
        if len(value) == 11 and re.match(r'^1[3-9]\d{9}$', value):
            instance.theme_text_color = "Primary"  # 正确格式
        elif value:
            instance.theme_text_color = "Error"  # 错误格式

    def _on_email_input(self, instance, value):
        """邮箱输入验证 - 实时检查@符号"""
        if value:
            if '@' in value and '.' in value.split('@')[-1]:
                instance.theme_text_color = "Primary"  # 正确格式
            else:
                instance.theme_text_color = "Error"  # 错误格式
        
    def _on_id_card_input(self, instance, value):
        """身份证号输入处理 - 实时验证18位格式"""
        # 限制只能输入数字和X，最多18位
        if value and not re.match(r'^[\dXx]{0,18}$', value):
            instance.text = re.sub(r'[^\dXx]', '', value)[:18]
            return
        
        # 实时验证身份证格式
        if len(value) == 18:
            if re.match(r'^\d{17}[\dXx]$', value):
                instance.theme_text_color = "Primary"  # 正确格式
                # 自动提取信息
                info = self.extract_info_from_id_card(value)
                if info:
                    # 自动填充性别
                    self.gender = info.get('gender', '男')
                    if hasattr(self, 'gender_spinner'):
                        self.gender_spinner.text = self.gender
                    
                    # 自动填充出生日期
                    birth_date = info.get('birth_date', '')
                    if birth_date and hasattr(self, 'birth_date_input'):
                        self.birth_date_input.text = birth_date
                        self.birth_date = birth_date
            else:
                instance.theme_text_color = "Error"  # 错误格式
        elif value:
            instance.theme_text_color = "Error"  # 不足18位
        else:
            instance.theme_text_color = "Primary"  # 空值时恢复正常颜色

    def extract_info_from_id_card(self, id_card_number):
        """从身份证号提取信息"""
        try:
            if len(id_card_number) != 18:
                return None
            
            # 提取出生日期
            birth_year = id_card_number[6:10]
            birth_month = id_card_number[10:12]
            birth_day = id_card_number[12:14]
            birth_date = f"{birth_year}-{birth_month}-{birth_day}"
            
            # 提取性别（第17位数字，奇数为男，偶数为女）
            gender_digit = int(id_card_number[16])
            gender = "男" if gender_digit % 2 == 1 else "女"
            
            return {
                'birth_date': birth_date,
                'gender': gender
            }
        except Exception as e:
            logger.error(f"提取身份证信息失败: {e}")
            return None

    def upload_certificate(self, certificate_type):
        """上传证书"""
        self.current_certificate_type = certificate_type
        
        def on_file_selected(file_path):
            if file_path:
                self._on_certificate_selected(file_path)
        
        # 使用文件管理器选择文件
        self.file_manager.select_file(
            callback=on_file_selected,
            file_types=['jpg', 'jpeg', 'png', 'pdf']
        )

    def _on_certificate_selected(self, file_path):
        """证书文件选择回调"""
        if not file_path:
            return
            
        # 显示上传进度
        self._show_loading_dialog("正在上传证书...")
        
        # 上传文件
        self.file_manager.upload_file(
            file_path=file_path,
            callback=self._on_certificate_uploaded
        )

    def _on_certificate_uploaded(self, success, result=None, error=None):
        """证书上传完成回调"""
        self._dismiss_loading_dialog()
        
        if success and result:
            document_id = result.get('document_id')
            if document_id:
                self.certificate_document_ids.append(document_id)
                
                # 更新对应的证书状态
                if self.current_certificate_type == "medical_license":
                    self.medical_license_taken = True
                    self.medical_license_path = result.get('file_path', '')
                elif self.current_certificate_type == "practice_license":
                    self.practice_license_taken = True
                    self.practice_license_path = result.get('file_path', '')
                
                self._show_message("证书上传成功")
            else:
                self._show_message("证书上传失败：无效的响应")
        else:
            error_msg = error or "证书上传失败"
            self._show_message(f"证书上传失败：{error_msg}")

    def register(self, *args):
        """执行注册"""
        # 验证输入
        validation_result = self.validate_inputs()
        if not validation_result['valid']:
            self._show_message(validation_result['message'])
            return

        # 显示加载对话框
        self._show_loading_dialog("正在注册...")

        # 准备注册数据
        register_data = {
            'username': self.username_input.text.strip(),
            'password': self.password_input.text,
            'real_name': self.name_input.text.strip(),
            'id_card': self.id_card_input.text.strip(),
            'phone': self.phone_input.text.strip(),
            'email': self.email_input.text.strip(),
            'gender': self.gender,
            'birth_date': self.birth_date,
            'workplace': getattr(self.workplace_input, 'text', '').strip(),
            'occupation': getattr(self.occupation_input, 'text', '').strip(),
            'address': getattr(self.address_input, 'text', '').strip(),
            'emergency_contact': self.emergency_contact_input.text.strip(),
            'emergency_phone': self.emergency_phone_input.text.strip(),
            'ethnicity': getattr(self.ethnicity_spinner, 'text', '民族 *') if hasattr(self.ethnicity_spinner, 'text') and self.ethnicity_spinner.text != '民族 *' else '',
            'education': getattr(self.education_spinner, 'text', '教育程度 *') if hasattr(self.education_spinner, 'text') and self.education_spinner.text != '教育程度 *' else '',
            'roles': self._convert_roles_to_ids(self.selected_roles),
            'registration_type': self.registration_type,
            'relationship': self.relationship if self.registration_type == "替他人注册" else "",
            'certificate_document_ids': self.certificate_document_ids
        }

        # 调用API注册
        try:
            response = self.api_client.register_user(register_data)
            self._dismiss_loading_dialog()
            
            if response.get('success'):
                self._show_message("注册成功！即将跳转到登录页面")
                Clock.schedule_once(self.switch_to_login, 2)
            else:
                error_msg = response.get('message', '注册失败')
                self._show_message(f"注册失败：{error_msg}")
        except Exception as e:
            self._dismiss_loading_dialog()
            self.logger.error(f"注册请求失败: {e}")
            self._show_message(f"注册失败：{str(e)}")

    def validate_inputs(self):
        """验证输入数据"""
        # 检查必填字段
        required_fields = {
            'username_input': '用户名',
            'password_input': '密码',
            'confirm_password_input': '确认密码',
            'name_input': '姓名',
            'id_card_input': '身份证号',
            'phone_input': '手机号',
            'emergency_contact_input': '紧急联系人',
            'emergency_phone_input': '紧急联系人电话'
        }
        
        for field_name, field_label in required_fields.items():
            field = getattr(self, field_name, None)
            if not field or not field.text.strip():
                return {'valid': False, 'message': f'{field_label}不能为空'}

        # 检查密码一致性
        if self.password_input.text != self.confirm_password_input.text:
            return {'valid': False, 'message': '两次输入的密码不一致'}

        # 检查密码强度
        password = self.password_input.text
        if len(password) < 6:
            return {'valid': False, 'message': '密码长度不能少于6位'}

        # 检查身份证格式
        id_card = self.id_card_input.text.strip()
        if not self._validate_id_card(id_card):
            return {'valid': False, 'message': '身份证号格式不正确'}

        # 检查手机号格式
        phone = self.phone_input.text.strip()
        if not self._validate_phone(phone):
            return {'valid': False, 'message': '手机号格式不正确'}

        # 检查邮箱格式
        email = self.email_input.text.strip()
        if not self._validate_email(email):
            return {'valid': False, 'message': '邮箱格式不正确'}

        # 检查角色选择
        if not self.selected_roles:
            return {'valid': False, 'message': '请至少选择一个身份'}

        # 检查性别和出生日期
        if not self.gender:
            return {'valid': False, 'message': '请选择性别'}
        
        if not self.birth_date:
            return {'valid': False, 'message': '请选择出生日期'}

        # 检查替他人注册时的关系
        if self.registration_type == "替他人注册" and not self.relationship:
            return {'valid': False, 'message': '请选择与被注册人的关系'}

        # 检查健康顾问证书
        if "健康顾问" in self.selected_roles:
            if not self.medical_license_taken or not self.practice_license_taken:
                return {'valid': False, 'message': '健康顾问需要上传医师资格证书和执业证书'}

        return {'valid': True, 'message': '验证通过'}

    def _validate_id_card(self, id_card):
        """验证身份证号码格式 - 18位数字+X格式"""
        if not id_card:
            return False, "身份证号码不能为空"
        
        if len(id_card) != 18:
            return False, "身份证号码必须为18位"
        
        if not re.match(r'^\d{17}[\dXx]$', id_card):
            return False, "身份证号码格式不正确，应为17位数字+1位数字或X"
        
        # 验证出生日期是否合理
        try:
            birth_year = int(id_card[6:10])
            birth_month = int(id_card[10:12])
            birth_day = int(id_card[12:14])
            
            # 检查年份范围
            current_year = datetime.datetime.now().year
            if birth_year < 1900 or birth_year > current_year:
                return False, "身份证号码中的出生年份不合理"
            
            # 检查月份
            if birth_month < 1 or birth_month > 12:
                return False, "身份证号码中的出生月份不合理"
            
            # 检查日期
            if birth_day < 1 or birth_day > 31:
                return False, "身份证号码中的出生日期不合理"
            
            # 验证具体日期是否存在
            datetime.datetime(birth_year, birth_month, birth_day)
            
        except ValueError:
            return False, "身份证号码中的出生日期不存在"
        
        return True, ""

    def _validate_phone(self, phone):
        """验证手机号码格式 - 11位数字，1开头"""
        if not phone:
            return False, "手机号码不能为空"
        
        if len(phone) != 11:
            return False, "手机号码必须为11位数字"
        
        if not re.match(r'^1[3-9]\d{9}$', phone):
            return False, "手机号码格式不正确，应为1开头的11位数字"
        
        return True, ""

    def _validate_email(self, email):
        """验证邮箱格式 - 必须包含@符号和域名"""
        if not email:
            return True, ""  # 邮箱为可选字段
        
        if '@' not in email:
            return False, "邮箱地址必须包含@符号"
        
        # 基本邮箱格式验证
        if not re.match(r'^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$', email):
            return False, "邮箱地址格式不正确"
        
        return True, ""

    def _convert_roles_to_ids(self, roles):
        """将角色名称转换为ID"""
        role_mapping = {
            "个人用户": 1,
            "单位管理员": 2,
            "健康顾问": 3,
            "超级管理员": 4,
            "陪诊师": 5
        }
        return [role_mapping.get(role, 1) for role in roles]

    def switch_to_login(self, dt):
        """切换到登录页面"""
        if self.manager:
            self.manager.current = 'login_screen'

    def back_to_login(self, *args):
        """返回登录页面"""
        if self.manager:
            self.manager.current = 'login_screen'

    def _show_loading_dialog(self, message="加载中..."):
        """显示加载对话框"""
        if self._loading_dialog:
            self._loading_dialog.dismiss()
        
        self._loading_dialog = MDDialog(
            title=message,
            auto_dismiss=False
        )
        self._loading_dialog.open()

    def _dismiss_loading_dialog(self):
        """关闭加载对话框"""
        if self._loading_dialog:
            self._loading_dialog.dismiss()
            self._loading_dialog = None

    def _show_message(self, message):
        """显示消息"""
        snackbar = MDSnackbar(MDSnackbarText(text=message))
        snackbar.open()

    def on_enter(self, *args):
        """进入屏幕时调用"""
        # 初始化UI状态
        self._update_registration_ui()
        self._update_role_cards_visual_state()

    def _ensure_logo_display(self, dt):
        """确保logo显示 - 延迟调用以确保UI完全初始化"""
        try:
            # 调用父类的logo设置方法
            if hasattr(self, 'setup_logo'):
                self.setup_logo()
            
            # 如果有top_bar，确保logo可见
            if hasattr(self, 'ids') and 'top_bar' in self.ids:
                top_bar = self.ids.top_bar
                if hasattr(top_bar, 'ids') and 'logo' in top_bar.ids:
                    logo = top_bar.ids.logo
                    logo.opacity = 1
                    logo.size_hint = (None, None)
                    logo.size = (dp(40), dp(40))
                    
            self.logger.info("Logo显示设置完成")
        except Exception as e:
            self.logger.error(f"设置logo显示时出错: {e}")

    def _show_selection_feedback(self, role_name):
        """显示选择反馈"""
        # 可以添加震动反馈或声音反馈
        try:
            from plyer import vibrator
            vibrator.vibrate(0.1)  # 轻微震动反馈
        except:
            pass  # 如果不支持震动，忽略

    def _show_bottom_layout(self, *args):
        bottom_layout = MDBoxLayout(
            orientation='horizontal',
            spacing=dp(40),
            size_hint_y=None,
            height=dp(56),
            pos_hint={'center_x': 0.5}
        )
        bottom_layout.add_widget(info_title)
        
        # 信息图标行
        info_icons = MDBoxLayout(
            orientation='horizontal',
            spacing=dp(40),
            size_hint_y=None,
            height=dp(56),
            pos_hint={'center_x': 0.5}
        )
        
        # 添加信息图标
        icons = [
            ("account-circle", "个人信息"),
            ("phone", "联系方式"),
            ("map-marker", "地址信息"),
            ("dots-horizontal", "更多")
        ]
        
        for icon, tooltip in icons:
            icon_button = MDIconButton(
                icon=icon,
                theme_icon_color="Primary",
                size_hint=(None, None),
                size=(dp(48), dp(48))
            )
            info_icons.add_widget(icon_button)
        
        bottom_layout.add_widget(info_icons)
        return bottom_layout