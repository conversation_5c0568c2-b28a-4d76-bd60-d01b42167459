# -*- coding: utf-8 -*-
"""
问卷表单屏幕
基于FormScreen基类实现
"""

import logging
import sys
import os
from typing import Optional, List, Any

# 添加项目根目录到Python路径
current_dir = os.path.dirname(os.path.abspath(__file__))
parent_dir = os.path.dirname(current_dir)
if parent_dir not in sys.path:
    sys.path.insert(0, parent_dir)

from kivy.factory import Factory
from kivy.lang import Builder
from kivy.metrics import dp
from kivy.clock import Clock  # 添加Clock导入
from kivymd.app import MDApp

# 修复导入路径 - 使用相对导入
from .form_screen import FormScreen
from utils.cloud_api import get_cloud_api
from utils.user_manager import get_user_manager

# 获取日志记录器
logger = logging.getLogger(__name__)


class QuestionnaireFormScreen(FormScreen):
    """问卷表单屏幕"""

    def __init__(self, **kwargs):
        super().__init__(**kwargs)
        self.title = "问卷调查"
        self._ui_initialized = False  # 添加初始化标志，防止重复初始化
        
    def on_enter(self, *args):
        """进入屏幕时调用 - 优化版本，避免重复初始化"""
        logger.info(f"进入{self.__class__.__name__}页面")
        
        # 检查是否已经初始化过UI，避免重复初始化
        if not self._ui_initialized:
            # 只在第一次进入时初始化UI
            Clock.schedule_once(self.init_ui, 0.1)
        else:
            # 如果已经初始化过，只刷新数据
            logger.info("UI已初始化，仅刷新数据")
            Clock.schedule_once(self._refresh_data_only, 0.1)
            
    def _refresh_data_only(self, dt=None):
        """仅刷新数据，不重新创建UI组件"""
        try:
            # 获取最新数据
            self.current_data = self.get_current_data()
            if self.current_data:
                # 更新标题
                if hasattr(self, 'ids') and 'title_label' in self.ids:
                    title = self.current_data.get('title', '问卷调查')
                    self.ids.title_label.text = title
                    
                logger.info("数据刷新完成")
            else:
                logger.warning("未找到数据，执行完整初始化")
                self.init_ui()
                
        except Exception as e:
            logger.error(f"刷新数据失败: {e}")
            # 如果刷新失败，执行完整初始化
            self.init_ui()
            
    def init_ui(self, dt=None):
        """初始化UI - 优化版本，添加重复初始化检查"""
        try:
            logger.info(f"初始化{self.__class__.__name__} UI")
            
            # 调用父类的初始化方法
            super().init_ui(dt)
            
            # 标记UI已初始化
            self._ui_initialized = True
            logger.info("UI初始化完成并标记")
            
        except Exception as e:
            logger.error(f"初始化UI失败: {e}")
            self._ui_initialized = False  # 初始化失败时重置标志

    def _get_theme_color(self, color_name: str, default_color: Optional[List[float]] = None) -> List[float]:
        """
        安全地获取主题颜色属性
        重写基类方法以处理QuestionnaireFormScreen的特殊情况
        """
        if default_color is None:
            default_color = [0.5, 0.5, 0.5, 1.0]  # 默认灰色
            
        try:
            # 首先尝试使用self.app（基类中设置的）
            if hasattr(self, 'app') and self.app and hasattr(self.app, 'theme') and self.app.theme:
                theme = self.app.theme
                if hasattr(theme, color_name):
                    color = getattr(theme, color_name)
                    if color is not None:
                        return color
            else:
                # 如果self.app不可用，尝试直接获取运行中的应用
                from kivymd.app import MDApp
                app = MDApp.get_running_app()
                if app and hasattr(app, 'theme') and app.theme:
                    theme = app.theme
                    if hasattr(theme, color_name):
                        color = getattr(theme, color_name)
                        if color is not None:
                            return color
        except Exception as e:
            logger.warning(f"获取主题颜色 {color_name} 时出错: {e}")
            
        return default_color if default_color is not None else [0.5, 0.5, 0.5, 1.0]

    def _get_theme_attr(self, attr_name: str, default_value: Any = None) -> Any:
        """
        安全地获取主题属性
        重写基类方法以处理QuestionnaireFormScreen的特殊情况
        """
        try:
            # 首先尝试使用self.app（基类中设置的）
            if hasattr(self, 'app') and self.app and hasattr(self.app, 'theme') and self.app.theme:
                theme = self.app.theme
                if hasattr(theme, attr_name):
                    value = getattr(theme, attr_name)
                    if value is not None:
                        return value
            else:
                # 如果self.app不可用，尝试直接获取运行中的应用
                from kivymd.app import MDApp
                app = MDApp.get_running_app()
                if app and hasattr(app, 'theme') and app.theme:
                    theme = app.theme
                    if hasattr(theme, attr_name):
                        value = getattr(theme, attr_name)
                        if value is not None:
                            return value
        except Exception as e:
            logger.warning(f"获取主题属性 {attr_name} 时出错: {e}")
            
        return default_value

    def get_current_data(self):
        """获取当前问卷数据"""
        app = MDApp.get_running_app()
        return getattr(app, 'questionnaire_to_fill', None)

    def get_questions(self):
        """获取问卷问题列表 - 优化版本，减少警告日志"""
        questions = super().get_questions()
        
        # 如果没有获取到问题，尝试从API获取
        if not questions:
            questionnaire_id = self.current_data.get('id')
            title = self.current_data.get('title', '未知问卷')
            
            if questionnaire_id:
                logger.info(f"[获取问卷问题，ID: {questionnaire_id}] {title}")
                cloud_api = get_cloud_api()
                if cloud_api and cloud_api.is_authenticated():
                    # 多种方式获取问题
                    result = None
                    
                    # 首先尝试get_questionnaire_questions方法
                    try:
                        result = cloud_api.get_questionnaire_questions(questionnaire_id)
                        if result and 'questions' in result and result['questions']:
                            logger.info(f"从专门端点获取问题列表，共 {len(result['questions'])} 个问题")
                    except Exception as e:
                        logger.debug(f"get_questionnaire_questions方法失败: {e}")
                    
                    # 如果失败，尝试get_questionnaire_detail方法
                    if not result or 'questions' not in result or not result['questions']:
                        try:
                            result = cloud_api.get_questionnaire_detail(questionnaire_id)
                            if result and isinstance(result, dict):
                                logger.debug(f"[问卷详情API原始响应类型] {type(result)}")
                        except Exception as e:
                            logger.debug(f"get_questionnaire_detail方法失败: {e}")
                    
                    # 从结果中提取问题
                    if result and isinstance(result, dict):
                        if 'questions' in result and result['questions']:
                            questions = result['questions']
                        elif 'template' in result and isinstance(result['template'], dict) and 'questions' in result['template']:
                            questions = result['template']['questions']
                        elif 'data' in result and isinstance(result['data'], dict):
                            if 'questions' in result['data'] and result['data']['questions']:
                                questions = result['data']['questions']
                            elif 'template' in result['data'] and isinstance(result['data']['template'], dict) and 'questions' in result['data']['template']:
                                questions = result['data']['template']['questions']
                    
                    # 标准化问题格式
                    questions = self.standardize_questions(questions) if questions else []
                    
                    if questions:
                        logger.info(f"[最终获得的问题数量] {len(questions)}")
                    else:
                        # 只在确实无法获取问题时才记录警告
                        logger.info(f"问卷 {title} (ID: {questionnaire_id}) 暂无问题列表，将使用默认问题模板")
        
        # 如果仍然没有问题，创建默认问题
        if not questions:
            logger.info("创建默认问题模板")
            question_count = 5  # 默认创建5个问题
            if self.current_data and self.current_data.get('question_count', 0) > 0:
                question_count = self.current_data.get('question_count')

            questions = [
                {
                    'id': i + 1,
                    'text': f'问题 {i + 1}',
                    'type': 'radio',
                    'options': [
                        {'value': str(j), 'label': f'选项{j}'} 
                        for j in range(1, 6)
                    ],
                    'required': True
                } for i in range(question_count)
            ]
        
        return questions

    def add_description(self, container):
        """添加问卷描述"""
        try:
            description = ""
            
            if self.current_data:
                # 尝试从不同位置获取描述
                template = self.current_data.get('template', {})
                if isinstance(template, dict):
                    template_description = template.get('description')
                else:
                    template_description = None

                description = (
                    self.current_data.get('description') or
                    template_description or
                    ""
                )

            # 如果有描述，则显示
            if description:
                super().add_description(container)

        except Exception as e:
            logger.error(f"添加问卷描述时出错: {e}")

    def submit_api_call(self, cloud_api, submission_data):
        """调用问卷提交API"""
        user_info = get_user_manager().get_user_info()
        return cloud_api.submit_mobile_questionnaire(
            questionnaire_id=submission_data['id'],
            answers=submission_data['answers'],
            custom_id=user_info.get('custom_id') if user_info else None,
            template_id=submission_data['template_id']
        )

    def refresh_survey_data(self, survey_screen):
        """刷新survey_screen的问卷数据"""
        survey_screen.load_questionnaires()


# 注册屏幕
Factory.register('QuestionnaireFormScreen', cls=QuestionnaireFormScreen)

# 加载KV字符串 (使用基类的KV)
from .form_screen import FORM_SCREEN_KV
Builder.load_string(FORM_SCREEN_KV.replace("<FormScreen>:", "<QuestionnaireFormScreen>:"))
logger.info("QuestionnaireFormScreen KV loaded")