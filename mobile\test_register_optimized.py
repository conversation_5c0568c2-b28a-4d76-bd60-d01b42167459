#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试优化后的注册页面功能
验证BaseScreen集成、UI显示、数据验证等功能
"""

import sys
import os
import logging

# 添加项目路径
current_dir = os.path.dirname(os.path.abspath(__file__))
parent_dir = os.path.dirname(current_dir)
if parent_dir not in sys.path:
    sys.path.insert(0, parent_dir)

# 配置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

def test_register_screen_import():
    """测试注册页面导入"""
    try:
        from mobile.screens.register_screen_optimized import RegisterScreen
        logger.info("✅ RegisterScreen 导入成功")
        return True, RegisterScreen
    except Exception as e:
        logger.error(f"❌ RegisterScreen 导入失败: {e}")
        return False, None

def test_base_screen_inheritance(RegisterScreen):
    """测试BaseScreen继承"""
    try:
        from mobile.screens.base_screen import BaseScreen
        
        # 检查继承关系
        if issubclass(RegisterScreen, BaseScreen):
            logger.info("✅ RegisterScreen 正确继承了 BaseScreen")
        else:
            logger.error("❌ RegisterScreen 未继承 BaseScreen")
            return False
            
        # 检查必要方法
        required_methods = ['do_content_setup', '__init__']
        for method in required_methods:
            if hasattr(RegisterScreen, method):
                logger.info(f"✅ RegisterScreen 包含必要方法: {method}")
            else:
                logger.error(f"❌ RegisterScreen 缺少必要方法: {method}")
                return False
                
        return True
    except Exception as e:
        logger.error(f"❌ BaseScreen继承测试失败: {e}")
        return False

def test_validation_methods(RegisterScreen):
    """测试数据验证方法"""
    try:
        # 创建实例
        screen = RegisterScreen()
        
        # 测试身份证验证
        valid_id = "110101199001011234"  # 示例身份证号
        invalid_id = "123456789"
        
        if hasattr(screen, '_validate_id_card'):
            # 注意：这里的身份证号可能不会通过校验码验证，但格式是正确的
            logger.info("✅ RegisterScreen 包含身份证验证方法")
        else:
            logger.error("❌ RegisterScreen 缺少身份证验证方法")
            return False
            
        # 测试邮箱验证
        if hasattr(screen, '_validate_email'):
            valid_email = "<EMAIL>"
            invalid_email = "invalid-email"
            
            if screen._validate_email(valid_email):
                logger.info("✅ 邮箱验证方法工作正常")
            else:
                logger.warning("⚠️ 邮箱验证可能过于严格")
                
            if not screen._validate_email(invalid_email):
                logger.info("✅ 邮箱验证正确拒绝无效邮箱")
            else:
                logger.error("❌ 邮箱验证未能拒绝无效邮箱")
                return False
        else:
            logger.error("❌ RegisterScreen 缺少邮箱验证方法")
            return False
            
        # 测试手机号验证
        if hasattr(screen, '_validate_phone'):
            valid_phone = "13812345678"
            invalid_phone = "123456"
            
            if screen._validate_phone(valid_phone):
                logger.info("✅ 手机号验证方法工作正常")
            else:
                logger.error("❌ 手机号验证拒绝了有效手机号")
                return False
                
            if not screen._validate_phone(invalid_phone):
                logger.info("✅ 手机号验证正确拒绝无效手机号")
            else:
                logger.error("❌ 手机号验证未能拒绝无效手机号")
                return False
        else:
            logger.error("❌ RegisterScreen 缺少手机号验证方法")
            return False
            
        return True
    except Exception as e:
        logger.error(f"❌ 数据验证方法测试失败: {e}")
        return False

def test_ui_methods(RegisterScreen):
    """测试UI相关方法"""
    try:
        screen = RegisterScreen()
        
        # 检查UI创建方法
        ui_methods = [
            '_create_card',
            '_create_toggle_button', 
            '_create_role_card',
            '_create_text_field',
            '_add_page_title',
            '_add_registration_type_section',
            '_add_role_selection_section',
            '_add_basic_info_section'
        ]
        
        for method in ui_methods:
            if hasattr(screen, method):
                logger.info(f"✅ RegisterScreen 包含UI方法: {method}")
            else:
                logger.error(f"❌ RegisterScreen 缺少UI方法: {method}")
                return False
                
        return True
    except Exception as e:
        logger.error(f"❌ UI方法测试失败: {e}")
        return False

def test_business_logic_methods(RegisterScreen):
    """测试业务逻辑方法"""
    try:
        screen = RegisterScreen()
        
        # 检查业务逻辑方法
        business_methods = [
            'set_registration_type',
            'select_role',
            'upload_certificate',
            'register',
            'validate_inputs',
            'extract_info_from_id_card'
        ]
        
        for method in business_methods:
            if hasattr(screen, method):
                logger.info(f"✅ RegisterScreen 包含业务方法: {method}")
            else:
                logger.error(f"❌ RegisterScreen 缺少业务方法: {method}")
                return False
                
        # 测试角色选择逻辑
        screen.select_role("个人用户")
        if "个人用户" in screen.selected_roles:
            logger.info("✅ 角色选择逻辑工作正常")
        else:
            logger.error("❌ 角色选择逻辑失败")
            return False
            
        # 测试注册类型设置
        screen.set_registration_type("替他人注册")
        if screen.registration_type == "替他人注册":
            logger.info("✅ 注册类型设置工作正常")
        else:
            logger.error("❌ 注册类型设置失败")
            return False
            
        return True
    except Exception as e:
        logger.error(f"❌ 业务逻辑方法测试失败: {e}")
        return False

def test_properties(RegisterScreen):
    """测试属性定义"""
    try:
        from kivymd.app import MDApp
        
        # 创建临时应用实例
        class TestApp(MDApp):
            def build(self):
                return None
        
        app = TestApp()
        app.build()
        
        # 现在可以安全地创建RegisterScreen实例
        screen = RegisterScreen()
        
        # 检查必要属性
        required_properties = [
            'selected_roles',
            'gender', 
            'registration_type',
            'relationship',
            'birth_date',
            'medical_license_taken',
            'practice_license_taken'
        ]
        
        for prop in required_properties:
            if hasattr(screen, prop):
                logger.info(f"✅ RegisterScreen 包含属性: {prop}")
            else:
                logger.error(f"❌ RegisterScreen 缺少属性: {prop}")
                return False
                
        return True
    except Exception as e:
        logger.error(f"❌ 属性测试失败: {e}")
        return False

def main():
    """主测试函数"""
    logger.info("开始测试优化后的注册页面...")
    
    # 测试导入
    success, RegisterScreen = test_register_screen_import()
    if not success:
        return False
        
    # 测试BaseScreen继承
    if not test_base_screen_inheritance(RegisterScreen):
        return False
        
    # 测试属性定义
    if not test_properties(RegisterScreen):
        return False
        
    # 测试数据验证方法
    if not test_validation_methods(RegisterScreen):
        return False
        
    # 测试UI方法
    if not test_ui_methods(RegisterScreen):
        return False
        
    # 测试业务逻辑方法
    if not test_business_logic_methods(RegisterScreen):
        return False
        
    logger.info("🎉 所有测试通过！优化后的注册页面功能完整。")
    return True

if __name__ == "__main__":
    success = main()
    if success:
        print("\n✅ 测试结果：优化后的注册页面功能完整，可以正常使用。")
        sys.exit(0)
    else:
        print("\n❌ 测试结果：优化后的注册页面存在问题，需要修复。")
        sys.exit(1)