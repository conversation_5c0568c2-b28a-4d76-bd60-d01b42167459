# -*- coding: utf-8 -*-
"""
统一数据库模型 - 基于后端表结构的移动端数据库管理
与后端保持一致的表名和字段结构
"""

import sqlite3
import os
import json
from datetime import datetime, date
from typing import Optional, List, Dict, Any
from pathlib import Path

class UnifiedDBManager:
    """
    统一数据库管理器
    负责创建和管理与后端一致的数据库表结构
    """
    
    def __init__(self, db_path: str):
        """
        初始化数据库管理器
        
        Args:
            db_path: 数据库文件路径
        """
        self.db_path = db_path
        self.ensure_db_directory()
    
    def ensure_db_directory(self):
        """
        确保数据库目录存在
        """
        db_dir = os.path.dirname(self.db_path)
        if db_dir and not os.path.exists(db_dir):
            os.makedirs(db_dir, exist_ok=True)
    
    def get_connection(self) -> sqlite3.Connection:
        """
        获取数据库连接
        
        Returns:
            sqlite3.Connection: 数据库连接对象
        """
        conn = sqlite3.connect(self.db_path)
        conn.row_factory = sqlite3.Row  # 启用字典式访问
        # 启用外键约束
        conn.execute("PRAGMA foreign_keys = ON")
        return conn
    
    def create_all_tables(self, custom_id: str):
        """
        为指定用户创建所有数据表
        
        Args:
            custom_id: 用户自定义ID
        """
        with self.get_connection() as conn:
            try:
                # 创建用户相关表
                self._create_users_table(conn)
                
                # 创建健康记录相关表
                self._create_health_records_table(conn)
                self._create_health_overviews_table(conn)
                
                # 创建医疗记录相关表
                self._create_medical_records_table(conn)
                self._create_inpatient_records_table(conn)
                self._create_surgery_records_table(conn)
                
                # 创建药物相关表
                self._create_medications_table(conn)
                
                # 创建评估和问卷相关表
                self._create_assessments_table(conn)
                self._create_assessment_items_table(conn)
                self._create_assessment_responses_table(conn)
                self._create_assessment_templates_table(conn)
                self._create_assessment_template_questions_table(conn)
                
                # 创建问卷相关表
                self._create_questionnaires_table(conn)
                self._create_questionnaire_items_table(conn)
                self._create_questionnaire_responses_table(conn)
                self._create_questionnaire_templates_table(conn)
                self._create_questionnaire_template_questions_table(conn)
                
                # 创建随访和日记相关表
                self._create_follow_up_records_table(conn)
                self._create_health_diaries_table(conn)
                
                # 创建文档相关表
                self._create_documents_table(conn)
                
                # 创建可穿戴设备相关表
                self._create_wearable_device_table(conn)
                self._create_heart_rate_data_table(conn)
                self._create_blood_pressure_data_table(conn)
                self._create_sleep_data_table(conn)
                self._create_activity_data_table(conn)
                
                # 创建同步记录表
                self._create_sync_record_table(conn)
                
                # 创建索引
                self._create_indexes(conn)
                
                conn.commit()
                print(f"成功为用户 {custom_id} 创建所有数据表")
                
            except Exception as e:
                conn.rollback()
                print(f"创建数据表失败: {e}")
                raise
    
    def _create_users_table(self, conn: sqlite3.Connection):
        """
        创建用户表 - 与后端users表保持一致
        """
        conn.execute("""
            CREATE TABLE IF NOT EXISTS users (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                username TEXT UNIQUE NOT NULL,
                email TEXT NOT NULL,
                phone TEXT,
                hashed_password TEXT NOT NULL,
                full_name TEXT,
                role TEXT DEFAULT 'personal',
                is_active BOOLEAN DEFAULT 1,
                is_superuser BOOLEAN DEFAULT 0,
                id_number TEXT UNIQUE,
                gender TEXT,
                birth_date DATE,
                address TEXT,
                custom_id TEXT UNIQUE NOT NULL,
                profile_photo TEXT,
                emergency_contact TEXT,
                emergency_phone TEXT,
                registration_type TEXT,
                relationship_type TEXT,
                additional_roles TEXT,
                verification_status TEXT DEFAULT 'verified',
                is_first_login BOOLEAN DEFAULT 1,
                role_application_status TEXT,
                role_application_role TEXT,
                password_reset_at DATETIME,
                created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
                updated_at DATETIME DEFAULT CURRENT_TIMESTAMP,
                status TEXT DEFAULT 'active'
            )
        """)
    
    def _create_health_records_table(self, conn: sqlite3.Connection):
        """
        创建健康记录表
        """
        conn.execute("""
            CREATE TABLE IF NOT EXISTS health_records (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                custom_id TEXT NOT NULL,
                recordtype TEXT NOT NULL,
                title TEXT NOT NULL,
                description TEXT,
                content TEXT,
                priority TEXT DEFAULT 'normal',
                is_important BOOLEAN DEFAULT 0,
                created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
                updated_at DATETIME DEFAULT CURRENT_TIMESTAMP,
                FOREIGN KEY (custom_id) REFERENCES users(custom_id) ON DELETE CASCADE
            )
        """)
    
    def _create_health_overviews_table(self, conn: sqlite3.Connection):
        """
        创建健康状况概览表
        """
        conn.execute("""
            CREATE TABLE IF NOT EXISTS health_overviews (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                custom_id TEXT UNIQUE NOT NULL,
                height REAL,
                weight REAL,
                blood_type TEXT,
                allergies TEXT,
                chronic_diseases TEXT,
                family_history TEXT,
                current_medications TEXT,
                lifestyle_summary TEXT,
                created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
                updated_at DATETIME DEFAULT CURRENT_TIMESTAMP,
                FOREIGN KEY (custom_id) REFERENCES users(custom_id) ON DELETE CASCADE
            )
        """)
    
    def _create_medical_records_table(self, conn: sqlite3.Connection):
        """
        创建医疗记录表
        """
        conn.execute("""
            CREATE TABLE IF NOT EXISTS medical_records (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                custom_id TEXT NOT NULL,
                recordtype TEXT NOT NULL,
                hospital_name TEXT NOT NULL,
                department TEXT,
                doctor_name TEXT,
                visit_date DATETIME,
                diagnosis TEXT,
                treatment TEXT,
                prescription TEXT,
                notes TEXT,
                is_important BOOLEAN DEFAULT 0,
                created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
                updated_at DATETIME DEFAULT CURRENT_TIMESTAMP,
                FOREIGN KEY (custom_id) REFERENCES users(custom_id) ON DELETE CASCADE
            )
        """)
    
    def _create_inpatient_records_table(self, conn: sqlite3.Connection):
        """
        创建住院记录表
        """
        conn.execute("""
            CREATE TABLE IF NOT EXISTS inpatient_records (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                custom_id TEXT NOT NULL,
                medical_record_id INTEGER,
                hospital_name TEXT NOT NULL,
                department TEXT,
                admission_date DATETIME,
                discharge_date DATETIME,
                admission_diagnosis TEXT,
                discharge_diagnosis TEXT,
                treatment_summary TEXT,
                doctor_advice TEXT,
                notes TEXT,
                created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
                updated_at DATETIME DEFAULT CURRENT_TIMESTAMP,
                FOREIGN KEY (custom_id) REFERENCES users(custom_id) ON DELETE CASCADE,
                FOREIGN KEY (medical_record_id) REFERENCES medical_records(id) ON DELETE SET NULL
            )
        """)
    
    def _create_surgery_records_table(self, conn: sqlite3.Connection):
        """
        创建手术记录表
        """
        conn.execute("""
            CREATE TABLE IF NOT EXISTS surgery_records (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                custom_id TEXT NOT NULL,
                medical_record_id INTEGER,
                hospital_name TEXT NOT NULL,
                department TEXT,
                surgery_name TEXT NOT NULL,
                surgery_date DATETIME,
                surgeon TEXT,
                anesthesia_type TEXT,
                surgery_description TEXT,
                complications TEXT,
                notes TEXT,
                created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
                updated_at DATETIME DEFAULT CURRENT_TIMESTAMP,
                FOREIGN KEY (custom_id) REFERENCES users(custom_id) ON DELETE CASCADE,
                FOREIGN KEY (medical_record_id) REFERENCES medical_records(id) ON DELETE SET NULL
            )
        """)
    
    def _create_medications_table(self, conn: sqlite3.Connection):
        """
        创建药物表
        """
        # 与后端 app/models/medication.Medication 保持一致的字段定义 (23 列)
        # 说明：移动端历史上存在 prescriber/hospital/purpose/side_effects/specification/is_current 等额外列。
        # 为避免在后端 schema 中引入这些移动端专有列，迁移时会把这些额外信息合并到 notes 或 reminder_settings（JSON）字段中。
        conn.execute("""
            CREATE TABLE IF NOT EXISTS medications (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                custom_id TEXT NOT NULL,
                sync_status INTEGER DEFAULT 0,
                cloud_id TEXT,
                name TEXT NOT NULL,
                dosage TEXT,
                frequency TEXT,
                start_date DATETIME,
                end_date DATETIME,
                instructions TEXT,
                prescription_required INTEGER DEFAULT 0,
                notes TEXT,
                medication_type TEXT,
                created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
                updated_at DATETIME DEFAULT CURRENT_TIMESTAMP,
                status TEXT DEFAULT 'active',
                stop_reason TEXT,
                stop_date DATETIME,
                reason TEXT,
                reminder_times TEXT,
                review_reminder TEXT,
                reminder_enabled INTEGER DEFAULT 0,
                reminder_settings TEXT,
                -- 注：不再包含 prescriber/hospital/purpose/side_effects/specification/is_current
                FOREIGN KEY (custom_id) REFERENCES users(custom_id) ON DELETE CASCADE
            )
        """)
    
    def _create_assessments_table(self, conn: sqlite3.Connection):
        """
        创建评估量表表
        """
        conn.execute("""
            CREATE TABLE IF NOT EXISTS assessments (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                custom_id TEXT NOT NULL,
                template_id INTEGER,
                assessment_type TEXT NOT NULL,
                name TEXT NOT NULL,
                version TEXT,
                round_number INTEGER DEFAULT 1,
                sequence_number INTEGER DEFAULT 1,
                unique_identifier TEXT,
                completed_at DATETIME,
                assessor TEXT,
                score REAL,
                max_score REAL,
                result TEXT,
                conclusion TEXT,
                notes TEXT,
                status TEXT DEFAULT 'draft',
                created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
                updated_at DATETIME DEFAULT CURRENT_TIMESTAMP,
                last_reminded_at DATETIME,
                FOREIGN KEY (custom_id) REFERENCES users(custom_id) ON DELETE CASCADE,
                FOREIGN KEY (template_id) REFERENCES assessment_templates(id) ON DELETE SET NULL
            )
        """)
    
    def _create_assessment_items_table(self, conn: sqlite3.Connection):
        """
        创建评估量表项目表
        """
        conn.execute("""
            CREATE TABLE IF NOT EXISTS assessment_items (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                assessment_id INTEGER NOT NULL,
                question_id TEXT NOT NULL,
                question_text TEXT NOT NULL,
                question_type TEXT NOT NULL,
                options TEXT,
                order_num INTEGER,
                is_required BOOLEAN DEFAULT 1,
                jump_logic TEXT,
                answer TEXT,
                score REAL,
                notes TEXT,
                created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
                FOREIGN KEY (assessment_id) REFERENCES assessments(id) ON DELETE CASCADE
            )
        """)
    
    def _create_assessment_responses_table(self, conn: sqlite3.Connection):
        """
        创建评估量表填写记录表
        """
        conn.execute("""
            CREATE TABLE IF NOT EXISTS assessment_responses (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                assessment_id INTEGER NOT NULL,
                custom_id TEXT NOT NULL,
                answers TEXT NOT NULL,
                score REAL,
                dimension_scores TEXT,
                result TEXT,
                notes TEXT,
                status TEXT DEFAULT 'completed',
                completed_at DATETIME DEFAULT CURRENT_TIMESTAMP,
                created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
                updated_at DATETIME DEFAULT CURRENT_TIMESTAMP,
                FOREIGN KEY (assessment_id) REFERENCES assessments(id) ON DELETE CASCADE,
                FOREIGN KEY (custom_id) REFERENCES users(custom_id) ON DELETE CASCADE
            )
        """)
    
    def _create_assessment_templates_table(self, conn: sqlite3.Connection):
        """
        创建评估量表模板表
        """
        conn.execute("""
            CREATE TABLE IF NOT EXISTS assessment_templates (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                template_key TEXT UNIQUE,
                assessment_type TEXT NOT NULL,
                sub_type TEXT,
                name TEXT NOT NULL,
                version TEXT,
                description TEXT,
                instructions TEXT,
                scoring_method TEXT,
                max_score REAL,
                result_ranges TEXT,
                dimensions TEXT,
                is_active BOOLEAN DEFAULT 1,
                status TEXT DEFAULT 'draft',
                created_by INTEGER,
                created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
                updated_at DATETIME DEFAULT CURRENT_TIMESTAMP
            )
        """)
    
    def _create_assessment_template_questions_table(self, conn: sqlite3.Connection):
        """
        创建评估量表模板问题表
        """
        conn.execute("""
            CREATE TABLE IF NOT EXISTS assessment_template_questions (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                template_id INTEGER NOT NULL,
                question_id TEXT NOT NULL,
                question_text TEXT NOT NULL,
                question_type TEXT NOT NULL,
                options TEXT,
                scoring TEXT,
                order_num INTEGER NOT NULL,
                is_required BOOLEAN DEFAULT 1,
                dimension_key TEXT,
                jump_logic TEXT,
                created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
                updated_at DATETIME DEFAULT CURRENT_TIMESTAMP,
                FOREIGN KEY (template_id) REFERENCES assessment_templates(id) ON DELETE CASCADE
            )
        """)
    
    def _create_questionnaires_table(self, conn: sqlite3.Connection):
        """
        创建问卷表
        """
        conn.execute("""
            CREATE TABLE IF NOT EXISTS questionnaires (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                title TEXT NOT NULL,
                description TEXT,
                category TEXT,
                questions_count INTEGER DEFAULT 0,
                estimated_time TEXT,
                max_score INTEGER DEFAULT 0,
                template_id INTEGER,
                custom_id TEXT NOT NULL,
                questionnaire_type TEXT,
                notes TEXT,
                status TEXT DEFAULT 'active',
                version TEXT DEFAULT '1.0',
                created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
                updated_at DATETIME DEFAULT CURRENT_TIMESTAMP,
                created_by INTEGER,
                FOREIGN KEY (custom_id) REFERENCES users(custom_id) ON DELETE CASCADE,
                FOREIGN KEY (template_id) REFERENCES questionnaire_templates(id) ON DELETE SET NULL
            )
        """)
    
    def _create_questionnaire_items_table(self, conn: sqlite3.Connection):
        """
        创建问卷题目表
        """
        conn.execute("""
            CREATE TABLE IF NOT EXISTS questionnaire_items (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                questionnaire_id INTEGER NOT NULL,
                question_id TEXT NOT NULL,
                question_text TEXT NOT NULL,
                question_type TEXT NOT NULL,
                options TEXT,
                order_num INTEGER,
                is_required BOOLEAN DEFAULT 1,
                jump_logic TEXT,
                answer TEXT,
                score REAL,
                notes TEXT,
                created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
                FOREIGN KEY (questionnaire_id) REFERENCES questionnaires(id) ON DELETE CASCADE
            )
        """)
    
    def _create_questionnaire_responses_table(self, conn: sqlite3.Connection):
        """
        创建问卷回答表
        """
        conn.execute("""
            CREATE TABLE IF NOT EXISTS questionnaire_responses (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                questionnaire_id INTEGER NOT NULL,
                custom_id TEXT NOT NULL,
                total_score REAL,
                dimension_scores TEXT,
                status TEXT DEFAULT 'completed',
                answers TEXT,
                report TEXT,
                result_summary TEXT,
                completed_at DATETIME DEFAULT CURRENT_TIMESTAMP,
                created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
                updated_at DATETIME DEFAULT CURRENT_TIMESTAMP,
                FOREIGN KEY (questionnaire_id) REFERENCES questionnaires(id) ON DELETE CASCADE,
                FOREIGN KEY (custom_id) REFERENCES users(custom_id) ON DELETE CASCADE
            )
        """)
    
    def _create_questionnaire_templates_table(self, conn: sqlite3.Connection):
        """
        创建问卷模板表
        """
        conn.execute("""
            CREATE TABLE IF NOT EXISTS questionnaire_templates (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                template_key TEXT UNIQUE NOT NULL,
                name TEXT NOT NULL,
                name_en TEXT,
                description TEXT,
                instructions TEXT,
                category TEXT,
                type TEXT,
                status TEXT DEFAULT 'active',
                version TEXT DEFAULT '1.0',
                is_active BOOLEAN DEFAULT 1,
                created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
                updated_at DATETIME DEFAULT CURRENT_TIMESTAMP
            )
        """)
    
    def _create_questionnaire_template_questions_table(self, conn: sqlite3.Connection):
        """
        创建问卷模板问题表
        """
        conn.execute("""
            CREATE TABLE IF NOT EXISTS questionnaire_template_questions (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                template_id INTEGER NOT NULL,
                question_id TEXT NOT NULL,
                question_text TEXT NOT NULL,
                question_type TEXT NOT NULL,
                options TEXT,
                order_num INTEGER NOT NULL,
                is_required BOOLEAN DEFAULT 1,
                jump_logic TEXT,
                created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
                updated_at DATETIME DEFAULT CURRENT_TIMESTAMP,
                FOREIGN KEY (template_id) REFERENCES questionnaire_templates(id) ON DELETE CASCADE
            )
        """)
    
    def _create_follow_up_records_table(self, conn: sqlite3.Connection):
        """
        创建随访记录表
        """
        conn.execute("""
            CREATE TABLE IF NOT EXISTS follow_up_records (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                custom_id TEXT NOT NULL,
                consultant_id TEXT,
                follow_up_type TEXT NOT NULL,
                title TEXT NOT NULL,
                content TEXT,
                follow_up_date DATETIME NOT NULL,
                next_follow_up_date DATETIME,
                recommendations TEXT,
                notes TEXT,
                created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
                updated_at DATETIME DEFAULT CURRENT_TIMESTAMP,
                FOREIGN KEY (custom_id) REFERENCES users(custom_id) ON DELETE CASCADE,
                FOREIGN KEY (consultant_id) REFERENCES users(custom_id) ON DELETE SET NULL
            )
        """)
    
    def _create_health_diaries_table(self, conn: sqlite3.Connection):
        """
        创建健康日记表
        """
        conn.execute("""
            CREATE TABLE IF NOT EXISTS health_diaries (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                custom_id TEXT NOT NULL,
                diary_type TEXT NOT NULL,
                title TEXT NOT NULL,
                content TEXT,
                mood_level INTEGER,
                energy_level INTEGER,
                pain_level INTEGER,
                diary_date DATETIME NOT NULL,
                is_important BOOLEAN DEFAULT 0,
                created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
                updated_at DATETIME DEFAULT CURRENT_TIMESTAMP,
                FOREIGN KEY (custom_id) REFERENCES users(custom_id) ON DELETE CASCADE
            )
        """)
    
    def _create_documents_table(self, conn: sqlite3.Connection):
        """
        创建文档表
        """
        # Create documents table matching the backend app.db schema (17 columns)
        conn.execute("""
            CREATE TABLE IF NOT EXISTS documents (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                custom_id TEXT NOT NULL,
                filename TEXT NOT NULL,
                original_filename TEXT,
                file_path TEXT,
                file_size INTEGER,
                file_type TEXT,
                document_type TEXT,
                upload_date TEXT NOT NULL,
                ocr_content TEXT,
                ocr_status TEXT DEFAULT 'pending',
                health_record_id INTEGER,
                medical_record_id INTEGER,
                notes TEXT,
                is_processed INTEGER DEFAULT 0,
                created_at TEXT NOT NULL,
                updated_at TEXT NOT NULL,
                FOREIGN KEY (custom_id) REFERENCES users(custom_id) ON DELETE CASCADE,
                FOREIGN KEY (health_record_id) REFERENCES health_records(id) ON DELETE CASCADE,
                FOREIGN KEY (medical_record_id) REFERENCES medical_records(id) ON DELETE CASCADE
            )
        """)
    
    def _create_wearable_device_table(self, conn: sqlite3.Connection):
        """
        创建可穿戴设备表
        """
        conn.execute("""
            CREATE TABLE IF NOT EXISTS wearable_devices (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                custom_id TEXT NOT NULL,
                device_name TEXT NOT NULL,
                device_type TEXT NOT NULL,
                brand TEXT,
                model TEXT,
                mac_address TEXT,
                is_connected BOOLEAN DEFAULT 0,
                last_sync_time DATETIME,
                battery_level INTEGER,
                firmware_version TEXT,
                created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
                updated_at DATETIME DEFAULT CURRENT_TIMESTAMP,
                FOREIGN KEY (custom_id) REFERENCES users(custom_id) ON DELETE CASCADE
            )
        """)
    
    def _create_heart_rate_data_table(self, conn: sqlite3.Connection):
        """
        创建心率数据表
        """
        conn.execute("""
            CREATE TABLE IF NOT EXISTS heart_rate_data (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                device_id INTEGER NOT NULL,
                custom_id TEXT NOT NULL,
                heart_rate INTEGER NOT NULL,
                measurement_time DATETIME NOT NULL,
                activity_type TEXT,
                created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
                FOREIGN KEY (device_id) REFERENCES wearable_devices(id) ON DELETE CASCADE,
                FOREIGN KEY (custom_id) REFERENCES users(custom_id) ON DELETE CASCADE
            )
        """)
    
    def _create_blood_pressure_data_table(self, conn: sqlite3.Connection):
        """
        创建血压数据表
        """
        conn.execute("""
            CREATE TABLE IF NOT EXISTS blood_pressure_data (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                device_id INTEGER NOT NULL,
                custom_id TEXT NOT NULL,
                systolic INTEGER NOT NULL,
                diastolic INTEGER NOT NULL,
                pulse INTEGER,
                measurement_time DATETIME NOT NULL,
                created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
                FOREIGN KEY (device_id) REFERENCES wearable_devices(id) ON DELETE CASCADE,
                FOREIGN KEY (custom_id) REFERENCES users(custom_id) ON DELETE CASCADE
            )
        """)
    
    def _create_sleep_data_table(self, conn: sqlite3.Connection):
        """
        创建睡眠数据表
        """
        conn.execute("""
            CREATE TABLE IF NOT EXISTS sleep_data (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                device_id INTEGER NOT NULL,
                custom_id TEXT NOT NULL,
                sleep_date DATE NOT NULL,
                bedtime DATETIME,
                wake_time DATETIME,
                total_sleep_minutes INTEGER,
                deep_sleep_minutes INTEGER,
                light_sleep_minutes INTEGER,
                rem_sleep_minutes INTEGER,
                awake_minutes INTEGER,
                sleep_quality_score INTEGER,
                created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
                FOREIGN KEY (device_id) REFERENCES wearable_devices(id) ON DELETE CASCADE,
                FOREIGN KEY (custom_id) REFERENCES users(custom_id) ON DELETE CASCADE
            )
        """)
    
    def _create_activity_data_table(self, conn: sqlite3.Connection):
        """
        创建活动数据表
        """
        conn.execute("""
            CREATE TABLE IF NOT EXISTS activity_data (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                device_id INTEGER NOT NULL,
                custom_id TEXT NOT NULL,
                activity_date DATE NOT NULL,
                steps INTEGER DEFAULT 0,
                distance_meters REAL DEFAULT 0,
                calories_burned INTEGER DEFAULT 0,
                active_minutes INTEGER DEFAULT 0,
                floors_climbed INTEGER DEFAULT 0,
                created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
                FOREIGN KEY (device_id) REFERENCES wearable_devices(id) ON DELETE CASCADE,
                FOREIGN KEY (custom_id) REFERENCES users(custom_id) ON DELETE CASCADE
            )
        """)
    
    def _create_sync_record_table(self, conn: sqlite3.Connection):
        """
        创建同步记录表
        """
        conn.execute("""
            CREATE TABLE IF NOT EXISTS sync_record (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                custom_id TEXT NOT NULL,
                table_name TEXT NOT NULL,
                record_id INTEGER NOT NULL,
                operation TEXT NOT NULL,
                sync_status TEXT DEFAULT 'pending',
                sync_time DATETIME,
                error_message TEXT,
                retry_count INTEGER DEFAULT 0,
                max_retries INTEGER DEFAULT 3,
                record_time DATETIME DEFAULT CURRENT_TIMESTAMP,
                created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
                updated_at DATETIME DEFAULT CURRENT_TIMESTAMP,
                UNIQUE(custom_id, table_name, record_id),
                FOREIGN KEY (custom_id) REFERENCES users(custom_id) ON DELETE CASCADE
            )
        """)
    
    def _create_indexes(self, conn: sqlite3.Connection):
        """
        创建数据库索引
        """
        indexes = [
            # 用户表索引
            "CREATE INDEX IF NOT EXISTS idx_users_custom_id ON users(custom_id)",
            "CREATE INDEX IF NOT EXISTS idx_users_username ON users(username)",
            "CREATE INDEX IF NOT EXISTS idx_users_email ON users(email)",
            
            # 健康记录索引
            "CREATE INDEX IF NOT EXISTS idx_health_records_custom_id ON health_records(custom_id)",
            "CREATE INDEX IF NOT EXISTS idx_health_records_type ON health_records(recordtype)",
            "CREATE INDEX IF NOT EXISTS idx_health_records_created_at ON health_records(created_at)",
            
            # 医疗记录索引
            "CREATE INDEX IF NOT EXISTS idx_medical_records_custom_id ON medical_records(custom_id)",
            "CREATE INDEX IF NOT EXISTS idx_medical_records_type ON medical_records(recordtype)",
            "CREATE INDEX IF NOT EXISTS idx_medical_records_visit_date ON medical_records(visit_date)",
            
            # 药物索引
            "CREATE INDEX IF NOT EXISTS idx_medications_custom_id ON medications(custom_id)",
            "CREATE INDEX IF NOT EXISTS idx_medications_name ON medications(name)",
            "CREATE INDEX IF NOT EXISTS idx_medications_status ON medications(status)",
            
            # 评估索引
            "CREATE INDEX IF NOT EXISTS idx_assessments_custom_id ON assessments(custom_id)",
            "CREATE INDEX IF NOT EXISTS idx_assessments_type ON assessments(assessment_type)",
            "CREATE INDEX IF NOT EXISTS idx_assessments_status ON assessments(status)",
            
            # 文档索引
            "CREATE INDEX IF NOT EXISTS idx_documents_custom_id ON documents(custom_id)",
            "CREATE INDEX IF NOT EXISTS idx_documents_type ON documents(document_type)",
            "CREATE INDEX IF NOT EXISTS idx_documents_filename ON documents(filename)",
            
            # 同步记录索引
            "CREATE INDEX IF NOT EXISTS idx_sync_record_custom_id ON sync_record(custom_id)",
            "CREATE INDEX IF NOT EXISTS idx_sync_record_table_name ON sync_record(table_name)",
            "CREATE INDEX IF NOT EXISTS idx_sync_record_status ON sync_record(sync_status)",
        ]
        
        for index_sql in indexes:
            try:
                conn.execute(index_sql)
            except Exception as e:
                print(f"创建索引失败: {index_sql}, 错误: {e}")
    
    def check_table_exists(self, table_name: str) -> bool:
        """
        检查表是否存在
        
        Args:
            table_name: 表名
            
        Returns:
            bool: 表是否存在
        """
        with self.get_connection() as conn:
            cursor = conn.execute(
                "SELECT name FROM sqlite_master WHERE type='table' AND name=?",
                (table_name,)
            )
            return cursor.fetchone() is not None
    
    def get_table_info(self, table_name: str) -> List[Dict[str, Any]]:
        """
        获取表结构信息
        
        Args:
            table_name: 表名
            
        Returns:
            List[Dict[str, Any]]: 表结构信息
        """
        with self.get_connection() as conn:
            cursor = conn.execute(f"PRAGMA table_info({table_name})")
            columns = cursor.fetchall()
            return [dict(column) for column in columns]
    
    def backup_database(self, backup_path: str) -> bool:
        """
        备份数据库
        
        Args:
            backup_path: 备份文件路径
            
        Returns:
            bool: 备份是否成功
        """
        try:
            import shutil
            shutil.copy2(self.db_path, backup_path)
            print(f"数据库备份成功: {backup_path}")
            return True
        except Exception as e:
            print(f"数据库备份失败: {e}")
            return False
    
    def restore_database(self, backup_path: str) -> bool:
        """
        恢复数据库
        
        Args:
            backup_path: 备份文件路径
            
        Returns:
            bool: 恢复是否成功
        """
        try:
            import shutil
            if os.path.exists(backup_path):
                shutil.copy2(backup_path, self.db_path)
                print(f"数据库恢复成功: {self.db_path}")
                return True
            else:
                print(f"备份文件不存在: {backup_path}")
                return False
        except Exception as e:
            print(f"数据库恢复失败: {e}")
            return False


class UnifiedDataAccess:
    """
    统一数据访问层
    提供标准化的数据操作接口
    """
    
    def __init__(self, db_manager: UnifiedDBManager):
        """
        初始化数据访问层
        
        Args:
            db_manager: 数据库管理器实例
        """
        self.db_manager = db_manager
    
    def insert_record(self, table_name: str, data: Dict[str, Any]) -> Optional[int]:
        """
        插入记录
        
        Args:
            table_name: 表名
            data: 数据字典
            
        Returns:
            Optional[int]: 插入记录的ID，失败返回None
        """
        try:
            with self.db_manager.get_connection() as conn:
                # 构建插入SQL
                columns = list(data.keys())
                placeholders = ['?' for _ in columns]
                sql = f"INSERT INTO {table_name} ({', '.join(columns)}) VALUES ({', '.join(placeholders)})"
                
                cursor = conn.execute(sql, list(data.values()))
                conn.commit()
                return cursor.lastrowid
        except Exception as e:
            print(f"插入记录失败: {e}")
            return None
    
    def update_record(self, table_name: str, record_id: int, data: Dict[str, Any]) -> bool:
        """
        更新记录
        
        Args:
            table_name: 表名
            record_id: 记录ID
            data: 更新数据字典
            
        Returns:
            bool: 更新是否成功
        """
        try:
            with self.db_manager.get_connection() as conn:
                # 构建更新SQL
                set_clauses = [f"{col} = ?" for col in data.keys()]
                sql = f"UPDATE {table_name} SET {', '.join(set_clauses)} WHERE id = ?"
                
                values = list(data.values()) + [record_id]
                conn.execute(sql, values)
                conn.commit()
                return True
        except Exception as e:
            print(f"更新记录失败: {e}")
            return False
    
    def delete_record(self, table_name: str, record_id: int) -> bool:
        """
        删除记录
        
        Args:
            table_name: 表名
            record_id: 记录ID
            
        Returns:
            bool: 删除是否成功
        """
        try:
            with self.db_manager.get_connection() as conn:
                conn.execute(f"DELETE FROM {table_name} WHERE id = ?", (record_id,))
                conn.commit()
                return True
        except Exception as e:
            print(f"删除记录失败: {e}")
            return False
    
    def get_record(self, table_name: str, record_id: int) -> Optional[Dict[str, Any]]:
        """
        获取单条记录
        
        Args:
            table_name: 表名
            record_id: 记录ID
            
        Returns:
            Optional[Dict[str, Any]]: 记录数据，不存在返回None
        """
        try:
            with self.db_manager.get_connection() as conn:
                cursor = conn.execute(f"SELECT * FROM {table_name} WHERE id = ?", (record_id,))
                row = cursor.fetchone()
                return dict(row) if row else None
        except Exception as e:
            print(f"获取记录失败: {e}")
            return None
    
    def get_records_by_custom_id(self, table_name: str, custom_id: str, 
                                limit: Optional[int] = None, 
                                offset: Optional[int] = None) -> List[Dict[str, Any]]:
        """
        根据custom_id获取记录列表
        
        Args:
            table_name: 表名
            custom_id: 用户自定义ID
            limit: 限制数量
            offset: 偏移量
            
        Returns:
            List[Dict[str, Any]]: 记录列表
        """
        try:
            with self.db_manager.get_connection() as conn:
                sql = f"SELECT * FROM {table_name} WHERE custom_id = ? ORDER BY created_at DESC"
                params = [custom_id]
                
                if limit is not None:
                    sql += " LIMIT ?"
                    params.append(limit)
                    
                if offset is not None:
                    sql += " OFFSET ?"
                    params.append(offset)
                
                cursor = conn.execute(sql, params)
                rows = cursor.fetchall()
                return [dict(row) for row in rows]
        except Exception as e:
            print(f"获取记录列表失败: {e}")
            return []
    
    def count_records_by_custom_id(self, table_name: str, custom_id: str) -> int:
        """
        统计用户记录数量
        
        Args:
            table_name: 表名
            custom_id: 用户自定义ID
            
        Returns:
            int: 记录数量
        """
        try:
            with self.db_manager.get_connection() as conn:
                cursor = conn.execute(f"SELECT COUNT(*) FROM {table_name} WHERE custom_id = ?", (custom_id,))
                return cursor.fetchone()[0]
        except Exception as e:
            print(f"统计记录数量失败: {e}")
            return 0
    
    def execute_query(self, sql: str, params: tuple = ()) -> List[Dict[str, Any]]:
        """
        执行自定义查询
        
        Args:
            sql: SQL查询语句
            params: 查询参数
            
        Returns:
            List[Dict[str, Any]]: 查询结果
        """
        try:
            with self.db_manager.get_connection() as conn:
                cursor = conn.execute(sql, params)
                rows = cursor.fetchall()
                return [dict(row) for row in rows]
        except Exception as e:
            print(f"执行查询失败: {e}")
            return []


# 使用示例
if __name__ == "__main__":
    # 初始化数据库管理器
    db_path = "data/health_data.db"
    db_manager = UnifiedDBManager(db_path)
    
    # 创建所有表
    custom_id = "P_00000001"
    db_manager.create_all_tables(custom_id)
    
    # 初始化数据访问层
    data_access = UnifiedDataAccess(db_manager)
    
    # 示例：插入用户记录
    user_data = {
        "username": "test_user",
        "email": "<EMAIL>",
        "hashed_password": "hashed_password_here",
        "full_name": "测试用户",
        "custom_id": custom_id,
        "role": "personal"
    }
    
    user_id = data_access.insert_record("users", user_data)
    print(f"插入用户记录，ID: {user_id}")
    
    # 示例：查询用户记录
    user_record = data_access.get_record("users", user_id)
    print(f"用户记录: {user_record}")