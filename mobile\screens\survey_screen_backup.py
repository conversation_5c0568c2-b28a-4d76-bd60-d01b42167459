"""
问卷调查页面
提供评估量表、问卷和历史记录功能
"""

import os
import sys

# 添加项目根目录到路径
current_dir = os.path.dirname(os.path.abspath(__file__))
parent_dir = os.path.dirname(current_dir)
sys.path.insert(0, parent_dir)

from kivymd.uix.boxlayout import MDBoxLayout
from kivymd.uix.card import MDCard
from kivymd.uix.button import MDButton, MDButtonText
from kivymd.uix.scrollview import MDScrollView
from kivymd.uix.list import MDList, MDListItem, MDListItemHeadlineText, MDListItemSupportingText
from kivymd.uix.label import MDLabel
from kivy.metrics import dp
from kivy.clock import Clock

from screens.base_screen import BaseScreen
from utils.cloud_api import get_cloud_api
from utils.auth_manager import AuthManager
from theme import AppTheme, AppMetrics

# 主题配置
theme = AppTheme
metrics = AppMetrics()

# 日志配置
import logging
logger = logging.getLogger(__name__)

class SurveyScreen(BaseScreen):
    """问卷调查页面类"""
    
    def __init__(self, **kwargs):
        """初始化问卷调查页面"""
        # 页面属性
        self.questionnaires = []  # 问卷列表
        self.assessments = []     # 评估量表列表
        self.current_user = None  # 当前用户
        self.selected_item = None # 选中的项目
        self.current_tab = "assessments"  # 当前标签页
        
        # 设置导航栏属性
        self.title = "问卷调查"
        self.show_back_button = True
        self.show_menu_button = False
        
        super().__init__(**kwargs)
        
        # 初始化API客户端
        self.api_client = get_cloud_api()
        self.auth_manager = AuthManager()
        
        # 初始化UI组件引用
        self.assessment_list_widget = None
        self.questionnaire_list_widget = None
        self.history_list_widget = None
        self.content_area = None
        
        # 标签按钮引用
        self.assessment_tab_btn = None
        self.questionnaire_tab_btn = None
        self.history_tab_btn = None
        
        # 标签页内容引用
        self.assessments_content = None
        self.questionnaires_content = None
        self.history_content = None
        
        logger.info("[SurveyScreen] 问卷调查页面初始化完成")

    def on_pre_enter(self):
        """页面进入前的准备工作"""
        super().on_pre_enter()
        try:
            # 强制校验登录状态
            from kivymd.app import MDApp
            app = MDApp.get_running_app()

            # 检查是否已登录
            is_logged_in = False
            user_data = getattr(app, 'user_data', None)
            if user_data is not None and user_data.get('username'):
                # 检查cloud_api的认证状态
                try:
                    cloud_api = get_cloud_api()
                    if cloud_api and cloud_api.is_authenticated():
                        is_logged_in = True
                        # 获取用户信息
                        self.current_user = user_data
                        logger.info(f"[SurveyScreen] 获取到用户信息: {user_data.get('username', 'Unknown')}")
                except Exception as e:
                    logger.error(f"[SurveyScreen] 检查cloud_api认证状态时出错: {e}")

            if not is_logged_in:
                # 未登录或认证无效，强制跳转回登录页并提示
                logger.warning("[SurveyScreen] 用户未登录，跳转到登录页")
                if self.manager:
                    self.manager.current = 'login_screen'
                return
            
            # 加载数据
            Clock.schedule_once(lambda dt: self.load_data(), 0.1)
            
        except Exception as e:
            logger.error(f"[SurveyScreen] 页面准备失败: {e}")

    def do_content_setup(self):
        """设置页面内容"""
        try:
            logger.debug("[SurveyScreen] 开始设置页面内容")
            
            # 获取内容容器
            content_container = self.ids.get('content_container')
            if not content_container:
                logger.error("[SurveyScreen] 未找到content_container")
                return
            
            # 清空现有内容
            content_container.clear_widgets()
            
            # 创建主布局
            main_layout = MDBoxLayout(
                orientation="vertical",
                adaptive_height=True,
                size_hint_y=None,
                padding=[dp(20), dp(10), dp(20), dp(20)],
                spacing=dp(16)
            )
            main_layout.bind(minimum_height=main_layout.setter('height'))
            
            # 添加功能卡片
            self.create_function_cards(main_layout)
            
            # 添加内容区域
            self.create_content_area(main_layout)
            
            # 添加到容器
            content_container.add_widget(main_layout)
            
            logger.debug("[SurveyScreen] 页面内容设置完成")
            
        except Exception as e:
            logger.error(f"[SurveyScreen] 设置页面内容失败: {e}")
            import traceback
            logger.error(traceback.format_exc())
            
    def create_function_cards(self, parent_layout):
        """创建功能卡片区域"""
        try:
            # 功能卡片容器
            cards_layout = MDBoxLayout(
                orientation='horizontal',
                size_hint_y=None,
                height=dp(60),
                spacing=dp(10)
            )
            
            # 评估量表按钮
            self.assessment_tab_btn = MDButton(
                size_hint_x=0.33,
                style="filled" if self.current_tab == "assessments" else "outlined",
                md_bg_color=theme.PRIMARY_COLOR if self.current_tab == "assessments" else (0, 0, 0, 0),
                on_release=lambda x: self.switch_tab("assessments")
            )
            self.assessment_tab_btn.add_widget(MDButtonText(
                text="评估量表",
                text_color=(1, 1, 1, 1) if self.current_tab == "assessments" else theme.PRIMARY_COLOR
            ))
            
            # 问卷按钮
            self.questionnaire_tab_btn = MDButton(
                size_hint_x=0.33,
                style="filled" if self.current_tab == "questionnaires" else "outlined",
                md_bg_color=theme.PRIMARY_COLOR if self.current_tab == "questionnaires" else (0, 0, 0, 0),
                on_release=lambda x: self.switch_tab("questionnaires")
            )
            self.questionnaire_tab_btn.add_widget(MDButtonText(
                text="问卷",
                text_color=(1, 1, 1, 1) if self.current_tab == "questionnaires" else theme.PRIMARY_COLOR
            ))
            
            # 历史记录按钮
            self.history_tab_btn = MDButton(
                size_hint_x=0.33,
                style="filled" if self.current_tab == "history" else "outlined",
                md_bg_color=theme.PRIMARY_COLOR if self.current_tab == "history" else (0, 0, 0, 0),
                on_release=lambda x: self.switch_tab("history")
            )
            self.history_tab_btn.add_widget(MDButtonText(
                text="历史记录",
                text_color=(1, 1, 1, 1) if self.current_tab == "history" else theme.PRIMARY_COLOR
            ))
            
            # 添加按钮到布局
            cards_layout.add_widget(self.assessment_tab_btn)
            cards_layout.add_widget(self.questionnaire_tab_btn)
            cards_layout.add_widget(self.history_tab_btn)
            
            parent_layout.add_widget(cards_layout)
            
        except Exception as e:
            logger.error(f"[SurveyScreen] 创建功能卡片失败: {e}")
            
    def create_content_area(self, parent_layout):
        """创建内容区域"""
        try:
            # 主要内容卡片
            main_card = MDCard(
                orientation='vertical',
                size_hint_y=None,
                height=dp(600),
                radius=[dp(12)],
                elevation=3,
                padding=[dp(16), dp(16), dp(16), dp(16)],
                md_bg_color=theme.CARD_BACKGROUND
            )
            
            # 内容区域
            self.content_area = MDBoxLayout(
                orientation='vertical'
            )
            
            # 创建各标签页内容
            self.assessments_content = self.create_tab_content("assessments")
            self.questionnaires_content = self.create_tab_content("questionnaires")
            self.history_content = self.create_tab_content("history")
            
            # 默认显示评估量表内容
            self.content_area.add_widget(self.assessments_content)
            
            # 添加到主卡片
            main_card.add_widget(self.content_area)
            
            # 添加到父布局
            parent_layout.add_widget(main_card)
            
        except Exception as e:
            logger.error(f"[SurveyScreen] 创建内容区域失败: {e}")
         
    def create_tab_content(self, tab_type):
        """创建标签页内容"""
        # 创建内容容器 - 移除adaptive_height避免空白区域
        content = MDBoxLayout(
            orientation="vertical",
            size_hint_y=1,  # 使用完整高度
            spacing=dp(8)
        )
        
        # 创建滚动视图 - 使用完整高度
        list_scroll = MDScrollView(
            size_hint_y=1,  # 使用完整高度而不是固定高度
            do_scroll_x=False,
            do_scroll_y=True
        )
        
        # 创建列表容器
        list_widget = MDBoxLayout(
            orientation="vertical",
            adaptive_height=True,
            spacing=dp(8),
            padding=[dp(16), dp(8), dp(16), dp(8)]  # 增加左右内边距
        )
        
        # 根据标签类型设置列表ID和属性
        if tab_type == "assessments":
            list_widget.id = "assessment_list"
            self.assessment_list_widget = list_widget
        elif tab_type == "questionnaires":
            list_widget.id = "questionnaire_list"
            self.questionnaire_list_widget = list_widget
        elif tab_type == "history":
            list_widget.id = "history_list"
            self.history_list_widget = list_widget
         
        list_scroll.add_widget(list_widget)
        content.add_widget(list_scroll)
        return content

    def switch_tab(self, tab_name):
        """切换标签页"""
        try:
            if self.current_tab == tab_name:
                return
                
            self.current_tab = tab_name
            
            # 更新按钮样式
            self.update_tab_buttons()
            
            # 更新内容显示
            self.update_tab_display()
            
            logger.debug(f"[SurveyScreen] 切换到标签页: {tab_name}")
            
        except Exception as e:
            logger.error(f"[SurveyScreen] 切换标签页失败: {e}")

    def update_tab_buttons(self):
        """更新标签按钮样式"""
        try:
            # 更新评估量表按钮
            if self.assessment_tab_btn:
                self.assessment_tab_btn.style = "filled" if self.current_tab == "assessments" else "outlined"
                self.assessment_tab_btn.md_bg_color = theme.PRIMARY_COLOR if self.current_tab == "assessments" else (0, 0, 0, 0)
                
            # 更新问卷按钮
            if self.questionnaire_tab_btn:
                self.questionnaire_tab_btn.style = "filled" if self.current_tab == "questionnaires" else "outlined"
                self.questionnaire_tab_btn.md_bg_color = theme.PRIMARY_COLOR if self.current_tab == "questionnaires" else (0, 0, 0, 0)
                
            # 更新历史记录按钮
            if self.history_tab_btn:
                self.history_tab_btn.style = "filled" if self.current_tab == "history" else "outlined"
                self.history_tab_btn.md_bg_color = theme.PRIMARY_COLOR if self.current_tab == "history" else (0, 0, 0, 0)
                
        except Exception as e:
            logger.error(f"[SurveyScreen] 更新按钮样式失败: {e}")

    def update_tab_display(self):
        """更新标签页显示内容"""
        try:
            if not self.content_area:
                return
                
            # 清空当前内容
            self.content_area.clear_widgets()
            
            # 根据当前标签页添加对应内容
            if self.current_tab == "assessments" and self.assessments_content:
                self.content_area.add_widget(self.assessments_content)
            elif self.current_tab == "questionnaires" and self.questionnaires_content:
                self.content_area.add_widget(self.questionnaires_content)
            elif self.current_tab == "history" and self.history_content:
                self.content_area.add_widget(self.history_content)
                # 切换到历史记录标签页时才加载历史记录数据
                if not hasattr(self, '_history_loaded') or not self._history_loaded:
                    Clock.schedule_once(lambda dt: self.load_history(), 0.1)
                    self._history_loaded = True
                
        except Exception as e:
            logger.error(f"[SurveyScreen] 更新标签页显示失败: {e}")

    def create_empty_state_widget(self, message):
        """创建无数据状态的提示组件
        
        Args:
            message (str): 提示信息
            
        Returns:
            MDCard: 包含提示信息的卡片组件
        """
        empty_card = MDCard(
            size_hint_y=None,
            height=dp(120),
            radius=[dp(8)],
            elevation=1,
            padding=[dp(20), dp(20), dp(20), dp(20)],
            md_bg_color=theme.SURFACE_COLOR,
        )
        
        empty_layout = MDBoxLayout(
            orientation="vertical",
            spacing=dp(10),
            adaptive_height=True,
            pos_hint={"center_x": 0.5, "center_y": 0.5}
        )
        
        # 提示图标（使用文字代替图标）
        icon_label = MDLabel(
            text="📋",
            font_style="Display",
            role="small",
            theme_text_color="Secondary",
            halign="center",
            size_hint_y=None,
            height=dp(40)
        )
        
        # 提示文字
        message_label = MDLabel(
            text=message,
            font_style="Body",
            role="medium",
            theme_text_color="Secondary",
            halign="center",
            size_hint_y=None,
            height=dp(30)
        )
        
        empty_layout.add_widget(icon_label)
        empty_layout.add_widget(message_label)
        empty_card.add_widget(empty_layout)
        
        return empty_card

    def load_data(self):
        """加载数据 - 使用异步方式避免阻塞"""
        try:
            logger.debug("[SurveyScreen] 开始加载数据")
            
            # 使用Clock.schedule_once异步加载，避免阻塞UI
            Clock.schedule_once(lambda dt: self.load_assessments(), 0.1)
            Clock.schedule_once(lambda dt: self.load_questionnaires(), 0.2)
            # 历史记录延迟加载，避免阻塞
            Clock.schedule_once(lambda dt: self.load_history_async(), 0.3)
            
            logger.debug("[SurveyScreen] 数据加载任务已调度")
            
        except Exception as e:
            logger.error(f"[SurveyScreen] 加载数据失败: {e}")

    def load_history_async(self):
        """异步加载历史记录，避免阻塞UI"""
        try:
            # 只有在历史记录标签页激活时才加载
            if self.current_tab == "history":
                self.load_history()
            else:
                logger.debug("[SurveyScreen] 历史记录标签页未激活，跳过加载")
        except Exception as e:
            logger.error(f"[SurveyScreen] 异步加载历史记录失败: {e}")

    def load_assessments(self):
        """加载评估量表"""
        try:
            logger.info("开始加载分发评估量表列表")
            
            if not self.assessment_list_widget:
                logger.warning("[SurveyScreen] assessment_list_widget不存在")
                return
                
            # 清空现有内容
            self.assessment_list_widget.clear_widgets()
            
            # 使用cloud_api获取数据，参考survey_screen_old.py的逻辑
            from utils.cloud_api import get_cloud_api
            cloud_api = get_cloud_api()
            
            if not cloud_api.is_authenticated():
                logger.warning("用户未登录，无法加载评估量表")
                empty_widget = self.create_empty_state_widget("请先登录")
                self.assessment_list_widget.add_widget(empty_widget)
                return
            
            # 调用pending-assessments端点获取待完成量表
            result = cloud_api._make_request(
                method="GET",
                endpoint="mobile/pending-assessments",
                headers={
                    'Authorization': f"Bearer {cloud_api.token}" if cloud_api.token else None,
                    'X-User-ID': cloud_api.custom_id if cloud_api.custom_id else None
                }
            )
            
            if result and result.get('status') == 'success':
                data = result.get('data', {})
                assessments_data = data if isinstance(data, list) else data.get('assessments', [])
                
                # 对pending评估量表进行数据结构转换，确保问题去重等处理
                if assessments_data:
                    wrapped_result = {"status": "success", "data": assessments_data}
                    processed_result = cloud_api._process_assessment_data_structure(wrapped_result)
                    assessments_data = processed_result.get('data', [])
                    logger.debug(f"Pending评估量表数据处理完成: {len(assessments_data)}条")
                
                logger.info(f"成功获取分发评估量表列表，共 {len(assessments_data)} 条")
                
                # 创建列表项
                if assessments_data:
                    # 使用集合去重，仅保留不同标题的量表
                    unique_titles = set()
                    unique_assessments = []

                    for item in assessments_data:
                        title = item.get('title') or item.get('name') or '未命名量表'
                        if title not in unique_titles:
                            unique_titles.add(title)
                            unique_assessments.append(item)

                    logger.info(f"[SurveyScreen] 量表去重后数量: {len(unique_assessments)}/{len(assessments_data)}")
                    
                    for assessment in unique_assessments:
                        try:
                            # 使用MDListItem保持与旧版本一致的布局
                            list_item = MDListItem(
                                on_release=lambda x, item=assessment: self.on_assessment_select(item)
                            )
                            
                            # 获取标题
                            title = assessment.get('title') or assessment.get('name') or '未命名量表'
                            list_item.add_widget(MDListItemHeadlineText(text=title))
                            
                            # 获取描述 - 优先显示template.description
                            template = assessment.get('template', {})
                            if isinstance(template, dict):
                                template_description = template.get('description')
                            else:
                                template_description = None

                            description = (
                                template_description
                                or assessment.get('description')
                                or '无描述'
                            )
                            if len(description) > 100:
                                description = description[:100] + '...'
                            
                            list_item.add_widget(MDListItemSupportingText(text=description))
                            self.assessment_list_widget.add_widget(list_item)
                            
                        except Exception as e:
                            logger.error(f"[SurveyScreen] 添加评估量表项时出错: {e}")
                            continue
                else:
                    # 没有数据时显示提示信息
                    empty_widget = self.create_empty_state_widget("暂无待完成的评估量表")
                    self.assessment_list_widget.add_widget(empty_widget)
            else:
                logger.warning(f"获取分发评估量表失败: {result.get('message', '') if result else '无响应'}")
                error_widget = self.create_empty_state_widget("加载评估量表失败，请稍后重试")
                self.assessment_list_widget.add_widget(error_widget)
                
        except Exception as e:
            logger.error(f"[SurveyScreen] 加载评估量表失败: {e}")
            import traceback
            logger.error(traceback.format_exc())
            # 发生错误时显示错误提示
            if hasattr(self, 'assessment_list_widget') and self.assessment_list_widget:
                self.assessment_list_widget.clear_widgets()
                error_widget = self.create_empty_state_widget("加载评估量表失败，请检查网络连接")
                self.assessment_list_widget.add_widget(error_widget)

    def load_questionnaires(self):
        """加载问卷"""
        try:
            logger.info("开始加载分发问卷列表")
            
            if not self.questionnaire_list_widget:
                logger.warning("[SurveyScreen] questionnaire_list_widget不存在")
                return
                
            # 清空现有内容
            self.questionnaire_list_widget.clear_widgets()
            
            # 使用cloud_api获取数据，参考survey_screen_old.py的逻辑
            from utils.cloud_api import get_cloud_api
            cloud_api = get_cloud_api()
            
            if not cloud_api.is_authenticated():
                logger.warning("用户未登录，无法加载问卷")
                empty_widget = self.create_empty_state_widget("请先登录")
                self.questionnaire_list_widget.add_widget(empty_widget)
                return
            
            # 调用pending-questionnaires端点获取待完成问卷
            result = cloud_api._make_request(
                method="GET",
                endpoint="mobile/pending-questionnaires",
                headers={
                    'Authorization': f"Bearer {cloud_api.token}" if cloud_api.token else None,
                    'X-User-ID': cloud_api.custom_id if cloud_api.custom_id else None
                }
            )
            
            if result and result.get('status') == 'success':
                data = result.get('data', {})
                questionnaires_data = data if isinstance(data, list) else data.get('questionnaires', [])
                logger.info(f"成功获取分发问卷列表，共 {len(questionnaires_data)} 条")
                
                # 创建列表项
                if questionnaires_data:
                    # 使用集合去重，仅保留不同标题的问卷
                    unique_titles = set()
                    unique_questionnaires = []

                    for item in questionnaires_data:
                        title = item.get('title') or item.get('name') or '未命名问卷'
                        if title not in unique_titles:
                            unique_titles.add(title)
                            unique_questionnaires.append(item)

                    logger.info(f"[SurveyScreen] 问卷去重后数量: {len(unique_questionnaires)}/{len(questionnaires_data)}")
                    
                    for questionnaire in unique_questionnaires:
                        try:
                            # 使用MDListItem保持与旧版本一致的布局
                            list_item = MDListItem(
                                on_release=lambda x, item=questionnaire: self.on_questionnaire_select(item)
                            )
                            
                            # 获取标题
                            title = questionnaire.get('title') or questionnaire.get('name') or '未命名问卷'
                            list_item.add_widget(MDListItemHeadlineText(text=title))
                            
                            # 获取描述 - 优先显示template.description
                            template = questionnaire.get('template', {})
                            if isinstance(template, dict):
                                template_description = template.get('description')
                            else:
                                template_description = None

                            description = (
                                template_description
                                or questionnaire.get('description')
                                or '无描述'
                            )
                            if len(description) > 100:
                                description = description[:100] + '...'
                            
                            list_item.add_widget(MDListItemSupportingText(text=description))
                            self.questionnaire_list_widget.add_widget(list_item)
                            
                        except Exception as e:
                            logger.error(f"[SurveyScreen] 添加问卷项时出错: {e}")
                            continue
                else:
                    # 没有数据时显示提示信息
                    empty_widget = self.create_empty_state_widget("暂无待完成的问卷")
                    self.questionnaire_list_widget.add_widget(empty_widget)
            else:
                logger.warning(f"获取分发问卷失败: {result.get('message', '') if result else '无响应'}")
                error_widget = self.create_empty_state_widget("加载问卷失败，请稍后重试")
                self.questionnaire_list_widget.add_widget(error_widget)
                
        except Exception as e:
            logger.error(f"[SurveyScreen] 加载问卷失败: {e}")
            import traceback
            logger.error(traceback.format_exc())
            # 发生错误时显示错误提示
            if hasattr(self, 'questionnaire_list_widget') and self.questionnaire_list_widget:
                self.questionnaire_list_widget.clear_widgets()
                error_widget = self.create_empty_state_widget("加载问卷失败，请检查网络连接")
                self.questionnaire_list_widget.add_widget(error_widget)

    def load_history(self):
        """加载历史记录 - 优化版本，减少阻塞"""
        try:
            logger.debug("[SurveyScreen] 开始加载历史记录")
            
            if not self.history_list_widget:
                logger.warning("[SurveyScreen] history_list_widget不存在")
                return
                
            # 清空现有内容
            self.history_list_widget.clear_widgets()
            
            # 显示加载中状态
            loading_widget = self.create_empty_state_widget("正在加载历史记录...")
            self.history_list_widget.add_widget(loading_widget)
            
            # 使用异步方式加载数据
            Clock.schedule_once(lambda dt: self._load_history_data(), 0.1)
            
        except Exception as e:
            logger.error(f"[SurveyScreen] 加载历史记录失败: {e}")
            
    def _load_history_data(self):
        """实际加载历史记录数据的方法"""
        try:
            # 清空加载中状态
            self.history_list_widget.clear_widgets()
            
            # 初始化历史记录数据
            history_records = []
            
            # 从 API 获取已完成的评估量表和问卷
            from utils.cloud_api import get_cloud_api
            cloud_api = get_cloud_api()
            if not cloud_api.is_authenticated():
                logger.warning("[加载历史记录] 用户未登录，无法获取历史记录")
                empty_widget = self.create_empty_state_widget("请先登录")
                self.history_list_widget.add_widget(empty_widget)
                return
            
            all_history = []
            
            # 获取已完成的评估量表 - 使用超时控制
            try:
                history_assessments_result = cloud_api._make_request(
                    method="GET",
                    endpoint="mobile/history-assessments",
                    headers={
                        'Authorization': f"Bearer {cloud_api.token}" if cloud_api.token else None,
                        'X-User-ID': cloud_api.custom_id if cloud_api.custom_id else None
                    },
                    timeout=5  # 设置5秒超时
                )
                
                if history_assessments_result and history_assessments_result.get('status') == 'success':
                    assessments_data = history_assessments_result.get('data', [])
                    for item in assessments_data:
                        item['type'] = 'assessment'
                        item['item_type'] = 'assessment_report'
                    all_history.extend(assessments_data)
                    logger.info(f"[加载历史记录] 获取到 {len(assessments_data)} 条历史评估量表")
                else:
                    logger.warning(f"[加载历史记录] 获取历史评估量表失败: {history_assessments_result.get('message', '') if history_assessments_result else '无响应'}")
            except Exception as e:
                logger.error(f"[加载历史记录] 获取历史评估量表时出错: {e}")
            
            # 获取已完成的问卷 - 使用超时控制
            try:
                history_questionnaires_result = cloud_api._make_request(
                    method="GET",
                    endpoint="mobile/history-questionnaires",
                    headers={
                        'Authorization': f"Bearer {cloud_api.token}" if cloud_api.token else None,
                        'X-User-ID': cloud_api.custom_id if cloud_api.custom_id else None
                    },
                    timeout=5  # 设置5秒超时
                )
                
                if history_questionnaires_result and history_questionnaires_result.get('status') == 'success':
                    questionnaires_data = history_questionnaires_result.get('data', [])
                    for item in questionnaires_data:
                        item['type'] = 'questionnaire'
                        item['item_type'] = 'questionnaire_report'
                    all_history.extend(questionnaires_data)
                    logger.info(f"[加载历史记录] 获取到 {len(questionnaires_data)} 条历史问卷")
                else:
                    logger.warning(f"[加载历史记录] 获取历史问卷失败: {history_questionnaires_result.get('message', '') if history_questionnaires_result else '无响应'}")
            except Exception as e:
                logger.error(f"[加载历史记录] 获取历史问卷时出错: {e}")
            
            # 按时间排序
            all_history.sort(
                key=lambda x: x.get('created_at', '') or x.get('submitted_at', '') or x.get('completed_at', ''),
                reverse=True
            )
            
            history_records = all_history
            
            # 异步创建UI元素
            Clock.schedule_once(lambda dt: self._create_history_items(history_records), 0.1)
            
        except Exception as e:
            logger.error(f"[SurveyScreen] 加载历史记录数据失败: {e}")
            empty_widget = self.create_empty_state_widget("加载失败，请重试")
            self.history_list_widget.add_widget(empty_widget)
            
    def _create_history_items(self, history_records):
        """创建历史记录UI项目"""
        try:
            logger.info(f"[加载历史记录] 获取到 {len(history_records)} 条历史记录")
            
            # 如果没有历史记录，显示提示信息
            if not history_records:
                empty_widget = self.create_empty_state_widget("暂无历史记录")
                self.history_list_widget.add_widget(empty_widget)
                return
            
            # 分批创建列表项，避免UI阻塞
            batch_size = 5
            for i in range(0, len(history_records), batch_size):
                batch = history_records[i:i+batch_size]
                Clock.schedule_once(lambda dt, batch=batch: self._create_history_batch(batch), i * 0.05)
                
        except Exception as e:
            logger.error(f"[SurveyScreen] 创建历史记录UI失败: {e}")
            empty_widget = self.create_empty_state_widget("创建历史记录失败")
            self.history_list_widget.add_widget(empty_widget)
            
    def _create_history_batch(self, batch):
        """分批创建历史记录项目"""
        try:
            for record in batch:
                try:
                    # 使用MDListItem保持与旧版本一致的布局
                    list_item = MDListItem(
                        on_release=lambda x, item=record: self.on_history_select(item)
                    )
                    
                    # 获取标题和日期
                    title = record.get('title') or record.get('name', '未命名记录')
                    submitted_at = record.get('created_at', '') or record.get('submitted_at', '') or record.get('completed_at', '')
                    if submitted_at and len(submitted_at) > 10:
                        submitted_at = submitted_at[:10]  # 只取日期部分
                    
                    # 类型标签
                    item_type = record.get('item_type', '')
                    if item_type == 'assessment_report':
                        type_label = '量表报告'
                    elif item_type == 'questionnaire_report':
                        type_label = '问卷报告'
                    elif record.get('type') == 'assessment':
                        type_label = '评估量表'
                    elif record.get('type') == 'questionnaire':
                        type_label = '问卷'
                    else:
                        type_label = '回复'
                    
                    # 标题文本
                    title_text = f"{title} - {submitted_at}" if submitted_at else title
                    list_item.add_widget(MDListItemHeadlineText(text=title_text))
                    
                    # 类型信息
                    list_item.add_widget(MDListItemSupportingText(text=f"类型: {type_label}"))
                    
                    # 正常顺序添加，实现从上到下排列
                    self.history_list_widget.add_widget(list_item)
                    
                except Exception as e:
                    logger.error(f"[SurveyScreen] 添加历史记录项时出错: {e}")
                    continue
                    
        except Exception as e:
            logger.error(f"[SurveyScreen] 创建历史记录批次失败: {e}")
            
        except Exception as e:
            logger.error(f"[SurveyScreen] 加载历史记录失败: {e}")
            import traceback
            logger.error(traceback.format_exc())
            # 发生错误时显示错误提示
            if hasattr(self, 'history_list_widget') and self.history_list_widget:
                self.history_list_widget.clear_widgets()
                error_widget = self.create_empty_state_widget("加载历史记录失败，请检查网络连接")
                self.history_list_widget.add_widget(error_widget)

    def on_assessment_select(self, assessment):
        """选择评估量表"""
        try:
            logger.info(f"[SurveyScreen] 选择评估量表: {assessment.get('title', '')}")
            self.selected_item = assessment
            self.open_assessment(assessment)
            
        except Exception as e:
            logger.error(f"[SurveyScreen] 选择评估量表失败: {e}")

    def open_assessment(self, assessment_data):
        """打开评估量表，参考survey_screen_old.py的实现"""
        try:
            logger.info(f"打开评估量表: {assessment_data}")

            # 检查是否有问题列表（优先从根级别的questions获取）
            has_questions = False
            questions_source = ""
            
            # 优先检查根级别的questions字段
            if 'questions' in assessment_data and assessment_data.get('questions'):
                has_questions = True
                questions_source = "根级别"
                logger.info(f"找到根级别的questions字段，共 {len(assessment_data['questions'])} 个问题")
            # 如果根级别没有，检查template.questions
            elif 'template' in assessment_data and isinstance(assessment_data['template'], dict):
                template = assessment_data['template']
                if 'questions' in template and template.get('questions'):
                    # 从 template.questions 复制到根级别，保持兼容性
                    assessment_data['questions'] = template['questions']
                    has_questions = True
                    questions_source = "template"
                    logger.info(f"从template中提取了 {len(template['questions'])} 个问题到根级别")
                    
                    # 同时确保有template_id字段
                    if 'template_id' not in assessment_data and 'id' in template:
                        assessment_data['template_id'] = template['id']
                        logger.info(f"添加template_id: {template['id']}")
            
            # 如果仍然没有问题列表，尝试从 API 获取
            if not has_questions:
                assessment_id = assessment_data.get('id')
                if assessment_id:
                    logger.info(f"评估量表 {assessment_data.get('title')} (ID: {assessment_id}) 没有问题列表，尝试从 API 获取")
                    from utils.cloud_api import get_cloud_api
                    cloud_api = get_cloud_api()
                    if cloud_api:
                        # 获取完整问题列表
                        result = cloud_api.get_mobile_assessments(
                            custom_id=getattr(assessment_data, 'custom_id', None)
                        )

                        # 在返回的数据中查找对应ID的量表
                        if result and isinstance(result, dict) and 'data' in result:
                            for item in result['data']:
                                if item.get('id') == assessment_id:
                                    # 找到匹配的量表，更新数据
                                    if 'questions' in item and item['questions']:
                                        assessment_data['questions'] = item['questions']
                                        has_questions = True
                                        questions_source = "API重新获取"
                                        logger.info(f"成功从 API 获取评估量表问题，共 {len(item['questions'])} 个问题")
                                        # 同时更新template信息
                                        if 'template' in item:
                                            assessment_data['template'] = item['template']
                                        if 'template_id' in item:
                                            assessment_data['template_id'] = item['template_id']
                                        break

                        # 如果上面的方法没有找到问题，尝试使用get_assessment_questions方法
                        if not has_questions:
                            logger.info(f"通过列表未找到问题，尝试单独获取评估量表问题")
                            result = cloud_api.get_assessment_questions(assessment_id)
                            if result and 'questions' in result and result['questions']:
                                assessment_data['questions'] = result['questions']
                                has_questions = True
                                questions_source = "get_assessment_questions API"
                                logger.info(f"成功从 get_assessment_questions API 获取问题，共 {len(result['questions'])} 个问题")
                            
                        if not has_questions:
                            logger.warning(f"评估量表 {assessment_data.get('title')} (ID: {assessment_id}) 没有问题列表，将在表单页面尝试获取")
                else:
                    logger.warning(f"评估量表 {assessment_data.get('title')} 没有ID，无法获取问题列表")
            
            if has_questions:
                logger.info(f"评估量表问题来源: {questions_source}")

            # 设置当前评估量表
            from kivymd.app import MDApp
            app = MDApp.get_running_app()
            app.assessment_to_fill = assessment_data

            # 导航到评估量表表单页面
            self.manager.current = "assessment_form_screen"
        except Exception as e:
            logger.error(f"打开评估量表时出错: {e}")
            import traceback
            logger.error(traceback.format_exc())
            self.show_error(f"打开评估量表时出错: {str(e)}")

    def on_questionnaire_select(self, questionnaire):
        """选择问卷 - 异步处理避免阻塞"""
        try:
            logger.info(f"[SurveyScreen] 选择问卷: {questionnaire.get('title', '')}")
            self.selected_item = questionnaire
            
            # 使用异步方式处理问卷打开，避免阻塞UI
            Clock.schedule_once(lambda dt: self.open_questionnaire(questionnaire), 0.1)
            
        except Exception as e:
            logger.error(f"[SurveyScreen] 选择问卷失败: {e}")

    def open_questionnaire(self, questionnaire_data):
        """打开问卷 - 优化版本，减少阻塞"""
        try:
            logger.info(f"显示问卷详情: {questionnaire_data}")

            # 准备问卷数据
            questionnaire_id = questionnaire_data.get('id')

            # 获取问卷标题
            title = ""
            if 'questionnaire_info' in questionnaire_data and 'title' in questionnaire_data['questionnaire_info']:
                title = questionnaire_data['questionnaire_info']['title']
            else:
                title = questionnaire_data.get('title', '未命名问卷')

            # 获取问题列表
            questions = []
            if 'questions' in questionnaire_data:
                questions = questionnaire_data['questions']

            # 如果没有问题列表，先不报错，让表单页面尝试从API获取
            if not questions:
                logger.info(f"问卷 {title} (ID: {questionnaire_id}) 暂无问题列表，将在表单页面获取")

            # 异步设置问卷数据并导航
            Clock.schedule_once(lambda dt: self._navigate_to_questionnaire_form(questionnaire_data), 0.1)
            
        except Exception as e:
            logger.error(f"显示问卷详情时出错: {e}")
            self.show_error("显示问卷详情时出错，请稍后重试")
            
    def _navigate_to_questionnaire_form(self, questionnaire_data):
        """导航到问卷表单页面"""
        try:
            # 设置当前问卷数据
            from kivymd.app import MDApp
            app = MDApp.get_running_app()
            app.questionnaire_to_fill = questionnaire_data

            # 导航到问卷表单页面
            self.manager.current = "questionnaire_form_screen"
            
        except Exception as e:
            logger.error(f"导航到问卷表单页面失败: {e}")
            self.show_error(f"打开问卷失败: {str(e)}")

    def on_history_select(self, record):
        """选择历史记录 - 异步处理避免阻塞"""
        try:
            logger.info(f"[SurveyScreen] 选择历史记录: {record.get('title', '')}")
            self.selected_item = record
            
            # 使用异步方式处理详情显示，避免阻塞UI
            Clock.schedule_once(lambda dt: self.show_response_detail(record), 0.1)
            
        except Exception as e:
            logger.error(f"[SurveyScreen] 选择历史记录失败: {e}")

    def show_response_detail(self, response):
        """显示历史记录详情 - 优化版本，减少阻塞"""
        try:
            logger.info(f"显示回复详情: {response}")

            # 异步调用navigate_to_response_detail方法显示详情
            Clock.schedule_once(lambda dt: self.navigate_to_response_detail(response), 0.1)
            
        except Exception as e:
            logger.error(f"显示历史记录详情时出错: {e}")
            import traceback
            logger.error(traceback.format_exc())
            self.show_error("显示历史记录详情时出错，请稍后重试")

    def navigate_to_response_detail(self, response):
        """导航到报告详情页面 - 优化版本，添加超时控制"""
        try:
            logger.info(f"导航到报告详情: {response}")

            # 确定报告类型
            item_type = response.get('type', '')
            report_type = "assessment" if "assessment" in item_type else "questionnaire"

            # 获取报告ID
            report_id = response.get('id')
            if not report_id:
                self.show_error("无效的报告ID")
                return

            # 确保custom_id存在于报告数据中
            from utils.user_manager import get_user_manager
            user_manager = get_user_manager()
            current_user = user_manager.get_current_user()
            custom_id = response.get('custom_id') or (current_user.custom_id if current_user else None)
            if custom_id and 'custom_id' not in response:
                response['custom_id'] = custom_id

            # 异步获取报告详情，避免阻塞
            Clock.schedule_once(lambda dt: self._fetch_report_detail(response, report_id, report_type, custom_id), 0.1)

        except Exception as e:
            logger.error(f"导航到报告详情时出错: {e}")
            import traceback
            logger.error(traceback.format_exc())
            self.show_error(f"打开报告详情时出错: {str(e)}")
            
    def _fetch_report_detail(self, response, report_id, report_type, custom_id):
        """异步获取报告详情"""
        try:
            # 获取报告详情 - 添加超时控制
            from utils.cloud_api import get_cloud_api
            cloud_api = get_cloud_api()
            
            # 使用超时控制避免长时间阻塞
            import threading
            import time
            
            result = {'data': None, 'error': None}
            
            def fetch_data():
                try:
                    result['data'] = cloud_api.get_report_detail(report_id, report_type)
                except Exception as e:
                    result['error'] = str(e)
            
            # 创建线程获取数据
            thread = threading.Thread(target=fetch_data)
            thread.daemon = True
            thread.start()
            
            # 等待最多3秒
            thread.join(timeout=3)
            
            if thread.is_alive():
                # 超时处理
                logger.warning("获取报告详情超时，使用缓存数据")
                report_data = response
            elif result['error']:
                # 错误处理
                logger.warning(f"获取报告详情失败: {result['error']}，使用缓存数据")
                report_data = response
            else:
                # 成功获取
                report_result = result['data']
                if report_result and report_result.get("status") == "success":
                    report_data = report_result.get("data", {})
                    if custom_id and 'custom_id' not in report_data:
                        report_data['custom_id'] = custom_id
                else:
                    logger.warning(f"获取报告详情失败，使用列表数据: {report_result.get('message', '未知错误') if report_result else '无响应'}")
                    report_data = response

            # 异步跳转到详情页面
            Clock.schedule_once(lambda dt: self._navigate_to_detail_screen(report_data, report_type, report_id, custom_id), 0.1)
            
        except Exception as e:
            logger.error(f"获取报告详情时出错: {e}")
            Clock.schedule_once(lambda dt: self.show_error(f"获取报告详情失败: {str(e)}"), 0.1)
            
    def _navigate_to_detail_screen(self, report_data, report_type, report_id, custom_id):
        """跳转到详情页面"""
        try:
            # 懒加载报告详情页面
            from utils.screen_loader import get_screen_loader
            screen_loader = get_screen_loader()

            if screen_loader.load_screen('report_detail'):
                # 设置报告数据到屏幕管理器
                self.manager.current_report_data = report_data
                self.manager.current_report_type = report_type
                self.manager.current_report_id = report_id
                self.manager.current_user_id = custom_id

                # 跳转到报告详情页面
                self.manager.current = 'report_detail'
                logger.info("成功跳转到报告详情页面")
            else:
                self.show_error("无法加载报告详情页面")
                
        except Exception as e:
            logger.error(f"跳转到详情页面失败: {e}")
            self.show_error(f"跳转失败: {str(e)}")

    def show_error(self, msg):
        """显示错误提示"""
        from kivymd.uix.snackbar.snackbar import MDSnackbar, MDSnackbarText

        MDSnackbar(
            MDSnackbarText(
                text=msg,
                theme_text_color="Custom",
                text_color=(1, 1, 1, 1)
            ),
            y=dp(10),
            pos_hint={"center_x": 0.5},
            size_hint_x=0.9,
            md_bg_color=(0.8, 0.2, 0.2, 1),
            radius=[10, 10, 10, 10]
        ).open()

    def refresh_data(self):
        """刷新数据"""
        try:
            logger.debug("[SurveyScreen] 刷新数据")
            self.load_data()
            
        except Exception as e:
            logger.error(f"[SurveyScreen] 刷新数据失败: {e}")

    def on_back(self):
        """返回按钮处理"""
        try:
            logger.debug("[SurveyScreen] 返回上一页")
            if hasattr(self.manager, 'current'):
                self.manager.current = 'main_screen'
            return True
            
        except Exception as e:
            logger.error(f"[SurveyScreen] 返回操作失败: {e}")
            return False