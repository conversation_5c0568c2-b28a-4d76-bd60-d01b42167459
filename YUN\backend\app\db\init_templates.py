"""初始化评估量表和调查问卷模板数据

此脚本用于初始化临床评估量表和调查问卷数据库，将标准化的量表和问卷加载到数据库中。
使用新的临床量表和问卷模块，替代原有的模板数据。
"""
import copy
import logging
from sqlalchemy.orm import Session
from app.models.assessment import (
    AssessmentTemplate, AssessmentTemplateQuestion
)
from app.models.questionnaire import (
    QuestionnaireTemplate, QuestionnaireTemplateQuestion
)

# 导入新的临床量表和问卷模块
from app.clinical_scales.assessment import ALL_ASSESSMENT_TEMPLATES as STANDARD_ASSESSMENT_TEMPLATES
from app.clinical_scales.questionnaire import ALL_QUESTIONNAIRE_TEMPLATES as STANDARD_QUESTIONNAIRE_TEMPLATES

logger = logging.getLogger(__name__)

def init_assessment_templates(db: Session) -> None:
    """初始化评估量表模板数据"""
    # 清空现有模板数据
    from sqlalchemy import inspect
    inspector = inspect(db.bind)
    if inspector.has_table(AssessmentTemplateQuestion.__tablename__):
        db.query(AssessmentTemplateQuestion).delete()
    if inspector.has_table(AssessmentTemplate.__tablename__):
        db.query(AssessmentTemplate).delete()
    db.commit()
    
    logger.info("开始初始化评估量表模板...")
    
    # 导入模板数据
    for template_key, template_data in STANDARD_ASSESSMENT_TEMPLATES.items():
        # 创建模板数据的深拷贝，避免修改原始数据
        template_copy = copy.deepcopy(template_data)
        questions = template_copy.pop("questions", [])
        
        # 只保留AssessmentTemplate模型中存在的字段
        valid_fields = {
            'template_key', 'assessment_type', 'sub_type', 'title', 'version', 'description', 
            'instructions', 'scoring_method', 'max_score', 'result_ranges', 
            'is_active', 'status', 'created_by'
        }
        # 保证assessment_type有值
        if not template_copy.get('assessment_type'):
            template_copy['assessment_type'] = 'MENTAL_HEALTH'
        filtered_data = {k: v for k, v in template_copy.items() if k in valid_fields}
        
        template = AssessmentTemplate(**filtered_data)
        db.add(template)
        db.flush()  # 获取模板ID
        
        # 添加问题
        for question_data in questions:
            # 只保留AssessmentTemplateQuestion模型中存在的字段
            valid_question_fields = {
                'question_id', 'question_text', 'question_type', 'options', 
                'scoring', 'order', 'is_required'
            }
            filtered_question_data = {k: v for k, v in question_data.items() if k in valid_question_fields}
            
            question = AssessmentTemplateQuestion(
                template_id=template.id,
                **filtered_question_data
            )
            db.add(question)
    
    db.commit()
    logger.info(f"成功初始化 {len(STANDARD_ASSESSMENT_TEMPLATES)} 个评估量表模板")

def init_questionnaire_templates(db: Session) -> None:
    """初始化调查问卷模板数据"""
    # 清空现有模板数据
    from sqlalchemy import inspect
    inspector = inspect(db.bind)
    if inspector.has_table(QuestionnaireTemplateQuestion.__tablename__):
        db.query(QuestionnaireTemplateQuestion).delete()
    if inspector.has_table(QuestionnaireTemplate.__tablename__):
        db.query(QuestionnaireTemplate).delete()
    db.commit()
    
    logger.info("开始初始化调查问卷模板...")
    
    # 导入模板数据
    for template_key, template_data in STANDARD_QUESTIONNAIRE_TEMPLATES.items():
        # 创建模板数据的深拷贝，避免修改原始数据
        template_copy = copy.deepcopy(template_data)
        questions = template_copy.pop("questions", [])
        
        # 只保留QuestionnaireTemplate模型中存在的字段
        valid_fields = {
            'template_key', 'name', 'questionnaire_type', 'version', 'description', 
            'instructions', 'is_active', 'created_by'
        }
        # 保证questionnaire_type有值
        if not template_copy.get('questionnaire_type'):
            template_copy['questionnaire_type'] = 'HEALTH'
        filtered_data = {k: v for k, v in template_copy.items() if k in valid_fields}
        
        template = QuestionnaireTemplate(**filtered_data)
        db.add(template)
        db.flush()  # 获取模板ID
        
        # 添加问题
        for question_data in questions:
            question = QuestionnaireTemplateQuestion(
                template_id=template.id,
                **question_data
            )
            db.add(question)
    
    db.commit()
    logger.info(f"成功初始化 {len(STANDARD_QUESTIONNAIRE_TEMPLATES)} 个调查问卷模板")

def init_templates(db: Session) -> None:
    """初始化所有模板数据"""
    init_assessment_templates(db)
    init_questionnaire_templates(db)

# 如果直接运行此脚本，则执行初始化
if __name__ == "__main__":
    from app.db.session import engine, Base
    from sqlalchemy.orm import Session
    
    # 确保表存在
    Base.metadata.create_all(bind=engine)
    
    # 创建会话
    db = Session(engine)
    
    try:
        # 初始化所有模板
        init_templates(db)
        logger.info("模板数据初始化完成")
    except Exception as e:
        logger.error(f"初始化模板数据时出错: {e}")
        db.rollback()
    finally:
        db.close()