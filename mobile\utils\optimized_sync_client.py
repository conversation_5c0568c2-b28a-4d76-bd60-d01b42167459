#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
优化的移动端同步客户端

该模块提供移动端与后端数据库的高效同步功能。

主要功能:
1. 与后端同步API通信
2. 批量数据同步
3. 增量数据同步
4. 冲突解决
5. 离线队列管理
6. 性能优化

作者: Health App Team
创建时间: 2024-12-19
版本: 1.0.0
"""

import os
import json
import time
import logging
import threading
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Any, Union, Callable
from dataclasses import dataclass, field
from queue import Queue, Empty
from concurrent.futures import ThreadPoolExecutor, as_completed

# 导入移动端数据库管理器
try:
    from .unified_database_manager_optimized import UnifiedDatabaseManager
    from .cloud_api import get_cloud_api
except ImportError:
    # 兼容性导入
    import sys
    sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
    from mobile.utils.unified_database_manager_optimized import UnifiedDatabaseManager
    from mobile.utils.cloud_api import get_cloud_api

# 配置日志
logger = logging.getLogger(__name__)

# 统一的后端 recordtype 合法枚举（以服务器日志为准）
_RECORDTYPE_ENUM_VALUES = {
    'BASIC_INFO', 'LAB_REPORT', 'MEDICAL_REPORT', 'IMAGING_REPORT', 'DOCUMENT',
    'INPATIENT_RECORD', 'SURGERY_RECORD', 'EXAMINATION_REPORT', 'OTHER'
}

# 错误消息常量
ERROR_MSG_NO_USER_DB = '未连接用户数据库'
ERROR_MSG_NETWORK_FAILED = '网络请求失败'

# API端点常量
API_ENDPOINT_SYNC_RECORD = '/api/sync/record'
API_ENDPOINT_SYNC_RECORDS = '/api/sync/records'

# 常见别名到标准枚举的映射（全部转为标准大写）
_RECORDTYPE_ALIAS_MAP = {
    'basicinfo': 'BASIC_INFO',
    'basic_info': 'BASIC_INFO',
    'lab': 'LAB_REPORT',
    'lab_report': 'LAB_REPORT',
    'medical_report': 'MEDICAL_REPORT',
    'imaging_report': 'IMAGING_REPORT',
    'inpatient': 'INPATIENT_RECORD',
    'surgery': 'SURGERY_RECORD',
    'examination_report': 'EXAMINATION_REPORT',
    # 文档类型映射
    'document': 'DOCUMENT',
    'documents': 'DOCUMENT',
    'doc': 'DOCUMENT',
}

# 服务器可接受字段白名单（严格对齐后端模型，排除本地扩展字段）
# 可根据需要逐步扩展；未列入的表将退回使用本地 EXPECTED_FIELDS 但仍会删除已知非法字段
_SERVER_ALLOWED_FIELDS = {
    'users': [
        'custom_id','username','email','phone','hashed_password','full_name','role','is_active',
        'is_superuser','id_number','gender','birth_date','address','profile_photo',
        'emergency_contact','emergency_phone','registration_type','relationship_type',
        'additional_roles','verification_status','is_first_login','role_application_status',
        'role_application_role','password_reset_at','created_at','updated_at'
    ],
    'health_records': [
        'custom_id','user_custom_id','recordtype','title','content','description','is_important',
        'date_recorded','tags','file_path','status','created_at','updated_at'
    ],
    'medical_records': [
        'custom_id','user_custom_id','recordtype','title','description','diagnosis','treatment',
        'doctor_name','hospital_name','department','visit_date','follow_up_date','severity',
        'prescription','is_important','status','file_attachments','notes','created_at','updated_at'
    ],
    'medications': [
        'custom_id','name','dosage','frequency','start_date','end_date','stop_date','instructions',
        'notes','reason','stop_reason','status','medication_type','prescription_required',
        'reminder_enabled','reminder_times','reminder_settings','review_reminder','created_at','updated_at'
    ],
    'documents': [
        'custom_id','health_record_id','medical_record_id','lab_report_id','examination_report_id',
        'title','filename','file_path','file_size','document_type','document_category','mime_type',
        'description','source','status','file_metadata','ocr_processed','ocr_content','ocr_status',
        'ocr_confidence','created_at','updated_at'
    ],
    'lab_reports': [
        'custom_id','user_custom_id','report_type','test_name','test_date','report_date','result_value',
        'reference_range','unit','status','doctor_name','hospital_name','department','lab_name',
        'diagnosis','is_abnormal','notes','file_path','created_at','updated_at'
    ],
    'examination_reports': [
        'custom_id','user_id','exam_type','hospital_name','department','exam_part','exam_date',
        'report_date','device','doctor_name','description','conclusion','recommendation','notes',
        'is_abnormal','created_at','updated_at'
    ],
    'imaging_reports': [
        'custom_id','user_id','report_type','body_part','hospital_name','department','doctor_name',
        'exam_date','report_date','findings','impression','recommendations','is_abnormal','notes',
        'created_at','updated_at'
    ],
    'assessments': [
        'custom_id','user_id','template_id','assessment_type','name','version','round_number',
        'sequence_number','unique_identifier','completed_at','assessor','score','max_score','result',
        'conclusion','notes','status','last_reminded_at','created_at','updated_at'
    ],
    'questionnaires': [
        'custom_id','user_custom_id','questionnaire_type','title','description','questions','answers',
        'completion_date','score','max_score','template_id','notes','version','created_by',
        'interpretation','status','last_reminded_at','round_number','sequence_number',
        'unique_identifier','created_at','updated_at'
    ],
    'basic_health_info': [
        'custom_id','user_custom_id','category','item_key','item_value','unit','notes','status',
        'created_at','updated_at'
    ],
    'basic_health_info_list': [
        'custom_id','user_custom_id','category','item_data','notes','status','created_at','updated_at'
    ],
    'inpatient_records': [
        'custom_id','medical_record_id','hospital_name','department','admission_date',
        'discharge_date','admission_diagnosis','discharge_diagnosis','treatment_summary',
        'doctor_advice','notes','created_at','updated_at'
    ],
    'surgery_records': [
        'custom_id','medical_record_id','hospital_name','department','surgery_name',
        'surgery_date','surgeon','anesthesia_type','surgery_description','complications',
        'notes','created_at','updated_at'
    ],
    'alerts': [
        'custom_id','title','description','severity','status','source','acknowledged_at',
        'resolved_at','created_at','updated_at'
    ],
    'alert_rules': [
        'custom_id','name','description','condition_type','condition_value','severity',
        'is_active','created_at','updated_at'
    ],
    'alert_channels': [
        'custom_id','name','channel_type','configuration','is_active','created_at','updated_at'
    ],
    'assessment_items': [
        'custom_id','assessment_id','question_text','question_type','options','answer','score',
        'max_score','notes','created_at','updated_at'
    ],
    'assessment_responses': [
        'custom_id','assessment_id','item_id','response_value','response_score',
        'created_at','updated_at'
    ],
    'assessment_templates': [
        'custom_id','name','description','version','category','is_active',
        'created_at','updated_at'
    ],
    'assessment_template_questions': [
        'custom_id','template_id','question_text','question_type','options','order_index',
        'is_required','scoring_rule','created_at','updated_at'
    ],
    'lab_report_items': [
        'custom_id','report_id','item_name','item_value','item_unit','reference_range',
        'is_abnormal','abnormal_level','notes','created_at','updated_at'
    ]
}

@dataclass
class SyncConfig:
    """同步配置类"""
    batch_size: int = 100  # 批量同步大小
    max_retries: int = 3  # 最大重试次数
    retry_delay: int = 5  # 重试延迟(秒)
    sync_interval: int = 300  # 同步间隔(秒)
    max_workers: int = 4  # 最大工作线程数
    timeout: int = 30  # 请求超时时间(秒)
    enable_compression: bool = True  # 启用数据压缩
    enable_encryption: bool = False  # 启用数据加密
    conflict_resolution: str = "server_wins"  # 冲突解决策略: server_wins, client_wins, merge
    offline_mode: bool = False  # 离线模式
    max_queue_size: int = 10000  # 最大队列大小
    enable_incremental_sync: bool = True  # 启用增量同步
    sync_mode: str = "bidirectional"  # 同步模式: bidirectional, server_to_client, client_to_server
    auto_delete_local_orphans: bool = True  # 自动删除本地孤立记录(服务器不存在的记录)
    preserve_local_deletes: bool = False  # 保留本地删除操作(防止服务器数据覆盖本地删除)
    sync_tables: List[str] = field(default_factory=lambda: [
        'users',
        'health_records',
        'medications',
        'medical_records',
        'assessments',
        'documents',
        'lab_reports'
    ])

@dataclass
class SyncRecord:
    """同步记录类"""
    id: str
    table_name: str
    operation: str  # insert, update, delete
    data: Dict[str, Any]
    timestamp: datetime
    priority: int = 0  # 优先级，数字越大优先级越高
    retry_count: int = 0
    last_error: Optional[str] = None
    status: str = "pending"  # pending, processing, completed, failed
    custom_id: Optional[str] = None
    
    def to_dict(self) -> Dict[str, Any]:
        """转换为字典"""
        return {
            'id': self.id,
            'table_name': self.table_name,
            'operation': self.operation,
            'data': self.data,
            'timestamp': self.timestamp.isoformat(),
            'priority': self.priority,
            'retry_count': self.retry_count,
            'last_error': self.last_error,
            'status': self.status,
            'custom_id': self.custom_id
        }
    
    @classmethod
    def from_dict(cls, data: Dict[str, Any]) -> 'SyncRecord':
        """从字典创建"""
        # 确保data是字典类型
        if not isinstance(data, dict):
            logger.warning(f"SyncRecord.from_dict接收到非字典类型: {type(data)}")
            # 创建一个空字典作为回退
            data = {}
        
        # 安全解析data字段
        record_data = data.get('data', {})
        if isinstance(record_data, str):
            try:
                parsed_data = json.loads(record_data)
            except json.JSONDecodeError:
                # 如果JSON解析失败，将字符串包装为字典
                parsed_data = {'value': record_data}
        elif isinstance(record_data, dict):
            parsed_data = record_data
        else:
            # 对于其他类型（如int, float等），转换为字典
            parsed_data = {'value': str(record_data)}
        
        # 安全解析timestamp字段
        timestamp_str = data.get('timestamp', datetime.now().isoformat())
        try:
            # 检查是否包含药物剂量单位
            dosage_units = ['mg', 'ml', 'g', 'mcg', 'μg', 'ug', 'kg', 'oz', 'lb', 'cc', 'IU', 'units']
            if any(unit in str(timestamp_str).lower() for unit in dosage_units):
                logger.warning(f"检测到timestamp字段包含药物剂量信息: {timestamp_str}，使用当前时间")
                parsed_timestamp = datetime.now()
            elif str(timestamp_str).isdigit():
                # 纯数字字符串，可能是剂量
                logger.warning(f"检测到timestamp字段为纯数字: {timestamp_str}，使用当前时间")
                parsed_timestamp = datetime.now()
            else:
                parsed_timestamp = datetime.fromisoformat(timestamp_str)
        except (ValueError, TypeError) as e:
            logger.warning(f"解析timestamp失败: {timestamp_str}, 错误: {e}，使用当前时间")
            parsed_timestamp = datetime.now()
            
        return cls(
            id=data.get('id', ''),
            table_name=data.get('table_name', ''),
            operation=data.get('operation', ''),
            data=parsed_data,
            timestamp=parsed_timestamp,
            priority=data.get('priority', 0),
            retry_count=data.get('retry_count', 0),
            last_error=data.get('last_error'),
            status=data.get('status', 'pending'),
            custom_id=data.get('custom_id')
        )

class OptimizedSyncClient:
    """
    优化的移动端同步客户端
    
    提供高效的数据同步功能，支持批量同步、增量同步、冲突解决等。
    """
    
    def __init__(self, config: Optional[SyncConfig] = None, custom_id: Optional[str] = None):
        """初始化同步客户端"""
        self.config = config or SyncConfig()
        self.custom_id = custom_id
        self.logger = logging.getLogger(f"{__name__}.OptimizedSyncClient")
        
        # 初始化组件
        self.db_manager = UnifiedDatabaseManager()
        self.cloud_api = get_cloud_api()
        
        # 同步队列和状态
        self.sync_queue = Queue(maxsize=self.config.max_queue_size)
        self.is_syncing = False
        self.sync_thread = None
        self.sync_lock = threading.RLock()
        
        # 统计信息
        self.sync_stats = {
            'total_synced': 0,
            'total_failed': 0,
            'last_sync_time': None,
            'sync_duration': 0,
            'queue_size': 0,
            'error_count': 0
        }
        
        # 线程池
        self.executor = ThreadPoolExecutor(max_workers=self.config.max_workers)
        
        # 初始化数据库连接
        if self.custom_id:
            self.connect_user(self.custom_id)

    
    def _normalize_recordtype(self, recordtype: str) -> str:
        """
        简化的recordtype规范化方法
        
        Args:
            recordtype: 原始记录类型
            
        Returns:
            str: 规范化后的记录类型
        """
        if not recordtype:
            return 'other'
            
        # 统一转换为小写
        normalized = str(recordtype).lower().strip()
        
        # 直接映射到统一枚举值
        type_mapping = {
            'basic_info': 'basic_info',
            'lab_report': 'lab_report',
            'medical_report': 'medical_report', 
            'prescription': 'prescription',
            'questionnaire': 'questionnaire',
            'assessment': 'assessment',
            'document': 'document',
            'doc': 'document',
            'documents': 'document',
            'other': 'other'
        }
        
        return type_mapping.get(normalized, 'other')


    def _reset_cloud_api_servers(self):
        """重置CloudAPI服务器状态，让其重新尝试本地服务器"""
        try:
            # 重新获取CloudAPI实例并重置服务器状态
            self.cloud_api = get_cloud_api(reset_servers=True)
            self.logger.info("CloudAPI服务器状态已重置，将重新尝试本地服务器")
        except Exception as e:
            self.logger.error(f"重置CloudAPI服务器状态失败: {str(e)}")

    def _filter_record_type_safe(self, record_type: str, table_name: str = None) -> str | None:
        """
        最终防守：确保 recordtype 在允许列表中。优先使用 cloud_api 的白名单，如果没有则进行保守检查。
        返回允许的 recordtype 或 None（表示删除该字段）。
        """
        try:
            # documents表不应该有recordtype字段，直接返回None
            if table_name and 'document' in table_name.lower():
                return None
                
            if not record_type:
                return None
            v = str(record_type).strip()
            upper = v.upper()
            if upper in _RECORDTYPE_ENUM_VALUES:
                return upper
            alias = _RECORDTYPE_ALIAS_MAP.get(v.lower())
            if alias in _RECORDTYPE_ENUM_VALUES:
                return alias
            # 特殊处理：如果值是'document'，映射到'DOCUMENT'
            if v.lower() == 'document':
                return 'DOCUMENT'
            # 任何未知值一律回退为 OTHER，避免服务器枚举错误
            return 'OTHER'
        except Exception:
            return None

    def _filter_allowed_fields(self, table_name: str, data: Dict[str, Any]) -> Dict[str, Any]:
        """
        根据 UnifiedDatabaseManager.EXPECTED_FIELDS 过滤要发送到后端的字段，避免向后端提交不被接受的关键字参数。
        保留一些必要的元字段（custom_id, id, created_at, updated_at, status）。
        """
        try:
            if not isinstance(data, dict):
                return data

            # 字段规范化
            data = self._normalize_field_names(data)
            
            # 获取允许的字段列表
            allowed = self._get_allowed_fields(table_name)
            if not allowed:
                return data

            # 清理无效字段
            data = self._remove_invalid_fields(data, table_name)
            
            # 应用白名单过滤
            return self._apply_whitelist_filter(data, allowed)
        except Exception:
            return data
    
    def _normalize_field_names(self, data: Dict[str, Any]) -> Dict[str, Any]:
        """规范化字段名"""
        try:
            if 'record_type' in data and 'recordtype' not in data:
                data['recordtype'] = data.pop('record_type')
        except Exception:
            pass
        return data
    
    def _get_allowed_fields(self, table_name: str):
        """获取允许的字段列表"""
        allowed = _SERVER_ALLOWED_FIELDS.get(table_name)
        if not allowed:
            try:
                allowed = getattr(self.db_manager, 'EXPECTED_FIELDS', {}).get(table_name)
            except Exception:
                allowed = None
        return allowed
    
    def _remove_invalid_fields(self, data: Dict[str, Any], table_name: str) -> Dict[str, Any]:
        """删除已知无效字段"""
        invalid_fields = ('allergies', 'sync_status', 'blood_type', 'cloud_id', 'last_login', 'password_hash', 'height', 'weight', 'status')
        for field in invalid_fields:
            data.pop(field, None)
        
        # 特殊表规则
        if table_name == 'users' and 'status' in data:
            data.pop('status', None)
        if table_name and 'document' in table_name.lower():
            data.pop('recordtype', None)
            
        return data
    
    def _apply_whitelist_filter(self, data: Dict[str, Any], allowed) -> Dict[str, Any]:
        """应用白名单过滤"""
        allowed_set = set(allowed) | {'id', 'custom_id', 'created_at', 'updated_at', '_operation', '_sync_id'}
        return {k: v for k, v in data.items() if k in allowed_set}
    
    def connect_user(self, custom_id: str) -> bool:
        """连接用户数据库"""
        try:
            self.custom_id = custom_id
            success = self.db_manager.connect(custom_id)
            if success:
                self.logger.info(f"用户数据库连接成功: {custom_id}")
                # 确保本地表和字段存在，自动迁移缺失列
                try:
                    self.db_manager.ensure_tables_exist()
                except Exception as e:
                    self.logger.warning(f"ensure_tables_exist 失败: {e}")
                # 加载未完成的同步任务
                self._load_pending_sync_tasks()
            else:
                self.logger.error(f"用户数据库连接失败: {custom_id}")
            return success
        except Exception as e:
            self.logger.error(f"连接用户数据库异常: {str(e)}")
            return False
    
    def start_sync_service(self) -> bool:
        """启动同步服务"""
        try:
            with self.sync_lock:
                if self.is_syncing:
                    self.logger.warning("同步服务已在运行")
                    return True
                
                self.is_syncing = True
                self.sync_thread = threading.Thread(
                    target=self._sync_worker,
                    daemon=True,
                    name="SyncWorker"
                )
                self.sync_thread.start()
                
                self.logger.info("同步服务启动成功")
                return True
                
        except Exception as e:
            self.logger.error(f"启动同步服务异常: {str(e)}")
            self.is_syncing = False
            return False
    
    def stop_sync_service(self) -> bool:
        """停止同步服务"""
        try:
            with self.sync_lock:
                if not self.is_syncing:
                    self.logger.warning("同步服务未在运行")
                    return True
                
                self.is_syncing = False
                
                # 等待同步线程结束
                if self.sync_thread and self.sync_thread.is_alive():
                    self.sync_thread.join(timeout=10)
                
                # 关闭线程池
                self.executor.shutdown(wait=True)
                
                self.logger.info("同步服务停止成功")
                return True
                
        except Exception as e:
            self.logger.error(f"停止同步服务异常: {str(e)}")
            return False
    
    def _ensure_dict_data(self, data: Any) -> Dict[str, Any]:
        """
        确保数据是字典类型，如果不是则转换
        
        Args:
            data: 输入数据
            
        Returns:
            Dict[str, Any]: 转换后的字典数据
        """
        if isinstance(data, dict):
            return data
            
        original_type = type(data).__name__
        if isinstance(data, (int, str, float)):
            # 如果是基本类型，转换为包含id的字典
            converted_data = {'id': str(data)}
        elif hasattr(data, '__dict__'):
            # 如果是对象，尝试转换为字典
            try:
                converted_data = data.__dict__ if hasattr(data, '__dict__') else {'value': str(data)}
            except Exception:
                converted_data = {'value': str(data)}
        else:
            # 其他类型尝试转换为字符串
            converted_data = {'value': str(data)}
            
        self.logger.warning(f"数据类型不是字典，已转换: {original_type} -> dict, 转换后数据: {converted_data}")
        return converted_data
    
    def _create_sync_record(self, table_name: str, operation: str, data: Dict[str, Any], priority: int) -> 'SyncRecord':
        """
        创建同步记录
        
        Args:
            table_name: 表名
            operation: 操作类型
            data: 数据字典
            priority: 优先级
            
        Returns:
            SyncRecord: 创建的同步记录
        """
        return SyncRecord(
            id=f"{table_name}_{operation}_{int(time.time() * 1000000)}",
            table_name=table_name,
            operation=operation,
            data=data.copy() if isinstance(data, dict) else data,
            timestamp=datetime.now(),
            priority=priority,
            custom_id=self.custom_id
        )
    
    def _add_to_queue(self, sync_record: 'SyncRecord') -> None:
        """
        添加同步记录到队列
        
        Args:
            sync_record: 要添加的同步记录
        """
        if self.sync_queue.full():
            self.logger.warning("同步队列已满，丢弃最旧的任务")
            try:
                self.sync_queue.get_nowait()
            except Empty:
                pass
        
        self.sync_queue.put(sync_record)
    
    def queue_sync_task(self, table_name: str, operation: str, data: Dict[str, Any], 
                       priority: int = 0) -> bool:
        """添加同步任务到队列"""
        try:
            if not self.custom_id:
                self.logger.error(ERROR_MSG_NO_USER_DB)
                return False
            
            # 确保数据格式正确
            data = self._ensure_dict_data(data)
            
            # 创建同步记录
            sync_record = self._create_sync_record(table_name, operation, data, priority)
            
            # 添加到队列
            self._add_to_queue(sync_record)
            
            # 保存到数据库
            self._save_sync_task(sync_record)
            
            self.logger.debug(f"同步任务已加入队列: {sync_record.id}")
            return True
            
        except Exception as e:
            self.logger.error(f"添加同步任务异常: {str(e)}")
            return False
    
    def _prepare_sync_data(self, data: Dict[str, Any], table_name: str) -> Dict[str, Any]:
        """
        准备同步数据，清理和规范化字段
        
        Args:
            data: 原始数据
            table_name: 表名
            
        Returns:
            清理后的数据
        """
        cleaned_data = data.copy() if isinstance(data, dict) else dict(data)
        
        # 清理可能导致后端失败的字段
        self._clean_id_field(cleaned_data)
        
        # 添加时间戳字段
        self._add_timestamp_fields(cleaned_data)
        
        # 确保包含custom_id
        if 'custom_id' not in cleaned_data:
            cleaned_data['custom_id'] = self.custom_id
        
        # 清理弃用或不接受的字段
        self._clean_deprecated_fields(cleaned_data)
        
        # 应用白名单过滤
        cleaned_data = self._apply_whitelist_filter(cleaned_data, table_name)
        
        return cleaned_data
    
    def _clean_id_field(self, data: Dict[str, Any]) -> None:
        """
        清理ID字段，移除临时ID
        
        Args:
            data: 要清理的数据字典
        """
        if 'id' in data:
            id_val = data.get('id')
            # 如果是字符串且不是数字，移除该字段
            if isinstance(id_val, str) and not id_val.isdigit():
                del data['id']
    
    def _add_timestamp_fields(self, data: Dict[str, Any]) -> None:
        """
        添加时间戳字段
        
        Args:
            data: 要添加时间戳的数据字典
        """
        current_time = datetime.now().isoformat()
        if 'created_at' not in data:
            data['created_at'] = current_time
        if 'updated_at' not in data:
            data['updated_at'] = current_time
    
    def _clean_deprecated_fields(self, data: Dict[str, Any]) -> None:
        """
        清理弃用字段
        
        Args:
            data: 要清理的数据字典
        """
        # 移除is_current字段（如果存在）
        if 'is_current' in data:
            del data['is_current']
        
        # 移除patient_id字段（如果存在）
        if 'patient_id' in data:
            del data['patient_id']
    
    def _build_request_data(self, table_name: str, operation: str, 
                           cleaned_data: Dict[str, Any]) -> Dict[str, Any]:
        """
        构建请求数据
        
        Args:
            table_name: 表名
            operation: 操作类型
            cleaned_data: 清理后的数据
            
        Returns:
            构建的请求数据
        """
        # 处理recordtype字段
        if table_name == 'health_records' and 'recordtype' not in cleaned_data:
            cleaned_data['recordtype'] = 'general'
        
        # 记录关键信息
        self.logger.info(f"同步{table_name}表，操作: {operation}，custom_id: {self.custom_id}")
        
        return {
            'table_name': table_name,
            'operation': operation,
            'data': cleaned_data,
            'custom_id': self.custom_id
        }
    
    def _send_sync_request(self, request_data: Dict[str, Any], 
                          table_name: str, original_data: Dict[str, Any]) -> Dict[str, Any]:
        """
        发送同步请求到后端
        
        Args:
            request_data: 请求数据
            table_name: 表名
            original_data: 原始数据
            
        Returns:
            同步结果
        """
        try:
            response = requests.post(
                f"{self.server_url}{API_ENDPOINT_SYNC_RECORD}",
                json=request_data,
                timeout=30
            )
            
            if response.status_code == 200:
                self.sync_stats['total_synced'] += 1
                self.logger.info(f"同步成功: {table_name}")
                return {'success': True, 'data': response.json()}
            else:
                return self._handle_sync_error(response, request_data, table_name, original_data)
                
        except requests.exceptions.ConnectionError:
            return self._handle_connection_error(request_data, table_name, original_data)
        except Exception as e:
            self.sync_stats['total_failed'] += 1
            self.logger.error(f"同步请求异常: {str(e)}")
            return {'success': False, 'error': str(e)}
    
    def _handle_sync_error(self, response, request_data: Dict[str, Any], 
                          table_name: str, original_data: Dict[str, Any]) -> Dict[str, Any]:
        """
        处理同步错误响应
        
        Args:
            response: HTTP响应对象
            request_data: 请求数据
            table_name: 表名
            original_data: 原始数据
            
        Returns:
            处理结果
        """
        if response.status_code == 400:
            try:
                error_data = response.json()
                if 'missing_columns' in error_data:
                    # 后端schema不一致，尝试剥离缺失列后重试
                    missing_cols = error_data['missing_columns']
                    self.logger.warning(f"后端缺失列: {missing_cols}，尝试剥离后重试")
                    
                    # 移除缺失的列
                    filtered_data = original_data.copy()
                    for col in missing_cols:
                        if col in filtered_data:
                            del filtered_data[col]
                    
                    # 重新构建请求并重试
                    cleaned_data = self._prepare_sync_data(filtered_data, table_name)
                    new_request_data = self._build_request_data(table_name, request_data['operation'], cleaned_data)
                    
                    return self._send_sync_request(new_request_data, table_name, filtered_data)
            except Exception:
                pass
        
        self.sync_stats['total_failed'] += 1
        self.logger.error(f"同步失败: {response.status_code} - {response.text}")
        return {'success': False, 'error': f"HTTP {response.status_code}: {response.text}"}
    
    def _handle_connection_error(self, request_data: Dict[str, Any], 
                                table_name: str, original_data: Dict[str, Any]) -> Dict[str, Any]:
        """
        处理连接错误
        
        Args:
            request_data: 请求数据
            table_name: 表名
            original_data: 原始数据
            
        Returns:
            处理结果
        """
        self.logger.warning(f"{ERROR_MSG_NETWORK_FAILED}，尝试重置服务器状态并重试")
        self.server_available = False
        
        # 重试一次
        try:
            response = requests.post(
                f"{self.server_url}{API_ENDPOINT_SYNC_RECORD}",
                json=request_data,
                timeout=30
            )
            
            if response.status_code == 200:
                self.server_available = True
                self.sync_stats['total_synced'] += 1
                self.logger.info(f"重试同步成功: {table_name}")
                return {'success': True, 'data': response.json()}
            else:
                return self._handle_sync_error(response, request_data, table_name, original_data)
                
        except Exception as e:
            self.sync_stats['total_failed'] += 1
            self.logger.error(f"重试同步失败: {str(e)}")
            return {'success': False, 'error': f"{ERROR_MSG_NETWORK_FAILED}: {str(e)}"}
    
    def sync_record_immediate(self, table_name: str, operation: str, 
                            data: Dict[str, Any]) -> Dict[str, Any]:
        """立即同步单条记录"""
        try:
            if not self.custom_id:
                return {'success': False, 'error': ERROR_MSG_NO_USER_DB}
            
            # 清理和准备数据
            cleaned_data = self._prepare_sync_data(data, table_name)
            
            # 构建请求数据
            request_data = self._build_request_data(table_name, operation, cleaned_data)
            
            # 发送同步请求
            return self._send_sync_request(request_data, table_name, data)
                
        except Exception as e:
            self.sync_stats['total_failed'] += 1
            self.logger.error(f"立即同步异常: {str(e)}")
            return {'success': False, 'error': f"{ERROR_MSG_NETWORK_FAILED}: {str(e)}"}
    
    def _clean_batch_records(self, records: List[Dict[str, Any]], table_name: str) -> List[Dict[str, Any]]:
        """
        清理批量记录数据
        
        Args:
            records: 原始记录列表
            table_name: 表名
            
        Returns:
            清理后的记录列表
        """
        logger.debug(f"准备发送批量同步请求，表名: {table_name}, 记录数: {len(records)}")
        logger.debug(f"第一条记录示例: {records[0] if records else '无记录'}")
        
        cleaned_records = []
        
        for rec in records:
            try:
                r = rec.copy() if isinstance(rec, dict) else dict(rec)
            except Exception:
                r = {} if rec is None else {'value': str(rec)}
            
            # 清理ID字段
            self._clean_batch_id_field(r)
            
            # 移除后端不接受的字段
            self._remove_bad_fields(r)
            
            # 处理recordtype字段
            self._process_recordtype_field(r, table_name)
            
            # 特殊处理users表
            if table_name == 'users':
                r.pop('id', None)  # 移除id字段避免唯一约束冲突
            
            # 确保包含custom_id
            if 'custom_id' not in r:
                r['custom_id'] = self.custom_id
            
            cleaned_records.append(r)
        
        # 最终清理和规范化
        self._final_clean_records(cleaned_records, table_name)
        
        return cleaned_records
    
    def _clean_batch_id_field(self, record: Dict[str, Any]) -> None:
        """
        清理批量记录的ID字段
        
        Args:
            record: 要清理的记录
        """
        try:
            if 'id' in record:
                id_val = record.get('id')
                # 如果是字符串且不是数字，移除该字段
                if isinstance(id_val, str) and not id_val.isdigit():
                    record.pop('id', None)
                # 如果是数字字符串，转换为整数
                elif isinstance(id_val, str) and id_val.isdigit():
                    record['id'] = int(id_val)
        except Exception:
            record.pop('id', None)
    
    def _remove_bad_fields(self, record: Dict[str, Any]) -> None:
        """
        移除后端不接受的字段
        
        Args:
            record: 要清理的记录
        """
        bad_fields = ['allergies', 'blood_type', 'height', 'weight', 'status', 
                     'sync_status', 'cloud_id']
        for bad_field in bad_fields:
            record.pop(bad_field, None)
    
    def _process_recordtype_field(self, record: Dict[str, Any], table_name: str) -> None:
        """
        处理recordtype字段
        
        Args:
            record: 要处理的记录
            table_name: 表名
        """
        # 特殊处理：文档表不需要 recordtype 字段
        if self._is_document_table(table_name):
            record.pop('recordtype', None)
        else:
            self._normalize_recordtype_field(record)
            self._validate_recordtype_field(record, table_name)
    
    def _is_document_table(self, table_name: str) -> bool:
        """
        检查是否为文档表
        
        Args:
            table_name: 表名
            
        Returns:
            是否为文档表
        """
        return table_name and 'document' in table_name.lower()
    
    def _normalize_recordtype_field(self, record: Dict[str, Any]) -> None:
        """
        规范化recordtype字段
        
        Args:
            record: 要处理的记录
        """
        if 'record_type' in record and 'recordtype' not in record:
            try:
                record['recordtype'] = record.pop('record_type')
            except Exception:
                record.pop('record_type', None)
    
    def _validate_recordtype_field(self, record: Dict[str, Any], table_name: str) -> None:
        """
        验证recordtype字段的有效性
        
        Args:
            record: 要处理的记录
            table_name: 表名
        """
        if 'recordtype' in record:
            safe_rt = self._filter_record_type_safe(record.get('recordtype'), table_name)
            if safe_rt and safe_rt in _RECORDTYPE_ENUM_VALUES:
                record['recordtype'] = safe_rt
            else:
                record.pop('recordtype', None)
    
    def _final_clean_records(self, records: List[Dict[str, Any]], table_name: str) -> None:
        """
        最终清理记录列表
        
        Args:
            records: 记录列表
            table_name: 表名
        """
        try:
            self._clean_all_recordtype_fields(records, table_name)
            self._log_recordtype_statistics(records, table_name)
        except Exception:
            pass
    
    def _clean_all_recordtype_fields(self, records: List[Dict[str, Any]], table_name: str) -> None:
        """
        清理所有记录的recordtype字段
        
        Args:
            records: 记录列表
            table_name: 表名
        """
        for record in records:
            try:
                if isinstance(record, dict):
                    self._clean_single_record_recordtype(record, table_name)
            except Exception:
                pass
    
    def _clean_single_record_recordtype(self, record: Dict[str, Any], table_name: str) -> None:
        """
        清理单个记录的recordtype字段
        
        Args:
            record: 单个记录
            table_name: 表名
        """
        # 文档表不携带 recordtype
        if self._is_document_table(table_name):
            record.pop('recordtype', None)
            return
        
        # 规范化和验证 recordtype
        self._normalize_recordtype_field(record)
        self._validate_recordtype_field(record, table_name)
    
    def _log_recordtype_statistics(self, records: List[Dict[str, Any]], table_name: str) -> None:
        """
        记录recordtype统计信息
        
        Args:
            records: 记录列表
            table_name: 表名
        """
        types = [r.get('recordtype') for r in records if isinstance(r, dict) and 'recordtype' in r]
        unique_types = {t for t in types if t is not None}
        self.logger.debug(f"OUTGOING /api/sync/batch table={table_name} records={len(records)} recordtypes={unique_types}")
    
    def _build_batch_data(self, table_name: str, cleaned_records: List[Dict[str, Any]]) -> Dict[str, Any]:
        """
        构建批量同步数据
        
        Args:
            table_name: 表名
            cleaned_records: 清理后的记录列表
            
        Returns:
            批量同步数据
        """
        batch_data = {
            'custom_id': self.custom_id,
            'table_name': table_name,
            'records': cleaned_records,
            'batch_size': min(len(cleaned_records), self.config.batch_size),
            'timestamp': datetime.now().isoformat()
        }
        
        # 记录发送的数据用于调试
        logger.debug(f"发送到后端的批量同步数据: {json.dumps(batch_data, ensure_ascii=False)[:500]}...")
        
        return batch_data
    
    def _send_batch_request(self, batch_data: Dict[str, Any], 
                           original_count: int) -> Dict[str, Any]:
        """
        发送批量同步请求
        
        Args:
            batch_data: 批量同步数据
            original_count: 原始记录数量
            
        Returns:
            同步结果
        """
        # 调用后端API
        response = self.cloud_api.post('/api/sync/batch', json_data=batch_data)
        logger.debug(f"后端响应: {response}")
        
        # 处理连接池错误
        if response is None:
            response = self._handle_connection_pool_error(batch_data)
        
        # 从batch_data中获取table_name
        table_name = batch_data.get('table_name', 'unknown')
        
        # 处理响应结果
        return self._process_batch_response(response, table_name, original_count)
    
    def _handle_connection_pool_error(self, batch_data: Dict[str, Any]):
        """
        处理连接池错误
        
        Args:
            batch_data: 批量同步数据
            
        Returns:
            重试后的响应
        """
        last_error = getattr(self.cloud_api, 'last_error', '')
        if "too many 500 error responses" in str(last_error).lower() or "connection pool" in str(last_error).lower():
            self.logger.warning(f"检测到连接池错误: {last_error}，尝试重置连接池")
            try:
                from .connection_pool_manager import get_connection_pool_manager
                pool_manager = get_connection_pool_manager()
                pool_manager.reset_http_connection_pool()
                self.logger.info("连接池已重置，重新尝试批量同步")
                
                # 重新尝试一次
                response = self.cloud_api.post('/api/sync/batch', json_data=batch_data)
                self.logger.debug(f"重试后端响应: {response}")
                return response
            except Exception as reset_error:
                self.logger.error(f"重置连接池失败: {reset_error}")
        return None
    
    def _process_batch_response(self, response, table_name: str, original_count: int) -> Dict[str, Any]:
        """
        处理批量同步响应
        
        Args:
            response: API响应
            table_name: 表名
            original_count: 原始记录数量
            
        Returns:
            处理结果
        """
        if response and response.get('success'):
            processed_count = response.get('data', {}).get('processed_count', 0)
            failed_count = response.get('data', {}).get('failed_count', 0)
            message = response.get('message', f'批量同步完成: {table_name}, 成功{processed_count}条, 失败{failed_count}条')
            
            self.sync_stats['total_synced'] += processed_count
            self.sync_stats['total_failed'] += failed_count
            
            self.logger.info(message)
            return {'success': True, 'data': response.get('data')}
        else:
            return self._handle_batch_failure(response, table_name, original_count)
    
    def _handle_batch_failure(self, response, table_name: str, original_count: int) -> Dict[str, Any]:
        """
        处理批量同步失败
        
        Args:
            response: API响应
            table_name: 表名
            original_count: 原始记录数量
            
        Returns:
            失败处理结果
        """
        error_msg = response.get('message', '未知错误') if response else ERROR_MSG_NETWORK_FAILED
        
        # 如果响应中有部分成功的数据，也要统计
        if response and response.get('data'):
            processed_count = response.get('data', {}).get('processed_count', 0)
            failed_count = response.get('data', {}).get('failed_count', 0)
            self.sync_stats['total_synced'] += processed_count
            self.sync_stats['total_failed'] += failed_count
            
            # 如果有处理成功的记录，记录为信息而不是错误
            if processed_count > 0:
                self.logger.info(f"批量同步部分成功: 成功{processed_count}条, 失败{failed_count}条")
                return {'success': True, 'data': response.get('data'), 'partial': True}
        else:
            self.sync_stats['total_failed'] += original_count
        
        # 记录详细的错误信息
        if response:
            self.logger.error(f"批量同步失败: {error_msg}, 详细响应: {json.dumps(response, ensure_ascii=False)}")
        else:
            self.logger.error(f"批量同步失败: {error_msg}")
        
        return {'success': False, 'error': error_msg}
    
    def _handle_batch_exception(self, exception: Exception, record_count: int) -> Dict[str, Any]:
        """
        处理批量同步异常
        
        Args:
            exception: 异常对象
            record_count: 记录数量
            
        Returns:
            异常处理结果
        """
        self.sync_stats['total_failed'] += record_count
        self.logger.error(f"批量同步异常: {str(exception)}")
        import traceback
        self.logger.error(traceback.format_exc())
        
        # 检查是否是连接池相关异常
        if "too many 500 error responses" in str(exception).lower() or "connection pool" in str(exception).lower():
            self.logger.warning(f"检测到连接池异常: {exception}，尝试重置连接池")
            try:
                from .connection_pool_manager import get_connection_pool_manager
                pool_manager = get_connection_pool_manager()
                pool_manager.reset_http_connection_pool()
                self.logger.info("连接池已重置")
            except Exception as reset_error:
                self.logger.error(f"重置连接池失败: {reset_error}")
        
        return {'success': False, 'error': str(exception)}
    
    def batch_sync_records(self, table_name: str, records: List[Dict[str, Any]]) -> Dict[str, Any]:
        """批量同步记录"""
        try:
            if not self.custom_id:
                return {'success': False, 'error': '未连接用户数据库'}
            
            if not records:
                return {'success': True, 'data': {'processed_count': 0, 'failed_count': 0}}
            
            # 清理记录数据
            cleaned_records = self._clean_batch_records(records, table_name)
            
            # 构建批量同步数据
            batch_data = self._build_batch_data(table_name, cleaned_records)
            
            # 发送批量同步请求
            return self._send_batch_request(batch_data, len(records))
                
        except Exception as e:
            return self._handle_batch_exception(e, len(records))
    
    def _build_download_params(self, table_name: str, since: Optional[str] = None, 
                              limit: int = 1000) -> Dict[str, Any]:
        """
        构建下载请求参数
        
        Args:
            table_name: 表名
            since: 起始时间
            limit: 限制数量
            
        Returns:
            请求参数字典
        """
        params = {
            'custom_id': self.custom_id,
            'table_name': table_name,
            'limit': limit
        }
        
        if since:
            params['since'] = since
            
        return params
    
    def _process_download_response(self, response, table_name: str) -> Dict[str, Any]:
        """
        处理下载响应
        
        Args:
            response: API响应
            table_name: 表名
            
        Returns:
            处理结果
        """
        if response and response.get('success'):
            records = response.get('data', {}).get('records', [])
            self.logger.info(f"下载记录成功: {table_name}, 获取{len(records)}条记录")
            return {'success': True, 'records': records, 'data': response.get('data')}
        
        # 检查是否有数据但success字段为False的情况
        if response and response.get('data') and response.get('data', {}).get('records'):
            records = response.get('data', {}).get('records', [])
            self.logger.info(f"获取记录成功: {table_name}, 获取{len(records)}条记录")
            return {'success': True, 'records': records, 'data': response.get('data')}
        
        # 处理错误情况
        return self._handle_download_error(response, table_name)
    
    def _handle_download_error(self, response, table_name: str) -> Dict[str, Any]:
        """
        处理下载错误
        
        Args:
            response: API响应
            table_name: 表名
            
        Returns:
            错误处理结果
        """
        error_msg = response.get('message', '未知错误') if response else ERROR_MSG_NETWORK_FAILED
        
        # 检查是否需要自动修复recordtype错误
        if self._should_repair_recordtype(error_msg):
            return self._repair_recordtype_and_retry(table_name, error_msg)
        
        # 记录失败日志
        has_records = response and response.get('data') and response.get('data', {}).get('records')
        if not has_records:
            self.logger.error(f"下载记录失败: {table_name}, {error_msg}")
        
        return {'success': False, 'error': error_msg}
    
    def _should_repair_recordtype(self, error_msg: str) -> bool:
        """
        检查是否需要修复recordtype错误
        
        Args:
            error_msg: 错误消息
            
        Returns:
            是否需要修复
        """
        return (isinstance(error_msg, str) and 
                'recordtype' in error_msg.lower() and 
                'document' in error_msg.lower())
    
    def _repair_recordtype_and_retry(self, table_name: str, error_msg: str) -> Dict[str, Any]:
        """
        修复recordtype错误并重试
        
        Args:
            table_name: 表名
            error_msg: 错误消息
            
        Returns:
            修复重试结果
        """
        try:
            self.logger.warning("检测到后端枚举值错误(recordtype='document')，尝试自动修复为 OTHER 后重试下载")
            
            # 发送修复请求
            fix_payload = {
                'custom_id': self.custom_id, 
                'from': 'document', 
                'to': 'OTHER', 
                'table': table_name
            }
            self.cloud_api.post('/api/admin/repair_recordtypes', json_data=fix_payload)
            
            # 重试下载
            return self._retry_download_after_repair(table_name)
            
        except Exception as fix_e:
            self.logger.error(f"下载记录修复过程异常: {table_name}, {str(fix_e)}")
            return {'success': False, 'error': error_msg}
    
    def _retry_download_after_repair(self, table_name: str) -> Dict[str, Any]:
        """
        修复后重试下载
        
        Args:
            table_name: 表名
            
        Returns:
            重试结果
        """
        params = self._build_download_params(table_name)
        response2 = self.cloud_api.get(API_ENDPOINT_SYNC_RECORDS, params=params)
        
        if response2 and (response2.get('success') or 
                         (response2.get('data') and response2.get('data', {}).get('records'))):
            records = response2.get('data', {}).get('records', [])
            self.logger.info(f"下载记录成功(修复后): {table_name}, 获取{len(records)}条记录")
            return {'success': True, 'records': records, 'data': response2.get('data')}
        else:
            # 修复后仍然失败
            retry_error = response2.get('message', '修复重试失败') if response2 else '修复重试网络失败'
            self.logger.error(f"下载记录失败(修复重试后): {table_name}, {retry_error}")
            return {'success': False, 'error': retry_error}
    
    def download_records(self, table_name: str, since: Optional[str] = None, 
                        limit: int = 1000) -> Dict[str, Any]:
        """
        从服务器下载记录
        
        Args:
            table_name: 表名
            since: 起始时间
            limit: 限制数量
            
        Returns:
            下载结果
        """
        try:
            if not self.custom_id:
                return {'success': False, 'error': ERROR_MSG_NO_USER_DB}
            
            # 构建请求参数
            params = self._build_download_params(table_name, since, limit)
            
            # 调用后端API
            response = self.cloud_api.get(API_ENDPOINT_SYNC_RECORDS, params=params)
            
            # 处理响应
            return self._process_download_response(response, table_name)
                
        except Exception as e:
            self.logger.error(f"下载记录异常: {str(e)}")
            import traceback
            self.logger.error(traceback.format_exc())
            return {'success': False, 'error': str(e)}
    
    def get_sync_status(self) -> Dict[str, Any]:
        """获取同步状态"""
        try:
            # 更新队列大小
            self.sync_stats['queue_size'] = self.sync_queue.qsize()
            
            # 获取服务器同步状态
            server_status = None
            if self.custom_id:
                try:
                    response = self.cloud_api.get(f'/api/sync/status?custom_id={self.custom_id}')
                    if response and response.get('success'):
                        server_status = response.get('data')
                except Exception as e:
                    self.logger.warning(f"获取服务器同步状态失败: {str(e)}")
            
            return {
                'client_stats': self.sync_stats.copy(),
                'server_status': server_status,
                'is_syncing': self.is_syncing,
                'custom_id': self.custom_id,
                'config': {
                    'batch_size': self.config.batch_size,
                    'sync_interval': self.config.sync_interval,
                    'offline_mode': self.config.offline_mode
                }
            }
            
        except Exception as e:
            self.logger.error(f"获取同步状态异常: {str(e)}")
            return {'error': str(e)}
    
    def _execute_sync_strategy(self) -> Dict[str, Any]:
        """
        根据配置的同步模式执行相应的同步策略
        
        Returns:
            Dict[str, Any]: 同步结果
        """
        self.logger.info(f"开始执行完整同步 - 模式: {self.config.sync_mode}")
        
        if self.config.sync_mode == "server_to_client":
            return self._perform_server_to_client_sync({})
        elif self.config.sync_mode == "client_to_server":
            return self._perform_client_to_server_sync({})
        else:
            return self._perform_bidirectional_sync({})
    
    def _finalize_sync_results(self, start_time: float, sync_results: Dict[str, Any]) -> Dict[str, Any]:
        """
        完成同步并整理最终结果
        
        Args:
            start_time: 同步开始时间
            sync_results: 同步执行结果
            
        Returns:
            Dict[str, Any]: 最终同步结果
        """
        # 提取统计数据
        total_uploaded = sync_results.get('total_uploaded', 0)
        total_downloaded = sync_results.get('total_downloaded', 0)
        errors = sync_results.get('errors', [])
        
        # 更新统计信息
        sync_duration = time.time() - start_time
        self.sync_stats['last_sync_time'] = datetime.now().isoformat()
        self.sync_stats['sync_duration'] = sync_duration
        
        success = len(errors) == 0
        self.logger.info(
            f"完整同步完成: 上传{total_uploaded}条, 下载{total_downloaded}条, "
            f"耗时{sync_duration:.2f}秒, 错误{len(errors)}个"
        )
        
        return {
            'success': success,
            'total_uploaded': total_uploaded,
            'total_downloaded': total_downloaded,
            'sync_duration': sync_duration,
            'results': sync_results,
            'errors': errors[:10]  # 只返回前10个错误
        }
    
    def perform_full_sync(self) -> Dict[str, Any]:
        """执行完整同步"""
        try:
            if not self.custom_id:
                return {'success': False, 'error': ERROR_MSG_NO_USER_DB}
            
            start_time = time.time()
            
            # 执行同步策略
            sync_results = self._execute_sync_strategy()
            
            # 更新统计信息并返回结果
            return self._finalize_sync_results(start_time, sync_results)
            
        except Exception as e:
            self.logger.error(f"完整同步异常: {str(e)}")
            return {'success': False, 'error': str(e)}
    
    def _perform_server_to_client_sync(self, results: Dict[str, Any]) -> Dict[str, Any]:
        """
        执行服务器到客户端的单向同步
        
        Args:
            results: 同步结果字典
            
        Returns:
            Dict[str, Any]: 更新后的同步结果
        """
        total_downloaded = 0
        deleted_orphans = 0
        errors = []
        
        for table_name in self.config.sync_tables:
            try:
                # 下载服务器记录
                download_result = self.download_records(table_name)
                if download_result.get('status') == 'success' or download_result.get('success'):
                    server_records = download_result.get('records', [])
                    if server_records:
                        # 更新本地数据库
                        updated_count = self._update_local_records(table_name, server_records)
                        total_downloaded += updated_count
                        results[f"{table_name}_download"] = updated_count
                        
                        # 如果启用了自动删除孤立记录，删除服务器不存在的本地记录
                        if self.config.auto_delete_local_orphans:
                            orphan_count = self._delete_local_orphans(table_name, server_records)
                            deleted_orphans += orphan_count
                            results[f"{table_name}_deleted_orphans"] = orphan_count
                else:
                    errors.append(f"{table_name}下载失败: {download_result.get('error')}")
                    
            except Exception as e:
                errors.append(f"{table_name}同步异常: {str(e)}")
        
        results['total_downloaded'] = total_downloaded
        results['deleted_local_orphans'] = deleted_orphans
        results['errors'] = errors
        return results
    
    def _perform_client_to_server_sync(self, results: Dict[str, Any]) -> Dict[str, Any]:
        """
        执行客户端到服务器的单向同步
        
        Args:
            results: 同步结果字典
            
        Returns:
            Dict[str, Any]: 更新后的同步结果
        """
        total_uploaded = 0
        errors = []
        
        for table_name in self.config.sync_tables:
            uploaded_count = self._upload_table_records(table_name, errors)
            if uploaded_count > 0:
                total_uploaded += uploaded_count
                results[f"{table_name}_upload"] = uploaded_count
        
        results['total_uploaded'] = total_uploaded
        results['errors'] = errors
        return results
    
    def _upload_table_records(self, table_name: str, errors: List[str]) -> int:
        """
        上传单个表的记录
        
        Args:
            table_name: 表名
            errors: 错误列表
            
        Returns:
            int: 上传的记录数量
        """
        try:
            # 获取本地记录
            local_records = self._get_local_records(table_name)
            if not local_records:
                return 0
                
            # 批量上传
            upload_result = self.batch_sync_records(table_name, local_records)
            if upload_result.get('status') == 'success' or upload_result.get('success'):
                return upload_result.get('data', {}).get('processed_count', 0)
            else:
                errors.append(f"{table_name}上传失败: {upload_result.get('error')}")
                return 0
                
        except Exception as e:
            errors.append(f"{table_name}同步异常: {str(e)}")
            return 0
    
    def _perform_bidirectional_sync(self, results: Dict[str, Any]) -> Dict[str, Any]:
        """
        执行双向同步(默认模式)
        
        Args:
            results: 同步结果字典
            
        Returns:
            Dict[str, Any]: 更新后的同步结果
        """
        total_uploaded = 0
        total_downloaded = 0
        deleted_orphans = 0
        errors = []
        
        for table_name in self.config.sync_tables:
            upload_count, download_count, orphan_count = self._sync_single_table_bidirectional(
                table_name, errors
            )
            
            total_uploaded += upload_count
            total_downloaded += download_count
            deleted_orphans += orphan_count
            
            if upload_count > 0:
                results[f"{table_name}_upload"] = upload_count
            if download_count > 0:
                results[f"{table_name}_download"] = download_count
            if orphan_count > 0:
                results[f"{table_name}_deleted_orphans"] = orphan_count
        
        results['total_uploaded'] = total_uploaded
        results['total_downloaded'] = total_downloaded
        results['deleted_local_orphans'] = deleted_orphans
        results['errors'] = errors
        return results
    
    def _sync_single_table_bidirectional(self, table_name: str, errors: List[str]) -> tuple[int, int, int]:
        """
        对单个表执行双向同步
        
        Args:
            table_name: 表名
            errors: 错误列表
            
        Returns:
            tuple[int, int, int]: (上传数量, 下载数量, 删除孤立记录数量)
        """
        try:
            # 上传本地数据
            upload_count = self._upload_table_records(table_name, errors)
            
            # 下载服务器数据
            download_count, orphan_count = self._download_and_update_table(table_name, errors)
            
            return upload_count, download_count, orphan_count
            
        except Exception as e:
            errors.append(f"{table_name}同步异常: {str(e)}")
            return 0, 0, 0
    
    def _download_and_update_table(self, table_name: str, errors: List[str]) -> tuple[int, int]:
        """
        下载并更新单个表的数据
        
        Args:
            table_name: 表名
            errors: 错误列表
            
        Returns:
            tuple[int, int]: (下载数量, 删除孤立记录数量)
        """
        download_result = self.download_records(table_name)
        if not (download_result.get('status') == 'success' or download_result.get('success')):
            errors.append(f"{table_name}下载失败: {download_result.get('error')}")
            return 0, 0
            
        server_records = download_result.get('records', [])
        if not server_records:
            return 0, 0
            
        # 更新本地记录
        updated_count = self._update_local_records(table_name, server_records)
        
        # 处理孤立记录删除
        orphan_count = 0
        if self.config.auto_delete_local_orphans:
            orphan_count = self._delete_local_orphans(table_name, server_records)
            
        return updated_count, orphan_count
    
    def _delete_local_orphans(self, table_name: str, server_records: List[Dict[str, Any]]) -> int:
        """
        删除本地孤立记录(服务器不存在的记录)
        
        Args:
            table_name: 表名
            server_records: 服务器记录列表
            
        Returns:
            int: 删除的记录数量
        """
        try:
            # 获取服务器记录的ID集合
            server_ids = set()
            for record in server_records:
                if 'id' in record:
                    server_ids.add(record['id'])
                elif 'custom_id' in record:
                    server_ids.add(record['custom_id'])
            
            if not server_ids:
                return 0
            
            # 获取本地记录
            local_records = self._get_local_records(table_name)
            orphan_ids = []
            
            for record in local_records:
                record_id = record.get('id') or record.get('custom_id')
                if record_id and record_id not in server_ids:
                    orphan_ids.append(record_id)
            
            # 删除孤立记录
            deleted_count = 0
            for orphan_id in orphan_ids:
                try:
                    # 使用数据库管理器删除记录
                    if hasattr(self.db_manager, 'delete_record'):
                        success = self.db_manager.delete_record(table_name, orphan_id)
                        if success:
                            deleted_count += 1
                            self.logger.debug(f"删除孤立记录: {table_name}.{orphan_id}")
                except Exception as e:
                    self.logger.error(f"删除孤立记录失败 {table_name}.{orphan_id}: {str(e)}")
            
            if deleted_count > 0:
                self.logger.info(f"删除了 {deleted_count} 个孤立记录 from {table_name}")
            
            return deleted_count
            
        except Exception as e:
            self.logger.error(f"删除孤立记录异常 {table_name}: {str(e)}")
            return 0
    
    def cleanup_old_sync_records(self, days: int = 7) -> int:
        """清理旧的同步记录"""
        try:
            cutoff_time = datetime.now() - timedelta(days=days)
            
            # 清理数据库中的同步记录
            query = """
                DELETE FROM sync_record 
                WHERE created_at < ? AND status IN ('completed', 'failed')
            """
            
            result = self.db_manager.execute_query(
                query, (cutoff_time.isoformat(),)
            )
            
            if result.get('status') == 'success' or result.get('success'):
                deleted_count = result.get('rows_affected', 0)
                self.logger.info(f"清理旧同步记录: 删除{deleted_count}条记录")
                return deleted_count
            else:
                self.logger.error(f"清理同步记录失败: {result.get('error')}")
                return 0
                
        except Exception as e:
            self.logger.error(f"清理同步记录异常: {str(e)}")
            return 0
    
    def reset_failed_syncs(self) -> int:
        """重置失败的同步任务"""
        try:
            # 重置数据库中失败的同步记录
            query = """
                UPDATE sync_record 
                SET status = 'pending', retry_count = 0, last_error = NULL
                WHERE status = 'failed' AND retry_count < ?
            """
            
            result = self.db_manager.execute_query(
                query, (self.config.max_retries,)
            )
            
            if result.get('status') == 'success' or result.get('success'):
                reset_count = result.get('rows_affected', 0)
                self.logger.info(f"重置失败同步任务: {reset_count}条")
                
                # 重新加载待处理任务
                self._load_pending_sync_tasks()
                
                return reset_count
            else:
                self.logger.error(f"重置失败同步任务失败: {result.get('error')}")
                return 0
                
        except Exception as e:
            self.logger.error(f"重置失败同步任务异常: {str(e)}")
            return 0
    
    def _sync_worker(self):
        """同步工作线程"""
        self.logger.info("同步工作线程启动")
        
        while self.is_syncing:
            try:
                # 处理队列中的同步任务
                self._process_sync_queue()
                
                # 定期执行完整同步
                if self._should_perform_full_sync():
                    self.perform_full_sync()
                
                # 休眠一段时间
                time.sleep(min(self.config.sync_interval, 60))
                
            except Exception as e:
                self.logger.error(f"同步工作线程异常: {str(e)}")
                time.sleep(10)  # 异常后短暂休眠
        
        self.logger.info("同步工作线程停止")
    
    def _process_sync_queue(self):
        """处理同步队列"""
        processed_count = 0
        batch_records = {}
        
        # 批量处理队列中的任务
        while not self.sync_queue.empty() and processed_count < self.config.batch_size:
            try:
                sync_record = self.sync_queue.get_nowait()
                
                # 按表名分组
                table_name = sync_record.table_name
                if table_name not in batch_records:
                    batch_records[table_name] = []
                
                batch_records[table_name].append(sync_record)
                processed_count += 1
                
            except Empty:
                break
            except Exception as e:
                self.logger.error(f"处理同步队列异常: {str(e)}")
        
        # 批量同步每个表的记录
        for table_name, records in batch_records.items():
            try:
                self._batch_sync_table_records(table_name, records)
            except Exception as e:
                self.logger.error(f"批量同步表记录异常 {table_name}: {str(e)}")
    
    def _batch_sync_table_records(self, table_name: str, sync_records: List[SyncRecord]):
        """批量同步表记录"""
        if not sync_records:
            return
        
        # 准备批量数据
        batch_data = self._prepare_batch_data(sync_records)
        
        # 执行批量同步
        result = self.batch_sync_records(table_name, batch_data)

        # 更新同步记录状态
        self._update_batch_sync_status(sync_records, result, batch_data, table_name)
    
    def _prepare_batch_data(self, sync_records: List[SyncRecord]) -> List[Dict[str, Any]]:
        """准备批量同步数据"""
        batch_data = []
        for sync_record in sync_records:
            record_data = self._normalize_sync_record_data(sync_record)
            record_data['_operation'] = sync_record.operation
            record_data['_sync_id'] = sync_record.id
            batch_data.append(record_data)
        return batch_data
    
    def _normalize_sync_record_data(self, sync_record: SyncRecord) -> Dict[str, Any]:
        """标准化同步记录数据"""
        # 确保data是字典类型
        if not isinstance(sync_record.data, dict):
            self.logger.warning(f"同步记录数据不是字典类型: {type(sync_record.data)}")
            if isinstance(sync_record.data, (int, str, float)):
                record_data = {'value': sync_record.data}
            else:
                try:
                    record_data = {'value': str(sync_record.data)}
                except Exception:
                    record_data = {}
        else:
            record_data = sync_record.data.copy()
            
            # 处理ID字段
            self._process_record_id(record_data, sync_record.operation)
            
            # 填充缺失的时间戳
            self._ensure_created_at(record_data)
            
            # 清理已弃用字段
            self._clean_deprecated_fields(record_data)
        
        return record_data
    
    def _process_record_id(self, record_data: Dict[str, Any], operation: str):
        """处理记录ID字段"""
        try:
            if isinstance(record_data, dict) and 'id' in record_data:
                rid = record_data.get('id')
                # 对于更新操作，如果ID是有效的数字，则保留它
                if operation == 'update':
                    if isinstance(rid, str) and rid.isdigit():
                        record_data['id'] = int(rid)  # 转换为整数
                    elif isinstance(rid, int) and rid > 0:
                        pass  # 保留有效的整数ID
                    else:
                        # 对于更新操作，如果没有有效ID，尝试通过custom_id查找
                        if 'custom_id' not in record_data:
                            record_data.pop('id', None)
                else:
                    # 对于插入操作，清理非数值ID避免主键冲突
                    if isinstance(rid, str):
                        if not rid.isdigit():
                            record_data.pop('id', None)
                    elif not isinstance(rid, int):
                        record_data.pop('id', None)
        except Exception:
            pass
    
    def _ensure_created_at(self, record_data: Dict[str, Any]):
        """确保记录包含created_at字段"""
        try:
            if isinstance(record_data, dict) and 'created_at' not in record_data:
                record_data['created_at'] = datetime.now().isoformat()
        except Exception:
            pass
    
    def _clean_deprecated_fields(self, record_data: Dict[str, Any]):
        """清理已弃用或后端不接受的字段"""
        try:
            if isinstance(record_data, dict):
                # 将 patient_id 映射为 custom_id（若 custom_id 不存在）
                if 'patient_id' in record_data and 'custom_id' not in record_data:
                    record_data['custom_id'] = record_data.pop('patient_id')
                # 删除遗留字段
                record_data.pop('patient_id', None)
                # 合并 is_current 到 status（保守处理），然后删除 is_current
                if 'is_current' in record_data:
                    try:
                        if 'status' not in record_data:
                            record_data['status'] = 'active' if record_data.get('is_current') else 'stopped'
                    except Exception:
                        pass
                    record_data.pop('is_current', None)
                # 移除其他常见会导致后端失败的字段（可扩展）
                for bad in ('patient_id',):
                    record_data.pop(bad, None)
        except Exception:
            pass
    
    def _update_batch_sync_status(self, sync_records: List[SyncRecord], result: Dict[str, Any], 
                                 batch_data: List[Dict[str, Any]], table_name: str):
        """更新批量同步状态"""
        for sync_record in sync_records:
            if result.get('status') == 'success' or result.get('success'):
                sync_record.status = 'completed'
                self._update_sync_task_status(sync_record)
            else:
                sync_record.status = 'failed'
                sync_record.retry_count += 1
                sync_record.last_error = result.get('error', '未知错误')

                # 尝试处理后端缺失列的错误
                if self._handle_missing_column_error(result, batch_data, table_name, sync_records):
                    continue

                # 如果重试次数未超限，重新加入队列
                if sync_record.retry_count < self.config.max_retries:
                    sync_record.status = 'pending'
                    self.sync_queue.put(sync_record)

                self._update_sync_task_status(sync_record)
    
    def _handle_missing_column_error(self, result: Dict[str, Any], batch_data: List[Dict[str, Any]], 
                                   table_name: str, sync_records: List[SyncRecord]) -> bool:
        """处理后端缺失列错误"""
        if isinstance(result.get('error'), str) and 'no such column' in result.get('error'):
            self.logger.warning(f"批量同步失败: 后端缺失列，尝试剥离字段并重试一次: {result.get('error')}")
            cleaned_batch = []
            for r in batch_data:
                rc = r.copy()
                rc.pop('stop_reason', None)
                cleaned_batch.append(rc)
            result2 = self.batch_sync_records(table_name, cleaned_batch)
            if result2.get('success'):
                for sync_record in sync_records:
                    sync_record.status = 'completed'
                    self._update_sync_task_status(sync_record)
                return True
        return False
    
    def _should_perform_full_sync(self) -> bool:
        """判断是否应该执行完整同步"""
        if not self.sync_stats.get('last_sync_time'):
            return True
        
        try:
            last_sync = datetime.fromisoformat(self.sync_stats['last_sync_time'])
            time_diff = datetime.now() - last_sync
            return time_diff.total_seconds() > self.config.sync_interval
        except Exception:
            return True
    
    def _get_local_records(self, table_name: str) -> List[Dict[str, Any]]:
        """获取本地记录"""
        try:
            # 根据表名使用不同的查询条件
            if table_name in ['health_records', 'medications', 'medical_records', 'lab_reports']:
                # 这些表有status列
                query = f"""
                    SELECT * FROM {table_name} 
                    WHERE custom_id = ? AND (status IS NULL OR status != 'completed')
                    ORDER BY updated_at DESC
                    LIMIT ?
                """
            else:
                # 其他表使用基本查询
                query = f"""
                    SELECT * FROM {table_name} 
                    WHERE custom_id = ?
                    ORDER BY updated_at DESC
                    LIMIT ?
                """
            
            result = self.db_manager.execute_query(
                query, (self.custom_id, self.config.batch_size), "main", "all"
            )
            
            # 直接返回结果，因为execute_query已经返回了列表格式
            if isinstance(result, list):
                return result
            else:
                self.logger.error(f"获取本地记录失败 {table_name}: 返回格式不正确")
                return []
                
        except Exception as e:
            self.logger.error(f"获取本地记录异常 {table_name}: {str(e)}")
            return []
    
    def _update_local_records(self, table_name: str, server_records: List[Dict[str, Any]]) -> int:
        """更新本地记录"""
        updated_count = 0
        datetime_fields_map = self._get_datetime_fields_map()
        
        try:
            for record in server_records:
                try:
                    record_id = record.get('id')
                    if not record_id:
                        continue
                    
                    # 检查本地记录是否存在
                    check_result = self._check_local_record_exists(table_name, record_id)
                    
                    if check_result:
                        # 更新现有记录
                        if self._update_existing_record(table_name, record, check_result, datetime_fields_map):
                            updated_count += 1
                    else:
                        # 插入新记录
                        if self._insert_new_record(table_name, record, datetime_fields_map):
                            updated_count += 1
                            
                except Exception as e:
                    import traceback
                    self.logger.error(f"更新本地记录异常 {table_name}#{record.get('id')}: {str(e)}")
                    self.logger.error(f"异常详情: {traceback.format_exc()}")
            
            self.logger.info(f"更新本地记录完成 {table_name}: {updated_count}条")
            return updated_count
            
        except Exception as e:
            self.logger.error(f"更新本地记录异常 {table_name}: {str(e)}")
            return updated_count
    
    def _get_datetime_fields_map(self) -> Dict[str, List[str]]:
        """获取各表的日期时间字段映射"""
        return {
            'users': ['created_at', 'updated_at', 'last_login', 'password_reset_at', 'birth_date'],
            'health_records': ['date_recorded', 'created_at', 'updated_at'],
            'medical_records': ['visit_date', 'follow_up_date', 'created_at', 'updated_at'],
            'lab_reports': ['test_date', 'report_date', 'created_at', 'updated_at'],
            'examination_reports': ['exam_date', 'report_date', 'created_at', 'updated_at'],
            'imaging_reports': ['exam_date', 'report_date', 'created_at', 'updated_at'],
            'hospitalization_records': ['admission_date', 'discharge_date', 'created_at', 'updated_at'],
            'medications': ['start_date', 'end_date', 'created_at', 'updated_at'],
            'medication_schedules': ['scheduled_time', 'taken_at', 'created_at', 'updated_at'],
            'assessments': ['assessment_date', 'completed_at', 'created_at', 'updated_at'],
            'assessment_results': ['created_at', 'updated_at'],
            'documents': ['upload_date', 'ocr_processed_at', 'created_at', 'updated_at'],
            'alerts': ['created_at', 'updated_at', 'triggered_at', 'resolved_at'],
            'alert_rules': ['created_at', 'updated_at'],
            'sync_queue': ['created_at'],
            'sync_record': ['timestamp']
        }
    
    def _check_local_record_exists(self, table_name: str, record_id: str) -> Dict[str, Any]:
        """检查本地记录是否存在"""
        if table_name == 'medications':
            check_query = f"SELECT id, status, stop_date FROM {table_name} WHERE id = ? AND custom_id = ?"
        else:
            check_query = f"SELECT id, status FROM {table_name} WHERE id = ? AND custom_id = ?"
        
        return self.db_manager.execute_query(
            check_query, (record_id, self.custom_id), fetch="one"
        )
    
    def _update_existing_record(self, table_name: str, record: Dict[str, Any], 
                               check_result: Dict[str, Any], datetime_fields_map: Dict[str, List[str]]) -> bool:
        """更新现有记录"""
        try:
            update_data = {k: v for k, v in record.items() if k not in ['id', 'custom_id']}
            
            # 设置记录状态
            self._set_record_status(table_name, update_data, record, check_result)
            
            # 处理日期时间字段
            self._process_datetime_fields(update_data, table_name, datetime_fields_map)
            
            # 执行更新
            return self._execute_record_update(table_name, update_data, record.get('id'))
            
        except Exception as e:
            self.logger.error(f"更新记录失败 {table_name}#{record.get('id')}: {str(e)}")
            return False
    
    def _insert_new_record(self, table_name: str, record: Dict[str, Any], 
                          datetime_fields_map: Dict[str, List[str]]) -> bool:
        """插入新记录"""
        try:
            # 设置记录状态和custom_id
            if table_name == 'medications' and record.get('stop_date'):
                record['status'] = 'stopped'
            else:
                record['status'] = 'synced'
            record['custom_id'] = self.custom_id
            
            # 处理日期时间字段
            self._process_datetime_fields(record, table_name, datetime_fields_map)
            
            # 执行插入
            return self._execute_record_insert(table_name, record)
            
        except Exception as e:
            self.logger.error(f"插入记录失败 {table_name}#{record.get('id')}: {str(e)}")
            return False
    
    def _set_record_status(self, table_name: str, update_data: Dict[str, Any], 
                          record: Dict[str, Any], check_result: Dict[str, Any]):
        """设置记录状态"""
        if table_name == 'medications':
            local_status = check_result.get('status') if isinstance(check_result, dict) else None
            local_stop_date = check_result.get('stop_date') if isinstance(check_result, dict) else None
            
            # 如果本地记录已经是stopped状态，或者有stop_date但状态不是stopped，都应该设为stopped
            if local_status == 'stopped' or (local_stop_date and local_stop_date.strip()):
                update_data['status'] = 'stopped'
            elif record.get('stop_date') and str(record.get('stop_date')).strip():
                # 如果服务器记录有stop_date，也设为stopped
                update_data['status'] = 'stopped'
            else:
                update_data['status'] = 'synced'
        else:
            # 对于其他表，检查本地状态
            local_status = check_result.get('status') if isinstance(check_result, dict) else None
            if local_status == 'stopped':
                update_data['status'] = 'stopped'
            else:
                update_data['status'] = 'synced'
    
    def _process_datetime_fields(self, data: Dict[str, Any], table_name: str, 
                                datetime_fields_map: Dict[str, List[str]]):
        """处理日期时间字段"""
        datetime_fields = datetime_fields_map.get(table_name, [])
        for field in datetime_fields:
            if field in data and data[field]:
                try:
                    from .datetime_utils import DateTimeUtils
                    # 确保为ISO字符串格式，与数据库TEXT类型兼容
                    if isinstance(data[field], str):
                        # 验证并标准化ISO字符串格式
                        parsed_dt = DateTimeUtils.parse_datetime(data[field])
                        data[field] = DateTimeUtils.to_iso_datetime(parsed_dt)
                    else:
                        # 转换为ISO字符串格式
                        data[field] = DateTimeUtils.to_iso_datetime(data[field])
                except Exception:
                    # 如果解析失败，使用当前时间的ISO字符串
                    from .datetime_utils import now_iso
                    data[field] = now_iso()
    
    def _execute_record_update(self, table_name: str, update_data: Dict[str, Any], record_id: str) -> bool:
        """执行记录更新"""
        try:
            set_clause = ", ".join([f"{k} = ?" for k in update_data.keys()])
            update_query = f"UPDATE {table_name} SET {set_clause} WHERE id = ? AND custom_id = ?"
            params = list(update_data.values()) + [record_id, self.custom_id]
            
            result = self.db_manager.execute_query(update_query, params)
            return result is not None
        except Exception as e:
            self.logger.error(f"执行更新失败 {table_name}#{record_id}: {str(e)}")
            return False
    
    def _execute_record_insert(self, table_name: str, record: Dict[str, Any]) -> bool:
        """执行记录插入 - 使用统一数据库管理器的insert_record方法处理UNIQUE约束"""
        try:
            # 使用统一数据库管理器的insert_record方法，它包含UNIQUE约束处理逻辑
            result_id = self.db_manager.insert_record(table_name, record)
            if result_id and result_id > 0:
                self.logger.info(f"成功插入记录 {table_name}#{result_id}")
                return True
            else:
                self.logger.warning(f"插入记录返回无效ID {table_name}#{record.get('id')}")
                return False
        except Exception as e:
            self.logger.error(f"执行插入失败 {table_name}#{record.get('id')}: {str(e)}")
            return False
    
    def _save_sync_task(self, sync_record: SyncRecord):
        """保存同步任务到数据库"""
        try:
            task_data = {
                'id': sync_record.id,
                'table_name': sync_record.table_name,
                'operation': sync_record.operation,
                'data': json.dumps(sync_record.data),
                'timestamp': sync_record.timestamp.isoformat(),
                'priority': sync_record.priority,
                'retry_count': sync_record.retry_count,
                'status': sync_record.status,
                'custom_id': sync_record.custom_id,
                'created_at': datetime.now().isoformat()
            }
            
            result = self.db_manager.insert_record('sync_record', task_data)
            if not (result.get('status') == 'success' or result.get('success')):
                self.logger.error(f"保存同步任务失败: {result.get('error')}")
                
        except Exception as e:
            self.logger.error(f"保存同步任务异常: {str(e)}")
    
    def _update_sync_task_status(self, sync_record: SyncRecord):
        """更新同步任务状态"""
        try:
            update_data = {
                'status': sync_record.status,
                'retry_count': sync_record.retry_count,
                'last_error': sync_record.last_error,
                'updated_at': datetime.now().isoformat()
            }
            
            result = self.db_manager.update_record(
                'sync_record', 
                update_data, 
                {'id': sync_record.id}
            )
            
            if not (result.get('status') == 'success' or result.get('success')):
                self.logger.error(f"更新同步任务状态失败: {result.get('error')}")
                
        except Exception as e:
            self.logger.error(f"更新同步任务状态异常: {str(e)}")
    
    def _load_pending_sync_tasks(self):
        """加载待处理的同步任务"""
        try:
            query = """
                SELECT * FROM sync_record
                WHERE custom_id = ? AND status = 'pending'
                ORDER BY priority DESC, timestamp ASC
                LIMIT ?
            """
            
            # 直接获取查询结果，execute_query在fetch="all"时返回字典列表
            result = self.db_manager.execute_query(
                query, (self.custom_id, self.config.max_queue_size), fetch="all"
            )
            
            # execute_query在fetch="all"时直接返回字典列表
            tasks_data = result if isinstance(result, list) else []
            
            if tasks_data:
                loaded_count = 0
                
                for task_data in tasks_data:
                    try:
                        # 类型检查：确保task_data是字典类型
                        if not isinstance(task_data, dict):
                            self.logger.warning(f"跳过非字典类型的任务数据: {type(task_data)} - {task_data}")
                            continue

                        # 安全解析data字段
                        data_field = task_data.get('data', '{}')
                        if isinstance(data_field, str):
                            try:
                                parsed_data = json.loads(data_field)
                            except (json.JSONDecodeError, ValueError):
                                # 如果JSON解析失败，将字符串包装为字典
                                parsed_data = {'value': data_field}
                        elif isinstance(data_field, dict):
                            parsed_data = data_field
                        else:
                            # 对于其他类型（如int, float等），转换为字典
                            parsed_data = {'value': str(data_field)}
                        
                        # 安全解析timestamp字段
                        timestamp_str = task_data.get('timestamp', datetime.now().isoformat())
                        try:
                            # 检查是否包含药物剂量单位
                            dosage_units = ['mg', 'ml', 'g', 'mcg', 'μg', 'ug', 'kg', 'oz', 'lb', 'cc', 'IU', 'units']
                            if any(unit in str(timestamp_str).lower() for unit in dosage_units):
                                self.logger.warning(f"检测到timestamp字段包含药物剂量信息: {timestamp_str}，使用当前时间")
                                parsed_timestamp = datetime.now()
                            elif str(timestamp_str).isdigit():
                                # 纯数字字符串，可能是剂量
                                self.logger.warning(f"检测到timestamp字段为纯数字: {timestamp_str}，使用当前时间")
                                parsed_timestamp = datetime.now()
                            else:
                                parsed_timestamp = datetime.fromisoformat(timestamp_str)
                        except (ValueError, TypeError) as e:
                            self.logger.warning(f"解析timestamp失败: {timestamp_str}, 错误: {e}，使用当前时间")
                            parsed_timestamp = datetime.now()
                        
                        sync_record = SyncRecord(
                            id=task_data.get('id', ''),
                            table_name=task_data.get('table_name', ''),
                            operation=task_data.get('operation', ''),
                            data=parsed_data,
                            timestamp=parsed_timestamp,
                            priority=task_data.get('priority', 0),
                            retry_count=task_data.get('retry_count', 0),
                            status=task_data.get('status', 'pending'),
                            custom_id=task_data.get('custom_id')
                        )
                        
                        # 添加到队列
                        if not self.sync_queue.full():
                            self.sync_queue.put(sync_record)
                            loaded_count += 1
                        else:
                            break
                            
                    except Exception as e:
                        self.logger.error(f"加载同步任务异常: {str(e)}")
                
                self.logger.info(f"加载待处理同步任务: {loaded_count}条")
            else:
                self.logger.info("没有待处理的同步任务")
                
        except Exception as e:
            self.logger.error(f"加载同步任务异常: {str(e)}")
    
    def __del__(self):
        """析构函数"""
        try:
            self.stop_sync_service()
        except Exception:
            pass

# 全局同步客户端实例
_sync_client_instance = None
_sync_client_lock = threading.Lock()

def get_sync_client(custom_id: Optional[str] = None, config: Optional[SyncConfig] = None) -> OptimizedSyncClient:
    """获取同步客户端实例（单例模式）"""
    global _sync_client_instance
    
    with _sync_client_lock:
        if _sync_client_instance is None:
            _sync_client_instance = OptimizedSyncClient(config, custom_id)
        elif custom_id and _sync_client_instance.custom_id != custom_id:
            # 用户切换，重新连接
            _sync_client_instance.connect_user(custom_id)
        
        return _sync_client_instance

# 导出主要类和函数
__all__ = [
    'SyncConfig',
    'SyncRecord', 
    'OptimizedSyncClient',
    'get_sync_client'
]

# 测试代码
if __name__ == "__main__":
    # 基本功能测试
    logging.basicConfig(level=logging.INFO)
    
    # 创建同步客户端
    config = SyncConfig(batch_size=50, sync_interval=60)
    client = get_sync_client("test_user_001", config)
    
    # 启动同步服务
    client.start_sync_service()
    
    # 添加测试同步任务
    test_data = {
        'id': 'med_test_002',
        'custom_id': 'test_user_001',  # 添加必需的custom_id字段
        'name': '测试药物',
        'dosage': '100mg',
        'frequency': '每日一次',
        'created_at': datetime.now().isoformat(),
        'updated_at': datetime.now().isoformat()
    }
    
    client.queue_sync_task('medications', 'insert', test_data, priority=1)
    
    # 获取同步状态
    status = client.get_sync_status()
    print(f"同步状态: {json.dumps(status, indent=2, ensure_ascii=False)}")
    
    # 等待一段时间后停止
    time.sleep(10)
    client.stop_sync_service()
    
    print("测试完成")