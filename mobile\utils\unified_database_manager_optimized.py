#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
统一数据库管理器（优化版）

该模块整合了fallback_database_manager.py和unified_fallback_manager.py的功能，
提供统一的数据库管理和降级处理机制。

主要功能:
1. 统一数据库路径管理（移动端：health_data.db，后端：YUN/backend/app.db）
2. 自动降级处理（统一数据库不可用时切换到本地SQLite）
3. 标准化的数据操作接口
4. 统一的错误处理和日志记录
5. 数据同步队列管理
版本: 2.1.0
"""

import os
import sqlite3
import json
import logging
import threading
import time
from datetime import datetime
from pathlib import Path
from typing import Dict, List, Optional, Any, Tuple, Union
from contextlib import contextmanager
try:
    from .connection_pool_manager import get_connection_pool_manager
    from .datetime_utils import DateTimeUtils, now_iso, today_iso, to_iso
    from .simple_server_manager import get_server_manager
except ImportError:
    # 直接运行时的导入处理
    import sys
    import os
    current_dir = os.path.dirname(os.path.abspath(__file__))
    parent_dir = os.path.dirname(current_dir)
    if parent_dir not in sys.path:
        sys.path.insert(0, parent_dir)
    try:
        from utils.connection_pool_manager import get_connection_pool_manager
        from utils.datetime_utils import DateTimeUtils, now_iso, today_iso, to_iso
        from utils.simple_server_manager import get_server_manager
    except ImportError:
        # 最后的备用导入方式
        from mobile.utils.connection_pool_manager import get_connection_pool_manager
        from mobile.utils.datetime_utils import DateTimeUtils, now_iso, today_iso, to_iso
        from mobile.utils.simple_server_manager import get_server_manager
# 注释：sqlite3 datetime适配器功能已集成到DateTimeUtils模块中

# 配置日志
logger = logging.getLogger(__name__)

class UnifiedDatabaseManager:
    """
    统一数据库管理器
    
    整合移动端和后端数据库管理功能，提供统一的接口和降级处理。
    """
    
    _instance = None
    _lock = threading.RLock()
    
    # 标准化数据库路径配置
    DATABASE_PATHS = {
        'mobile': 'data/health_data.db',  # 移动端主数据库
        'backend': 'YUN/backend/app.db',  # 后端数据库
        'fallback': 'data/fallback_database.db'  # 降级数据库
    }

    # 新增表的索引定义
    ADDITIONAL_INDEXES = [
        # Documents表索引
        "CREATE INDEX IF NOT EXISTS idx_documents_custom_id ON documents(custom_id)",
        "CREATE INDEX IF NOT EXISTS idx_documents_type ON documents(document_type)",
        "CREATE INDEX IF NOT EXISTS idx_documents_filename ON documents(filename)",
        "CREATE INDEX IF NOT EXISTS idx_documents_status ON documents(status)",
        
        # Examination Reports表索引
        "CREATE INDEX IF NOT EXISTS idx_examination_reports_custom_id ON examination_reports(custom_id)",
        "CREATE INDEX IF NOT EXISTS idx_examination_reports_exam_type ON examination_reports(exam_type)",
        "CREATE INDEX IF NOT EXISTS idx_examination_reports_exam_date ON examination_reports(exam_date)",
        
        # Imaging Reports表索引
        "CREATE INDEX IF NOT EXISTS idx_imaging_reports_custom_id ON imaging_reports(custom_id)",
        "CREATE INDEX IF NOT EXISTS idx_imaging_reports_report_type ON imaging_reports(report_type)",
        "CREATE INDEX IF NOT EXISTS idx_imaging_reports_exam_date ON imaging_reports(exam_date)",
        "CREATE INDEX IF NOT EXISTS idx_imaging_reports_body_part ON imaging_reports(body_part)",
        
        # Alerts表索引
        "CREATE INDEX IF NOT EXISTS idx_alerts_custom_id ON alerts(custom_id)",
        "CREATE INDEX IF NOT EXISTS idx_alerts_severity ON alerts(severity)",
        "CREATE INDEX IF NOT EXISTS idx_alerts_status ON alerts(status)",
        
        # Alert Rules表索引
        "CREATE INDEX IF NOT EXISTS idx_alert_rules_custom_id ON alert_rules(custom_id)",
        "CREATE INDEX IF NOT EXISTS idx_alert_rules_is_active ON alert_rules(is_active)",
        
        # Alert Channels表索引
        "CREATE INDEX IF NOT EXISTS idx_alert_channels_custom_id ON alert_channels(custom_id)",
        "CREATE INDEX IF NOT EXISTS idx_alert_channels_is_active ON alert_channels(is_active)",
        
        # Assessments表索引
        "CREATE INDEX IF NOT EXISTS idx_assessments_custom_id ON assessments(custom_id)",
        "CREATE INDEX IF NOT EXISTS idx_assessments_status ON assessments(status)",
        
        # Assessment Items表索引
        "CREATE INDEX IF NOT EXISTS idx_assessment_items_custom_id ON assessment_items(custom_id)",
        "CREATE INDEX IF NOT EXISTS idx_assessment_items_assessment_id ON assessment_items(assessment_id)",
        
        # Assessment Responses表索引
        "CREATE INDEX IF NOT EXISTS idx_assessment_responses_custom_id ON assessment_responses(custom_id)",
        "CREATE INDEX IF NOT EXISTS idx_assessment_responses_assessment_id ON assessment_responses(assessment_id)",
        
        # Assessment Templates表索引
        "CREATE INDEX IF NOT EXISTS idx_assessment_templates_custom_id ON assessment_templates(custom_id)",
        "CREATE INDEX IF NOT EXISTS idx_assessment_templates_is_active ON assessment_templates(is_active)",
        
        # Assessment Template Questions表索引
        "CREATE INDEX IF NOT EXISTS idx_assessment_template_questions_custom_id ON assessment_template_questions(custom_id)",
        "CREATE INDEX IF NOT EXISTS idx_assessment_template_questions_template_id ON assessment_template_questions(template_id)",
        
        # Basic Health Info表索引
        "CREATE INDEX IF NOT EXISTS idx_basic_health_info_custom_id ON basic_health_info(custom_id)",
        "CREATE INDEX IF NOT EXISTS idx_basic_health_info_user_custom_id ON basic_health_info(user_custom_id)",
        "CREATE INDEX IF NOT EXISTS idx_basic_health_info_category ON basic_health_info(category)",
        "CREATE INDEX IF NOT EXISTS idx_basic_health_info_status ON basic_health_info(status)",
        
        # Basic Health Info List表索引
        "CREATE INDEX IF NOT EXISTS idx_basic_health_info_list_custom_id ON basic_health_info_list(custom_id)",
        "CREATE INDEX IF NOT EXISTS idx_basic_health_info_list_user_custom_id ON basic_health_info_list(user_custom_id)",
        "CREATE INDEX IF NOT EXISTS idx_basic_health_info_list_category ON basic_health_info_list(category)",
        "CREATE INDEX IF NOT EXISTS idx_basic_health_info_list_status ON basic_health_info_list(status)"
    ]
    
    # 统一表结构定义
    TABLE_SCHEMAS = {
        'medications': '''
            CREATE TABLE IF NOT EXISTS medications (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                custom_id TEXT NOT NULL,
                sync_status INTEGER DEFAULT 0,
                cloud_id TEXT,
                created_at TEXT DEFAULT (datetime('now')),
                updated_at TEXT DEFAULT (datetime('now')),
                name TEXT NOT NULL,
                dosage TEXT,
                frequency TEXT,
                start_date TEXT,
                end_date TEXT,
                stop_date TEXT,
                instructions TEXT,
                notes TEXT,
                reason TEXT,
                stop_reason TEXT,
                status TEXT DEFAULT 'active',
                medication_type TEXT,
                prescription_required INTEGER DEFAULT 0,
                reminder_enabled INTEGER DEFAULT 0,
                reminder_times TEXT,
                reminder_settings TEXT,
                review_reminder TEXT,
                FOREIGN KEY (custom_id) REFERENCES users(custom_id)
            )
        ''',
        'health_records': '''
            CREATE TABLE IF NOT EXISTS health_records (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                custom_id TEXT NOT NULL,
                sync_status INTEGER DEFAULT 0,
                cloud_id TEXT,
                created_at TEXT DEFAULT (datetime('now')),
                updated_at TEXT DEFAULT (datetime('now')),
                recordtype TEXT NOT NULL,
                title TEXT NOT NULL,
                content TEXT,
                description TEXT,
                is_important INTEGER DEFAULT 0,
                date_recorded TEXT,
                tags TEXT,
                file_path TEXT,
                item_type TEXT DEFAULT 'text',
                status TEXT DEFAULT 'active',
                FOREIGN KEY (custom_id) REFERENCES users(custom_id)
            )
        ''',
        'sync_queue': '''
            CREATE TABLE IF NOT EXISTS sync_queue (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                custom_id TEXT UNIQUE,
                sync_status INTEGER DEFAULT 0,
                cloud_id TEXT,
                created_at TEXT DEFAULT (datetime('now')),
                updated_at TEXT DEFAULT (datetime('now')),
                table_name TEXT NOT NULL,
                record_id INTEGER NOT NULL,
                action TEXT NOT NULL,
                data TEXT,
                retries INTEGER DEFAULT 0,
                max_retries INTEGER DEFAULT 3
            )
        ''',
        'sync_record': '''
            CREATE TABLE IF NOT EXISTS sync_record (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                custom_id TEXT UNIQUE,
                sync_status INTEGER DEFAULT 0,
                cloud_id TEXT,
                created_at TEXT DEFAULT (datetime('now')),
                updated_at TEXT DEFAULT (datetime('now')),
                table_name TEXT NOT NULL,
                operation TEXT NOT NULL,
                data TEXT,
                timestamp TEXT,
                priority INTEGER DEFAULT 0,
                retry_count INTEGER DEFAULT 0,
                last_error TEXT,
                operation_state TEXT DEFAULT 'pending'
            )
        ''',
        'sync_tasks': '''
            CREATE TABLE IF NOT EXISTS sync_tasks (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                custom_id TEXT UNIQUE,
                sync_status INTEGER DEFAULT 0,
                cloud_id TEXT,
                created_at TEXT DEFAULT (datetime('now')),
                updated_at TEXT DEFAULT (datetime('now')),
                table_name TEXT NOT NULL,
                operation TEXT NOT NULL,
                data TEXT,
                priority INTEGER DEFAULT 0,
                mode TEXT DEFAULT 'bidirectional',
                strategy TEXT DEFAULT 'server_wins',
                timestamp TEXT,
                retry_count INTEGER DEFAULT 0,
                max_retries INTEGER DEFAULT 3,
                timeout INTEGER DEFAULT 30,
                status TEXT DEFAULT 'pending',
                error_message TEXT,
                metadata TEXT DEFAULT '{}'
            )
        ''',
        'user_configs': '''
            CREATE TABLE IF NOT EXISTS user_configs (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                custom_id TEXT UNIQUE,
                sync_status INTEGER DEFAULT 0,
                cloud_id TEXT,
                created_at TEXT DEFAULT (datetime('now')),
                updated_at TEXT DEFAULT (datetime('now')),
                config_key TEXT NOT NULL,
                config_value TEXT,
                UNIQUE(custom_id, config_key)
            )
        ''',
        'users': '''
            CREATE TABLE IF NOT EXISTS users (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                custom_id TEXT UNIQUE,
                sync_status INTEGER DEFAULT 0,
                cloud_id TEXT,
                created_at TEXT DEFAULT (datetime('now')),
                updated_at TEXT DEFAULT (datetime('now')),
                username TEXT UNIQUE,
                email TEXT,
                phone TEXT,
                hashed_password TEXT NOT NULL,
                full_name TEXT,
                role TEXT DEFAULT 'personal',
                is_active INTEGER DEFAULT 1,
                is_superuser INTEGER DEFAULT 0,
                id_number TEXT,
                gender TEXT,
                birth_date TEXT,
                address TEXT,
                profile_photo TEXT,
                emergency_contact TEXT,
                emergency_phone TEXT,
                registration_type TEXT,
                relationship_type TEXT,
                additional_roles TEXT,
                verification_status TEXT DEFAULT 'verified',
                is_first_login INTEGER DEFAULT 1,
                role_application_status TEXT,
                role_application_role TEXT,
                password_reset_at TEXT,
                status TEXT DEFAULT 'active'
            )
        ''',
        'medical_records': '''
            CREATE TABLE IF NOT EXISTS medical_records (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                custom_id TEXT UNIQUE,
                sync_status INTEGER DEFAULT 0,
                cloud_id TEXT,
                created_at TEXT DEFAULT (datetime('now')),
                updated_at TEXT DEFAULT (datetime('now')),
                user_custom_id TEXT,
                recordtype TEXT NOT NULL,
                title TEXT,
                description TEXT,
                diagnosis TEXT,
                treatment TEXT,
                doctor_name TEXT,
                hospital_name TEXT,
                department TEXT,
                visit_date TEXT,
                follow_up_date TEXT,
                severity TEXT,
                prescription TEXT,
                is_important INTEGER DEFAULT 0,
                status TEXT DEFAULT 'active',
                file_attachments TEXT,
                notes TEXT,
                FOREIGN KEY (user_custom_id) REFERENCES users(custom_id)
            )
        ''',
        'lab_reports': '''
            CREATE TABLE IF NOT EXISTS lab_reports (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                custom_id TEXT UNIQUE,
                sync_status INTEGER DEFAULT 0,
                cloud_id TEXT,
                created_at TEXT DEFAULT (datetime('now')),
                updated_at TEXT DEFAULT (datetime('now')),
                user_custom_id TEXT,
                report_type TEXT NOT NULL,
                test_name TEXT,
                test_date TEXT,
                report_date TEXT,
                result_value TEXT,
                reference_range TEXT,
                unit TEXT,
                status TEXT DEFAULT 'normal',
                doctor_name TEXT,
                hospital_name TEXT,
                department TEXT,
                lab_name TEXT,
                diagnosis TEXT,
                is_abnormal INTEGER DEFAULT 0,
                notes TEXT,
                file_path TEXT,
                FOREIGN KEY (user_custom_id) REFERENCES users(custom_id)
            )
        ''',
        'lab_report_items': '''
            CREATE TABLE IF NOT EXISTS lab_report_items (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                custom_id TEXT UNIQUE,
                sync_status INTEGER DEFAULT 0,
                cloud_id TEXT,
                created_at TEXT DEFAULT (datetime('now')),
                updated_at TEXT DEFAULT (datetime('now')),
                report_id INTEGER,
                item_name TEXT NOT NULL,
                item_value TEXT NOT NULL,
                item_unit TEXT,
                reference_range TEXT,
                is_abnormal INTEGER DEFAULT 0,
                abnormal_level TEXT,
                notes TEXT,
                FOREIGN KEY (report_id) REFERENCES lab_reports(id)
            )
        ''',
        'inpatient_records': '''
            CREATE TABLE IF NOT EXISTS inpatient_records (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                custom_id TEXT UNIQUE,
                sync_status INTEGER DEFAULT 0,
                cloud_id TEXT,
                created_at TEXT DEFAULT (datetime('now')),
                updated_at TEXT DEFAULT (datetime('now')),
                medical_record_id INTEGER,
                hospital_name TEXT NOT NULL,
                department TEXT,
                admission_date TEXT,
                discharge_date TEXT,
                admission_diagnosis TEXT,
                discharge_diagnosis TEXT,
                treatment_summary TEXT,
                doctor_advice TEXT,
                notes TEXT,
                FOREIGN KEY (medical_record_id) REFERENCES medical_records(id)
            )
        ''',
        'surgery_records': '''
            CREATE TABLE IF NOT EXISTS surgery_records (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                custom_id TEXT UNIQUE,
                sync_status INTEGER DEFAULT 0,
                cloud_id TEXT,
                created_at TEXT DEFAULT (datetime('now')),
                updated_at TEXT DEFAULT (datetime('now')),
                medical_record_id INTEGER,
                hospital_name TEXT NOT NULL,
                department TEXT,
                surgery_name TEXT NOT NULL,
                surgery_date TEXT,
                surgeon TEXT,
                anesthesia_type TEXT,
                surgery_description TEXT,
                complications TEXT,
                notes TEXT,
                FOREIGN KEY (medical_record_id) REFERENCES medical_records(id)
            )
        ''',
        'documents': '''
            CREATE TABLE IF NOT EXISTS documents (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                custom_id TEXT NOT NULL,
                health_record_id INTEGER,
                medical_record_id INTEGER,
                lab_report_id INTEGER,
                examination_report_id INTEGER,
                documentrecord_id INTEGER,
                title TEXT,
                filename TEXT NOT NULL,
                file_path TEXT NOT NULL,
                file_size INTEGER NOT NULL,
                document_type TEXT,
                document_category TEXT,
                mime_type TEXT,
                description TEXT,
                source TEXT,
                status TEXT DEFAULT 'active',
                file_metadata TEXT,
                ocr_processed INTEGER DEFAULT 0,
                ocr_content TEXT,
                ocr_status TEXT,
                ocr_confidence REAL,
                created_at TEXT DEFAULT (datetime('now')),
                updated_at TEXT DEFAULT (datetime('now')),
                FOREIGN KEY (health_record_id) REFERENCES health_records(id),
                FOREIGN KEY (medical_record_id) REFERENCES medical_records(id),
                FOREIGN KEY (lab_report_id) REFERENCES lab_reports(id),
                FOREIGN KEY (examination_report_id) REFERENCES examination_reports(id)
            )
        ''',
        'examination_reports': '''
            CREATE TABLE IF NOT EXISTS examination_reports (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                custom_id TEXT UNIQUE,
                sync_status INTEGER DEFAULT 0,
                cloud_id TEXT,
                created_at TEXT DEFAULT (datetime('now')),
                updated_at TEXT DEFAULT (datetime('now')),
                user_id INTEGER,
                exam_type TEXT NOT NULL,
                hospital_name TEXT,
                department TEXT,
                exam_part TEXT,
                exam_date TEXT,
                report_date TEXT,
                device TEXT,
                doctor_name TEXT,
                description TEXT,
                conclusion TEXT,
                recommendation TEXT,
                notes TEXT,
                is_abnormal INTEGER DEFAULT 0,
                FOREIGN KEY (user_id) REFERENCES users(id)
            )
        ''',
        'imaging_reports': '''
            CREATE TABLE IF NOT EXISTS imaging_reports (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                custom_id TEXT UNIQUE,
                sync_status INTEGER DEFAULT 0,
                cloud_id TEXT,
                created_at TEXT DEFAULT (datetime('now')),
                updated_at TEXT DEFAULT (datetime('now')),
                user_id INTEGER,
                report_type TEXT,
                body_part TEXT,
                hospital_name TEXT,
                department TEXT,
                doctor_name TEXT,
                exam_date TEXT,
                report_date TEXT,
                findings TEXT,
                impression TEXT,
                recommendations TEXT,
                is_abnormal INTEGER DEFAULT 0,
                notes TEXT,
                FOREIGN KEY (user_id) REFERENCES users(id)
            )
        ''',
        'alerts': '''
            CREATE TABLE IF NOT EXISTS alerts (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                custom_id TEXT UNIQUE,
                sync_status INTEGER DEFAULT 0,
                cloud_id TEXT,
                created_at TEXT DEFAULT (datetime('now')),
                updated_at TEXT DEFAULT (datetime('now')),
                title TEXT NOT NULL,
                description TEXT,
                severity TEXT DEFAULT 'medium',
                status TEXT DEFAULT 'active',
                source TEXT,
                acknowledged_at TEXT,
                resolved_at TEXT
            )
        ''',
        'alert_rules': '''
            CREATE TABLE IF NOT EXISTS alert_rules (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                custom_id TEXT UNIQUE,
                sync_status INTEGER DEFAULT 0,
                cloud_id TEXT,
                created_at TEXT DEFAULT (datetime('now')),
                updated_at TEXT DEFAULT (datetime('now')),
                name TEXT NOT NULL,
                description TEXT,
                condition_type TEXT NOT NULL,
                condition_value TEXT,
                severity TEXT DEFAULT 'medium',
                is_active INTEGER DEFAULT 1
            )
        ''',
        'alert_channels': '''
            CREATE TABLE IF NOT EXISTS alert_channels (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                custom_id TEXT UNIQUE,
                sync_status INTEGER DEFAULT 0,
                cloud_id TEXT,
                created_at TEXT DEFAULT (datetime('now')),
                updated_at TEXT DEFAULT (datetime('now')),
                name TEXT NOT NULL,
                channel_type TEXT NOT NULL,
                configuration TEXT,
                is_active INTEGER DEFAULT 1
            )
        ''',
        'assessments': '''
            CREATE TABLE IF NOT EXISTS assessments (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                custom_id TEXT UNIQUE,
                sync_status INTEGER DEFAULT 0,
                cloud_id TEXT,
                created_at TEXT DEFAULT (datetime('now')),
                updated_at TEXT DEFAULT (datetime('now')),
                user_id INTEGER,
                template_id INTEGER,
                assessment_type TEXT,
                name TEXT,
                version TEXT,
                round_number INTEGER,
                sequence_number INTEGER,
                unique_identifier TEXT,
                completed_at TEXT,
                assessor TEXT,
                score REAL,
                max_score REAL,
                result TEXT,
                conclusion TEXT,
                notes TEXT,
                status TEXT DEFAULT 'pending',
                last_reminded_at TEXT,
                FOREIGN KEY (user_id) REFERENCES users(id),
                FOREIGN KEY (template_id) REFERENCES assessment_templates(id)
            )
        ''',
        'assessment_items': '''
            CREATE TABLE IF NOT EXISTS assessment_items (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                custom_id TEXT UNIQUE,
                sync_status INTEGER DEFAULT 0,
                cloud_id TEXT,
                created_at TEXT DEFAULT (datetime('now')),
                updated_at TEXT DEFAULT (datetime('now')),
                assessment_id INTEGER,
                question_text TEXT NOT NULL,
                question_type TEXT DEFAULT 'text',
                options TEXT,
                answer TEXT,
                score REAL,
                max_score REAL,
                notes TEXT,
                FOREIGN KEY (assessment_id) REFERENCES assessments(id)
            )
        ''',
        'assessment_responses': '''
            CREATE TABLE IF NOT EXISTS assessment_responses (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                custom_id TEXT UNIQUE,
                sync_status INTEGER DEFAULT 0,
                cloud_id TEXT,
                created_at TEXT DEFAULT (datetime('now')),
                updated_at TEXT DEFAULT (datetime('now')),
                assessment_id INTEGER,
                item_id INTEGER,
                response_value TEXT,
                response_score REAL,
                FOREIGN KEY (assessment_id) REFERENCES assessments(id),
                FOREIGN KEY (item_id) REFERENCES assessment_items(id)
            )
        ''',
        'assessment_templates': '''
            CREATE TABLE IF NOT EXISTS assessment_templates (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                custom_id TEXT UNIQUE,
                sync_status INTEGER DEFAULT 0,
                cloud_id TEXT,
                created_at TEXT DEFAULT (datetime('now')),
                updated_at TEXT DEFAULT (datetime('now')),
                name TEXT NOT NULL,
                description TEXT,
                version TEXT,
                category TEXT,
                is_active INTEGER DEFAULT 1
            )
        ''',
        'assessment_template_questions': '''
            CREATE TABLE IF NOT EXISTS assessment_template_questions (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                custom_id TEXT UNIQUE,
                sync_status INTEGER DEFAULT 0,
                cloud_id TEXT,
                created_at TEXT DEFAULT (datetime('now')),
                updated_at TEXT DEFAULT (datetime('now')),
                template_id INTEGER,
                question_text TEXT NOT NULL,
                question_type TEXT DEFAULT 'text',
                options TEXT,
                order_index INTEGER,
                is_required INTEGER DEFAULT 0,
                scoring_rule TEXT,
                FOREIGN KEY (template_id) REFERENCES assessment_templates(id)
            )
        ''',
        'questionnaires': '''
            CREATE TABLE IF NOT EXISTS questionnaires (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                custom_id TEXT,
                sync_status INTEGER DEFAULT 0,
                cloud_id TEXT,
                created_at TEXT DEFAULT (datetime('now')),
                updated_at TEXT DEFAULT (datetime('now')),
                user_custom_id TEXT,
                questionnaire_type TEXT,
                title TEXT,
                description TEXT,
                questions TEXT,
                answers TEXT,
                completion_date TEXT,
                score REAL,
                max_score INTEGER,
                template_id INTEGER,
                notes TEXT,
                version TEXT,
                created_by INTEGER,
                interpretation TEXT,
                status TEXT DEFAULT 'active',
                last_reminded_at TEXT,
                round_number INTEGER DEFAULT 1,
                sequence_number INTEGER DEFAULT 1,
                unique_identifier TEXT UNIQUE,
                FOREIGN KEY (user_custom_id) REFERENCES users(custom_id)
            )
        ''',
        'basic_health_info': '''
            CREATE TABLE IF NOT EXISTS basic_health_info (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                custom_id TEXT UNIQUE,
                sync_status INTEGER DEFAULT 0,
                cloud_id TEXT,
                created_at TEXT DEFAULT (datetime('now')),
                updated_at TEXT DEFAULT (datetime('now')),
                user_custom_id TEXT NOT NULL,
                category TEXT NOT NULL,
                item_key TEXT NOT NULL,
                item_value TEXT,
                unit TEXT,
                notes TEXT,
                status TEXT DEFAULT 'active',
                UNIQUE(user_custom_id, category, item_key),
                FOREIGN KEY (user_custom_id) REFERENCES users(custom_id)
            )
        ''',
        'basic_health_info_list': '''
            CREATE TABLE IF NOT EXISTS basic_health_info_list (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                custom_id TEXT UNIQUE,
                sync_status INTEGER DEFAULT 0,
                cloud_id TEXT,
                created_at TEXT DEFAULT (datetime('now')),
                updated_at TEXT DEFAULT (datetime('now')),
                user_custom_id TEXT NOT NULL,
                category TEXT NOT NULL,
                item_data TEXT,
                notes TEXT,
                status TEXT DEFAULT 'active',
                FOREIGN KEY (user_custom_id) REFERENCES users(custom_id)
            )
        '''
    }
    
    EXPECTED_FIELDS = {
        'medications': ['custom_id', 'sync_status', 'cloud_id', 'name', 'dosage', 'frequency', 'start_date', 'end_date', 'stop_date', 'instructions', 'notes', 'reason', 'stop_reason', 'status', 'medication_type', 'prescription_required', 'reminder_enabled', 'reminder_times', 'reminder_settings', 'review_reminder', 'created_at', 'updated_at'],
        'health_records': ['custom_id', 'sync_status', 'cloud_id', 'user_custom_id', 'recordtype', 'title', 'content', 'description', 'is_important', 'date_recorded', 'tags', 'file_path', 'item_type', 'status', 'created_at', 'updated_at'],
        'sync_queue': ['custom_id', 'sync_status', 'cloud_id', 'table_name', 'record_id', 'action', 'data', 'retries', 'max_retries', 'created_at', 'updated_at'],
        'sync_record': ['custom_id', 'sync_status', 'cloud_id', 'table_name', 'operation', 'data', 'timestamp', 'priority', 'retry_count', 'last_error', 'operation_state', 'created_at', 'updated_at'],
        'sync_tasks': ['custom_id', 'sync_status', 'cloud_id', 'table_name', 'operation', 'data', 'priority', 'mode', 'strategy', 'timestamp', 'retry_count', 'max_retries', 'timeout', 'status', 'error_message', 'metadata', 'created_at', 'updated_at'],
        'user_configs': ['custom_id', 'sync_status', 'cloud_id', 'config_key', 'config_value', 'created_at', 'updated_at'],
        'users': ['custom_id', 'username', 'email', 'phone', 'hashed_password', 'full_name', 'role', 'is_active', 'is_superuser', 'id_number', 'gender', 'birth_date', 'address', 'profile_photo', 'emergency_contact', 'emergency_phone', 'registration_type', 'relationship_type', 'additional_roles', 'verification_status', 'is_first_login', 'role_application_status', 'role_application_role', 'password_reset_at', 'created_at', 'updated_at'],
        'medical_records': ['custom_id', 'sync_status', 'cloud_id', 'user_custom_id', 'recordtype', 'title', 'description', 'diagnosis', 'treatment', 'doctor_name', 'hospital_name', 'department', 'visit_date', 'follow_up_date', 'severity', 'prescription', 'is_important', 'status', 'file_attachments', 'notes', 'created_at', 'updated_at'],
        'lab_reports': ['custom_id', 'sync_status', 'cloud_id', 'user_custom_id', 'report_type', 'test_name', 'test_date', 'report_date', 'result_value', 'reference_range', 'unit', 'status', 'doctor_name', 'hospital_name', 'department', 'lab_name', 'diagnosis', 'is_abnormal', 'notes', 'file_path', 'created_at', 'updated_at'],
        'lab_report_items': ['custom_id', 'sync_status', 'cloud_id', 'report_id', 'item_name', 'item_value', 'item_unit', 'reference_range', 'is_abnormal', 'abnormal_level', 'notes', 'created_at', 'updated_at'],
        'inpatient_records': ['custom_id', 'sync_status', 'cloud_id', 'medical_record_id', 'hospital_name', 'department', 'admission_date', 'discharge_date', 'admission_diagnosis', 'discharge_diagnosis', 'treatment_summary', 'doctor_advice', 'notes', 'created_at', 'updated_at'],
        'surgery_records': ['custom_id', 'sync_status', 'cloud_id', 'medical_record_id', 'hospital_name', 'department', 'surgery_name', 'surgery_date', 'surgeon', 'anesthesia_type', 'surgery_description', 'complications', 'notes', 'created_at', 'updated_at'],
        'documents': ['custom_id', 'sync_status', 'cloud_id', 'health_record_id', 'medical_record_id', 'lab_report_id', 'examination_report_id', 'title', 'filename', 'file_path', 'file_size', 'document_type', 'document_category', 'mime_type', 'description', 'source', 'status', 'file_metadata', 'ocr_processed', 'ocr_content', 'ocr_status', 'ocr_confidence', 'created_at', 'updated_at'],
        'examination_reports': ['custom_id', 'sync_status', 'cloud_id', 'user_id', 'exam_type', 'hospital_name', 'department', 'exam_part', 'exam_date', 'report_date', 'device', 'doctor_name', 'description', 'conclusion', 'recommendation', 'notes', 'is_abnormal', 'created_at', 'updated_at'],
        'imaging_reports': ['custom_id', 'sync_status', 'cloud_id', 'user_id', 'report_type', 'body_part', 'hospital_name', 'department', 'doctor_name', 'exam_date', 'report_date', 'findings', 'impression', 'recommendations', 'is_abnormal', 'notes', 'created_at', 'updated_at'],
        'alerts': ['custom_id', 'sync_status', 'cloud_id', 'title', 'description', 'severity', 'status', 'source', 'acknowledged_at', 'resolved_at', 'created_at', 'updated_at'],
        'alert_rules': ['custom_id', 'sync_status', 'cloud_id', 'name', 'description', 'condition_type', 'condition_value', 'severity', 'is_active', 'created_at', 'updated_at'],
        'alert_channels': ['custom_id', 'sync_status', 'cloud_id', 'name', 'channel_type', 'configuration', 'is_active', 'created_at', 'updated_at'],
        'assessments': ['custom_id', 'sync_status', 'cloud_id', 'user_id', 'template_id', 'assessment_type', 'name', 'version', 'round_number', 'sequence_number', 'unique_identifier', 'completed_at', 'assessor', 'score', 'max_score', 'result', 'conclusion', 'notes', 'status', 'last_reminded_at', 'created_at', 'updated_at'],
        'assessment_items': ['custom_id', 'sync_status', 'cloud_id', 'assessment_id', 'question_text', 'question_type', 'options', 'answer', 'score', 'max_score', 'notes', 'created_at', 'updated_at'],
        'assessment_responses': ['custom_id', 'sync_status', 'cloud_id', 'assessment_id', 'item_id', 'response_value', 'response_score', 'created_at', 'updated_at'],
        'assessment_templates': ['custom_id', 'sync_status', 'cloud_id', 'name', 'description', 'version', 'category', 'is_active', 'created_at', 'updated_at'],
        'assessment_template_questions': ['custom_id', 'sync_status', 'cloud_id', 'template_id', 'question_text', 'question_type', 'options', 'order_index', 'is_required', 'scoring_rule', 'created_at', 'updated_at'],
        'questionnaires': ['custom_id', 'sync_status', 'cloud_id', 'user_custom_id', 'questionnaire_type', 'title', 'description', 'questions', 'answers', 'completion_date', 'score', 'max_score', 'template_id', 'notes', 'version', 'created_by', 'interpretation', 'status', 'last_reminded_at', 'round_number', 'sequence_number', 'unique_identifier', 'created_at', 'updated_at'],
        'basic_health_info': ['custom_id', 'sync_status', 'cloud_id', 'user_custom_id', 'category', 'item_key', 'item_value', 'unit', 'notes', 'status', 'created_at', 'updated_at'],
        'basic_health_info_list': ['custom_id', 'sync_status', 'cloud_id', 'user_custom_id', 'category', 'item_data', 'notes', 'status', 'created_at', 'updated_at']
    }
    
    @classmethod
    def get_instance(cls, module_name: str = "default"):
        """获取单例实例"""
        if cls._instance is None:
            with cls._lock:
                if cls._instance is None:
                    cls._instance = cls(module_name)
        return cls._instance
    
    def __init__(self, module_name: str = "unified"):
        """初始化统一数据库管理器"""
        if hasattr(self, '_initialized'):
            return
            
        self._initialized = True
        self.module_name = module_name
        self._connections: Dict[str, sqlite3.Connection] = {}
        
        # 集成简化的服务器管理器
        self.server_manager = get_server_manager()
        
        # 数据库状态管理
        self.is_fallback_mode = False
        self.use_unified_db = True
        self.last_cloud_check = 0
        self.cloud_check_interval = 30  # 30秒检查一次
        
        # 初始化数据库路径
        self._init_database_paths()
        
        # 检查统一数据库可用性
        self._check_unified_availability()
        
        # 初始化数据库
        self._init_databases()
        
        # 延迟表创建，避免在初始化时重复操作
        # 表创建将在第一次实际使用时进行
        self._tables_ensured = False
        
        logger.info(f"统一数据库管理器初始化完成 - 模块: {module_name}")
        logger.info(f"当前服务器: {self.server_manager.get_current_server_name()}")
    
    def _init_database_paths(self):
        """初始化数据库路径"""
        try:
            # 项目根目录
            self.project_root = Path(__file__).parent.parent.parent
            
            # 移动端数据库路径
            self.mobile_db_path = self.project_root / "mobile" / self.DATABASE_PATHS['mobile']
            
            # 后端数据库路径
            self.backend_db_path = self.project_root / self.DATABASE_PATHS['backend']
            
            # 降级数据库路径
            self.fallback_db_path = self.project_root / "mobile" / self.DATABASE_PATHS['fallback']
            
            # 确保数据目录存在
            self.fallback_db_path.parent.mkdir(parents=True, exist_ok=True)
            
            # 只在第一次初始化时输出数据库路径配置信息
            global _db_paths_logged, _db_paths_lock
            with _db_paths_lock:
                if not _db_paths_logged:
                    logger.info("数据库路径配置完成:")
                    logger.info(f"  移动端: {self.mobile_db_path}")
                    logger.info(f"  后端: {self.backend_db_path}")
                    logger.info(f"  降级: {self.fallback_db_path}")
                    _db_paths_logged = True
            
        except Exception as e:
            logger.error(f"初始化数据库路径失败: {str(e)}")
            raise
    
    def _check_unified_availability(self) -> bool:
        """检查统一数据库可用性"""
        try:
            # 避免频繁检查
            current_time = time.time()
            if current_time - self.last_cloud_check < self.cloud_check_interval:
                return self.use_unified_db
            
            self.last_cloud_check = current_time
            
            # 尝试导入统一数据库配置模块（避免循环依赖）
            try:
                from config.unified_database_config import get_unified_config
                
                config = get_unified_config()
                
                if config and hasattr(config, 'UNIFIED_DB_PATH'):
                    self.use_unified_db = True
                    self.is_fallback_mode = False
                    logger.info("✅ 统一数据库配置可用，使用统一模式")
                else:
                    raise ValueError("统一数据库配置不完整")
                    
            except Exception as e:
                logger.warning(f"⚠️ 统一数据库配置不可用，切换到降级模式: {e}")
                self.use_unified_db = False
                self.is_fallback_mode = True
            
            return self.use_unified_db
            
        except Exception as e:
            logger.error(f"检查统一数据库可用性失败: {str(e)}")
            self.use_unified_db = False
            self.is_fallback_mode = True
            return False
    
    def _init_databases(self):
        """初始化数据库"""
        if self.use_unified_db:
            self._init_unified_mode()
        else:
            self._init_fallback_mode()
    
    def _setup_logger(self) -> logging.Logger:
        """设置日志记录器"""
        logger = logging.getLogger(f"{__name__}.{self.module_name}")
        if not logger.handlers:
            handler = logging.StreamHandler()
            formatter = logging.Formatter(
                '[%(asctime)s] [%(name)s] [%(levelname)s] %(message)s'
            )
            handler.setFormatter(formatter)
            logger.addHandler(handler)
            logger.setLevel(logging.INFO)
        return logger
    
    def _init_unified_mode(self):
        """初始化统一数据库模式"""
        try:
            from config.unified_database_config import get_unified_config
            
            self.config = get_unified_config()
            
            logger.info("✅ 统一数据库模式初始化成功")
            
        except Exception as e:
            logger.warning(f"⚠️ 统一数据库模式初始化失败，切换到降级模式: {e}")
            self._init_fallback_mode()
    
    def _init_fallback_mode(self):
        """初始化降级模式（SQLite直连）"""
        logger.info("✅ 降级模式初始化完成")
    
    def _create_fallback_database(self):
        """创建降级数据库和表结构"""
        try:
            with sqlite3.connect(str(self.fallback_db_path)) as conn:
                conn.row_factory = sqlite3.Row
                
                # 设置SQLite优化参数
                conn.execute('PRAGMA foreign_keys = ON')
                conn.execute('PRAGMA journal_mode = WAL')
                conn.execute('PRAGMA synchronous = NORMAL')
                conn.execute('PRAGMA cache_size = -64000')  # 64MB缓存
                conn.execute('PRAGMA temp_store = MEMORY')
                
                # 创建所有表
                for table_name, schema in self.TABLE_SCHEMAS.items():
                    conn.execute(schema)
                    logger.debug(f"创建表: {table_name}")
                
                conn.commit()
                logger.info(f"降级数据库创建完成: {self.fallback_db_path}")
                
        except Exception as e:
            logger.error(f"创建降级数据库失败: {str(e)}")
            raise
    
    @contextmanager
    def get_connection(self, db_type="main"):
        """获取数据库连接（上下文管理器）"""
        conn = None
        try:
            # 获取数据库路径
            db_path = self._get_db_path_for_type(db_type)
            
            # 创建连接
            conn = sqlite3.connect(
                str(db_path),
                timeout=30.0,
                check_same_thread=False
            )
            conn.row_factory = sqlite3.Row
            
            # 设置SQLite优化参数
            conn.execute('PRAGMA foreign_keys = ON')
            conn.execute('PRAGMA journal_mode = WAL')
            conn.execute('PRAGMA cache_size = -16384')  # 16MB cache
            
            yield conn
            
        except Exception as e:
            logger.error(f"数据库连接失败: {e}")
            if conn:
                conn.rollback()
            raise
        finally:
            if conn:
                conn.close()
    
    def _get_db_path_for_type(self, db_type: str) -> Path:
        """根据数据库类型获取对应的数据库路径"""
        if db_type in ["main", "medication", "health_records", "mobile"]:
            if self.is_fallback_mode:
                return self.fallback_db_path
            else:
                return self.mobile_db_path
        elif db_type == "backend":
            return self.backend_db_path
        return self.fallback_db_path
    
    def ensure_tables_exist(self):
        """
        确保数据库表存在并执行迁移（优化版本）
        """
        # 检查是否已经执行过表创建
        if hasattr(self, '_tables_ensured') and self._tables_ensured:
            logger.debug("数据库表已确保存在，跳过重复检查")
            return
            
        try:
            with self.get_connection() as conn:
                # 批量创建表，减少连接开销
                tables_created = []
                for table_name, schema in self.TABLE_SCHEMAS.items():
                    try:
                        conn.execute(schema)
                        tables_created.append(table_name)
                    except Exception as e:
                        logger.warning(f"创建表 {table_name} 失败: {e}")
                
                logger.info(f"批量创建/检查了 {len(tables_created)} 个表")
                
                # 执行迁移
                self._perform_migrations(conn)
                
                # 额外保障：确保关键字段存在
                self._ensure_critical_columns(conn)
                
                # 标记表已确保存在
                self._tables_ensured = True
                
        except Exception as e:
            logger.error(f"确保表存在失败: {e}")
            raise
    
    def _ensure_critical_columns(self, conn):
        """
        确保关键字段存在
        
        Args:
            conn: 数据库连接
        """
        try:
            # 确保 documents.documentrecord_id 存在
            cursor = conn.execute("PRAGMA table_info(documents)")
            cols = {row[1] for row in cursor.fetchall()}
            if 'documentrecord_id' not in cols:
                logger.info("为 documents 表添加缺失列 documentrecord_id")
                conn.execute("ALTER TABLE documents ADD COLUMN documentrecord_id INTEGER")

            # 确保 sync_record 表有 status 字段
            cursor = conn.execute("PRAGMA table_info(sync_record)")
            sync_cols = {row[1] for row in cursor.fetchall()}
            if 'status' not in sync_cols:
                logger.info("为 sync_record 表添加缺失列 status")
                conn.execute("ALTER TABLE sync_record ADD COLUMN status TEXT DEFAULT 'pending'")
                
        except Exception as e:
            logger.warning(f"检查或添加关键字段失败: {e}")
    
    def _perform_migrations(self, conn):
        """
        执行数据库迁移
        
        Args:
            conn: 数据库连接
        """
        try:
            # 额外保障：确保 users 表有 status 字段
            try:
                cursor = conn.execute("PRAGMA table_info(users)")
                users_cols = {row[1] for row in cursor.fetchall()}
                if 'status' not in users_cols:
                    logger.info("为 users 表添加缺失列 status")
                    conn.execute("ALTER TABLE users ADD COLUMN status TEXT DEFAULT 'active'")
            except Exception as e:
                logger.warning(f"检查或添加 users.status 失败: {e}")
            
            # 创建索引
            for index_sql in self.ADDITIONAL_INDEXES:
                try:
                    conn.execute(index_sql)
                except Exception:
                    # 索引可能已存在，忽略错误
                    continue
                    
            conn.commit()
            logger.info("数据库表检查和迁移完成")
            
        except Exception as e:
            logger.error(f"创建缺失表失败: {str(e)}")
            raise
    
    def _perform_migrations_optimized(self, conn):
        """
        执行数据库迁移（优化版：通用字段检查和添加）
        """
        try:
            logger.info("🔄 开始执行数据库迁移...")
            migration_start_time = time.time()
            total_fields_added = 0
            
            for table_name, expected_fields in self.EXPECTED_FIELDS.items():
                fields_added = self._migrate_table_fields(conn, table_name, expected_fields)
                total_fields_added += fields_added
            
            conn.commit()
            
            migration_duration = time.time() - migration_start_time
            logger.info(f"🎉 数据库迁移完成! 添加 {total_fields_added} 个字段，耗时 {migration_duration:.2f} 秒")
            
        except Exception as e:
            logger.error(f"❌ 数据库迁移失败: {str(e)}")
            conn.rollback()
    
    def _migrate_table_fields(self, conn, table_name: str, expected_fields: list) -> int:
        """
        为单个表执行字段迁移
        
        Args:
            conn: 数据库连接
            table_name: 表名
            expected_fields: 期望的字段列表
            
        Returns:
            int: 添加的字段数量
        """
        # 获取现有字段
        cursor = conn.execute(f"PRAGMA table_info({table_name})")
        existing_columns = {row[1] for row in cursor.fetchall()}
        
        # 共同字段总是存在（由schema确保），只检查特定字段
        missing_fields = [field for field in expected_fields if field not in existing_columns]
        
        if not missing_fields:
            logger.debug(f"✅ {table_name}表字段完整")
            return 0
            
        logger.info(f"📋 {table_name}表缺失字段: {missing_fields}")
        fields_added = 0
        
        for field in missing_fields:
            default_type = self._get_field_default_type(field)
            logger.info(f"➕ 为{table_name}表添加{field}字段 ({default_type})")
            conn.execute(f"ALTER TABLE {table_name} ADD COLUMN {field} {default_type}")
            fields_added += 1
            
        return fields_added
    
    def _get_field_default_type(self, field_name: str) -> str:
        """
        根据字段名推断默认类型和值
        
        Args:
            field_name: 字段名
            
        Returns:
            str: 字段类型定义
        """
        if 'is_' in field_name or '_enabled' in field_name:
            return 'INTEGER DEFAULT 0'
        elif '_at' in field_name or '_date' in field_name:
            return 'TEXT'
        elif '_id' in field_name and field_name != 'custom_id':
            return 'INTEGER'
        else:
            return 'TEXT'
    
    def _convert_boolean_fields(self, table_name: str, data: Dict[str, Any]) -> Dict[str, Any]:
        """
        转换布尔字段为Python布尔值格式
        处理数据库中存储的字符串'1'/'0'转换为True/False
        """
        # 定义需要转换的布尔字段
        boolean_fields = [
            'is_important', 'reminder_enabled', 'prescription_required', 
            'is_active', 'is_abnormal', 'ocr_processed', 'review_reminder'
        ]
        
        converted_data = data.copy()
        
        for field in boolean_fields:
            if field in converted_data and converted_data[field] is not None:
                converted_data[field] = self._convert_single_boolean_value(
                    converted_data[field], table_name, field
                )
        
        return converted_data
    
    def _convert_single_boolean_value(self, value, table_name: str, field_name: str):
        """
        转换单个布尔值
        
        Args:
            value: 要转换的值
            table_name: 表名（用于日志）
            field_name: 字段名（用于日志）
            
        Returns:
            转换后的布尔值或原值
        """
        try:
            # 处理字符串类型的布尔值
            if isinstance(value, str):
                return self._parse_string_boolean(value)
            elif isinstance(value, (int, float)):
                # 数字类型转换
                return bool(value)
            # 如果已经是布尔值，则保持不变
            return value
        except (ValueError, TypeError) as e:
            logger.warning(f"布尔值转换失败 {table_name}.{field_name}: {value}, 错误: {str(e)}")
            return value
    
    def _parse_string_boolean(self, value: str) -> bool:
        """
        解析字符串布尔值
        
        Args:
            value: 字符串值
            
        Returns:
            bool: 解析后的布尔值
        """
        # 处理 '1'/'0' 或 'true'/'false' 等情况
        if value.lower() in ['1', 'true', 'yes', 'on']:
            return True
        elif value.lower() in ['0', 'false', 'no', 'off', '']:
            return False
        else:
            # 尝试转换为整数再判断
            return bool(int(value))

    def _convert_datetime_fields(self, table_name: str, data: Dict[str, Any]) -> Dict[str, Any]:
        """
        转换日期时间字段为SQLite兼容格式
        根据数据库表定义，某些表使用DATETIME类型（需要datetime对象），某些表使用TEXT类型（需要字符串）
        """
        datetime_fields = [
            'created_at', 'updated_at', 'start_date', 'end_date', 'stop_date', 'visit_date',
            'test_date', 'report_date', 'exam_date', 'admission_date', 'discharge_date',
            'surgery_date', 'taken_at', 'completed_at', 'last_reminded_at', 'acknowledged_at',
            'resolved_at', 'password_reset_at', 'last_login', 'birth_date', 'date_recorded',
            'follow_up_date', 'completion_date', 'measurement_time', 'last_sync_time',
            'bedtime', 'wake_time', 'sync_time', 'record_time'
        ]  # 统一所有可能日期字段
        
        # 使用DATETIME类型的表（需要datetime对象）
        # 注意：根据TABLE_SCHEMAS定义，大部分表实际使用TEXT类型存储日期时间
        # 只有真正使用DATETIME类型的表才需要datetime对象
        datetime_type_tables = {
            'wearable_devices', 'heart_rate_data', 'blood_pressure_data', 'sleep_data',
            'activity_data', 'sync_logs'
            # 移除了users, health_records, medical_records, surgeries, medications, 
            # questionnaires, lab_reports, follow_ups, health_diary_entries, 
            # user_settings, assessments 因为它们在TABLE_SCHEMAS中使用TEXT类型
        }
        
        converted_data = data.copy()
        use_datetime_objects = table_name in datetime_type_tables
        
        for field in datetime_fields:
            if field in converted_data and converted_data[field] is not None:
                converted_data[field] = self._convert_single_datetime_field(
                    converted_data[field], field, use_datetime_objects
                )
        
        return converted_data

    def _convert_single_datetime_field(self, value: Any, field_name: str, use_datetime_objects: bool) -> Any:
        """
        转换单个日期时间字段
        
        Args:
            value: 要转换的值
            field_name: 字段名称（用于日志）
            use_datetime_objects: 是否转换为datetime对象
            
        Returns:
            转换后的值
        """
        try:
            if use_datetime_objects:
                return self._convert_to_datetime_object(value)
            else:
                return self._convert_to_datetime_string(value)
        except Exception as e:
            logger.warning(f"日期转换失败 {field_name}: {e}")
            return datetime.now() if use_datetime_objects else now_iso()
    
    def _convert_to_datetime_object(self, value: Any) -> datetime:
        """
        转换值为datetime对象（用于DATETIME类型字段）
        
        Args:
            value: 要转换的值
            
        Returns:
            datetime对象
        """
        if isinstance(value, str):
            if value.strip():
                parsed_dt = DateTimeUtils.parse_datetime(value)
                return parsed_dt if parsed_dt else datetime.now()
            else:
                return datetime.now()
        elif isinstance(value, datetime):
            return value
        else:
            parsed_dt = DateTimeUtils.parse_datetime(value)
            return parsed_dt if parsed_dt else datetime.now()
    
    def _convert_to_datetime_string(self, value: Any) -> str:
        """
        转换值为字符串（用于TEXT类型字段）
        
        Args:
            value: 要转换的值
            
        Returns:
            ISO格式的日期时间字符串
        """
        if isinstance(value, str):
            if value.strip():
                return value.strip()
            else:
                return now_iso()
        elif isinstance(value, datetime):
            return value.isoformat()
        else:
            iso_string = DateTimeUtils.to_iso_datetime(value)
            return iso_string if iso_string else now_iso()
    
    def execute_query(self, query: str, params: Optional[Tuple] = None, 
                      db_type: str = "main", fetch: str = "none") -> Any:
        """
        执行SQL查询
        """
        try:
            with self.get_connection(db_type) as conn:
                cursor = conn.cursor()
                
                if params:
                    if not isinstance(params, tuple):
                        params = tuple(params)
                    cursor.execute(query, params)
                else:
                    cursor.execute(query)
                
                if fetch == "one":
                    result = cursor.fetchone()
                    return dict(result) if result else None
                elif fetch == "all":
                    return [dict(row) for row in cursor.fetchall()]
                else:
                    conn.commit()
                    return cursor.rowcount
                    
        except Exception as e:
            logger.error(f"执行查询失败: {query}, 参数: {params}, 错误: {e}")
            raise
    
    def execute_script(self, script: str, db_type: str = "main") -> bool:
        """
        执行SQL脚本
        """
        try:
            with self.get_connection(db_type) as conn:
                conn.executescript(script)
                conn.commit()
            return True
                
        except Exception as e:
            logger.error(f"执行脚本失败: {script}, 错误: {e}")
            return False
    
    def get_records(self, table: str, where_clause: str = "", 
                    params: Optional[Tuple] = None, db_type: str = "main") -> List[Dict[str, Any]]:
        query = f"SELECT * FROM {table}"
        if where_clause:
            query += f" WHERE {where_clause}"
        query += " ORDER BY created_at DESC"
        return self.execute_query(query, params, db_type, "all")
    
    def get_record_by_id(self, table: str, record_id: int, db_type: str = "main") -> Optional[Dict[str, Any]]:
        query = f"SELECT * FROM {table} WHERE id = ?"
        return self.execute_query(query, (record_id,), db_type, "one")
    
    def insert_record(self, table: str, data: Dict[str, Any], db_type: str = "main") -> int:
        """插入记录，包含数据转换和特殊处理逻辑"""
        # 数据预处理
        converted_data = self._prepare_insert_data(table, data)
        
        with self.get_connection(db_type) as conn:
            cursor = conn.cursor()
            cursor.execute("PRAGMA foreign_keys = OFF")
            
            # 处理users表的特殊逻辑
            if table == 'users':
                existing_user_id = self._handle_users_table_insert(cursor, converted_data, conn)
                if existing_user_id:
                    return existing_user_id
            
            # 执行标准插入操作
            return self._execute_insert_operation(cursor, table, converted_data, conn)
    
    def _prepare_insert_data(self, table: str, data: Dict[str, Any]) -> Dict[str, Any]:
        """预处理插入数据，包含数据转换和users表特殊处理"""
        # 先转换布尔值字段，再转换日期时间字段
        converted_data = self._convert_boolean_fields(table, data)
        converted_data = self._convert_datetime_fields(table, converted_data)
        
        # 对users表特殊处理：确保hashed_password不为空
        if table == 'users' and ('hashed_password' in converted_data and 
                                (converted_data.get('hashed_password') is None or 
                                 converted_data.get('hashed_password') == '')):
            converted_data['hashed_password'] = 'default_password_hash'
            
        return converted_data
    
    def _handle_users_table_insert(self, cursor, converted_data: Dict[str, Any], conn) -> Optional[int]:
        """处理users表插入时的特殊逻辑，返回用户ID或None"""
        if 'custom_id' not in converted_data:
            return None
            
        try:
            cursor.execute("SELECT id FROM users WHERE custom_id = ?", (converted_data['custom_id'],))
            existing_row = cursor.fetchone()
            if existing_row:
                user_id = existing_row[0]
                return self._update_existing_user(cursor, converted_data, user_id, conn)
        except Exception as e:
            logger.warning(f"检查用户记录时出错: {e}")
            conn.rollback()
        return None
    
    def _update_existing_user(self, cursor, converted_data: Dict[str, Any], user_id: int, conn) -> int:
        """更新现有用户记录"""
        update_fields = {k: v for k, v in converted_data.items() if k != 'id'}
        if update_fields:
            set_clause = ', '.join([f"{k} = ?" for k in update_fields.keys()])
            update_values = list(update_fields.values()) + [user_id]
            cursor.execute(f"UPDATE users SET {set_clause} WHERE id = ?", tuple(update_values))
            conn.commit()
            logger.info(f"用户记录已更新而非插入 users#{user_id}, custom_id: {converted_data['custom_id']}")
        return user_id
    
    def _execute_insert_operation(self, cursor, table: str, converted_data: Dict[str, Any], conn) -> int:
        """执行标准插入操作，包含错误处理"""
        columns = list(converted_data.keys())
        placeholders = ["?" for _ in columns]
        values = list(converted_data.values())
        query = f"INSERT INTO {table} ({', '.join(columns)}) VALUES ({', '.join(placeholders)})"

        try:
            cursor.execute(query, values)
            conn.commit()
            return cursor.lastrowid if cursor.lastrowid is not None else 0
        except sqlite3.IntegrityError as ie:
            return self._handle_integrity_error(cursor, table, converted_data, conn, ie)
    
    def _handle_integrity_error(self, cursor, table: str, converted_data: Dict[str, Any], 
                               conn, ie: sqlite3.IntegrityError) -> int:
        """处理完整性约束错误"""
        err_msg = str(ie)
        logger.warning(f"插入记录时遇到完整性错误，尝试回退处理: {err_msg}")
        
        # 处理UNIQUE约束失败 - 支持所有表
        if 'UNIQUE constraint failed' in err_msg:
            return self._handle_unique_constraint_error_generic(cursor, table, converted_data, conn, err_msg)
        
        # 处理NOT NULL约束失败
        if 'NOT NULL constraint failed' in err_msg:
            return self._handle_not_null_constraint_error(cursor, table, converted_data, conn, err_msg)
        
        raise ie
    
    def _handle_unique_constraint_error_generic(self, cursor, table: str, converted_data: Dict[str, Any], 
                                              conn, err_msg: str) -> int:
        """处理通用UNIQUE约束失败 - 支持所有表的INSERT OR REPLACE逻辑"""
        try:
            # 解析错误信息，获取冲突的字段
            if 'custom_id' in err_msg or 'custom_id' in converted_data:
                # 使用custom_id作为唯一标识符
                unique_field = 'custom_id'
                unique_value = converted_data.get('custom_id')
            elif 'id' in converted_data:
                # 使用id作为唯一标识符
                unique_field = 'id'
                unique_value = converted_data.get('id')
            else:
                # 尝试从错误信息中解析字段名
                if '.' in err_msg:
                    field_part = err_msg.split('.')[-1].strip()
                    unique_field = field_part
                    unique_value = converted_data.get(field_part)
                else:
                    logger.error(f"无法解析UNIQUE约束冲突字段: {err_msg}")
                    raise sqlite3.IntegrityError(err_msg)
            
            if not unique_value:
                logger.error(f"缺少唯一字段值: {unique_field}")
                raise sqlite3.IntegrityError(err_msg)
            
            # 查找现有记录
            cursor.execute(f"SELECT id FROM {table} WHERE {unique_field} = ?", (unique_value,))
            row = cursor.fetchone()
            
            if row:
                existing_id = row[0]
                logger.info(f"发现重复记录，执行更新操作: {table}.{unique_field}={unique_value}, id={existing_id}")
                
                # 构建更新语句
                update_fields = []
                update_values = []
                
                for key, value in converted_data.items():
                    if key != unique_field and key != 'id':  # 不更新唯一字段和id
                        update_fields.append(f"{key} = ?")
                        update_values.append(value)
                
                if update_fields:
                    # 添加updated_at字段
                    update_fields.append("updated_at = ?")
                    update_values.append(self._get_updated_at_value(table))
                    update_values.append(existing_id)
                    
                    update_query = f"UPDATE {table} SET {', '.join(update_fields)} WHERE id = ?"
                    cursor.execute(update_query, update_values)
                    conn.commit()
                    logger.info(f"成功更新记录: {table}#{existing_id}")
                
                return existing_id
            else:
                logger.error(f"UNIQUE约束冲突但未找到现有记录: {table}.{unique_field}={unique_value}")
                raise sqlite3.IntegrityError(err_msg)
                
        except Exception as e:
            logger.error(f"处理UNIQUE约束失败时出错: {e}")
            conn.rollback()
            raise
        
        return 0
    
    def _handle_not_null_constraint_error(self, cursor, table: str, converted_data: Dict[str, Any], 
                                         conn, err_msg: str) -> int:
        """处理NOT NULL约束失败"""
        try:
            missing = err_msg.split(':')[-1].strip()
            defaults = {
                'users.hashed_password': 'default_password_hash',
                'users.password_hash': 'default_password_hash',
                'users.username': converted_data.get('username', f"user_{int(time.time())}"),
                'users.email': converted_data.get('email', f"user_{int(time.time())}@example.com")
            }
            
            if missing in defaults:
                key = missing.split('.')[-1]
                converted_data[key] = defaults[missing]
                columns = list(converted_data.keys())
                placeholders = ["?" for _ in columns]
                values = [converted_data[k] for k in columns]
                retry_query = f"INSERT INTO {table} ({', '.join(columns)}) VALUES ({', '.join(placeholders)})"
                cursor.execute(retry_query, tuple(values))
                conn.commit()
                return cursor.lastrowid if cursor.lastrowid is not None else 0
        except Exception as e:
            logger.warning(f"回退填充并重试插入失败: {e}")
        return 0
    
    def update_record(self, table: str, record_id: int, data: Dict[str, Any], 
                      db_type: str = "main") -> int:
        """更新记录，根据表类型使用正确的日期时间格式"""
        # 数据预处理
        converted_data = self._prepare_update_data(table, data)
        
        # 构建更新查询
        query, values = self._build_update_query(table, converted_data, record_id)
        
        # 执行查询
        result = self.execute_query(query, tuple(values), db_type)
        return result if result is not None else 0
    
    def _prepare_update_data(self, table: str, data: Dict[str, Any]) -> Dict[str, Any]:
        """预处理更新数据"""
        converted_data = self._convert_boolean_fields(table, data)
        return self._convert_datetime_fields(table, converted_data)
    
    def _build_update_query(self, table: str, converted_data: Dict[str, Any], record_id: int) -> tuple:
        """构建更新查询语句和参数"""
        set_clauses = [f"{key} = ?" for key in converted_data.keys()]
        values = list(converted_data.values())
        
        # 添加updated_at字段
        updated_at_value = self._get_updated_at_value(table)
        values.append(updated_at_value)
        values.append(record_id)
        
        query = f"UPDATE {table} SET {', '.join(set_clauses)}, updated_at = ? WHERE id = ?"
        return query, values
    
    def _get_updated_at_value(self, table: str):
        """根据表类型获取updated_at字段的值"""
        datetime_type_tables = {
            'wearable_devices', 'heart_rate_data', 'blood_pressure_data', 'sleep_data',
            'activity_data', 'sync_logs'
        }
        
        if table in datetime_type_tables:
            return datetime.now()  # DATETIME类型表使用datetime对象
        else:
            return now_iso()  # TEXT类型表使用ISO字符串
    
    def _get_foreign_key_column(self, table: str) -> str:
        """获取表的外键字段名"""
        user_custom_id_tables = {
            'health_records', 'medical_records', 'lab_reports', 'questionnaires', 
            'basic_health_info', 'basic_health_info_list'
        }
        
        return 'user_custom_id' if table in user_custom_id_tables else 'custom_id'
    
    def delete_record(self, table: str, record_id: int, db_type: str = "main") -> int:
        """删除记录，对users表特殊处理外键约束"""
        if table == 'users':
            # 对于users表，需要特殊处理外键约束
            with self.get_connection(db_type) as conn:
                cursor = conn.cursor()
                try:
                    # 暂时禁用外键约束以避免删除时的约束冲突
                    cursor.execute("PRAGMA foreign_keys = OFF")
                    
                    # 先检查是否存在该记录
                    cursor.execute("SELECT custom_id FROM users WHERE id = ?", (record_id,))
                    user_record = cursor.fetchone()
                    if not user_record:
                        logger.warning(f"要删除的用户记录不存在 users#{record_id}")
                        return 0
                    
                    custom_id = user_record[0]
                    
                    # 检查表是否存在的辅助函数
                    def table_exists(table_name):
                        cursor.execute("SELECT name FROM sqlite_master WHERE type='table' AND name=?", (table_name,))
                        return cursor.fetchone() is not None
                    
                    # 检查列是否存在的辅助函数
                    def column_exists(table_name, column_name):
                        if not table_exists(table_name):
                            return False
                        cursor.execute(f"PRAGMA table_info({table_name})")
                        columns = [row[1] for row in cursor.fetchall()]
                        return column_name in columns
                    
                    # 手动删除相关记录（因为当前表结构可能没有正确的CASCADE设置）
                    related_tables = [
                        'health_records', 'medical_records', 'medications',
                        'questionnaires', 'assessments', 'lab_reports', 'follow_ups',
                        'documents', 'basic_health_info', 'basic_health_info_list'
                    ]
                    
                    for related_table in related_tables:
                        try:
                            # 检查表是否存在
                            if not table_exists(related_table):
                                logger.debug(f"表 {related_table} 不存在，跳过删除")
                                continue
                            
                            # 根据表的外键字段删除相关记录
                            column_name = self._get_foreign_key_column(related_table)
                            if column_exists(related_table, column_name):
                                cursor.execute(f"DELETE FROM {related_table} WHERE {column_name} = ?", (custom_id,))
                                logger.debug(f"删除表 {related_table} 中 {column_name}={custom_id} 的记录")
                            else:
                                logger.debug(f"表 {related_table} 不存在 {column_name} 列，跳过删除")
                        except sqlite3.Error as e:
                            logger.warning(f"删除相关表 {related_table} 记录时出错: {e}")
                    
                    # 最后删除用户记录
                    cursor.execute("DELETE FROM users WHERE id = ?", (record_id,))
                    affected_rows = cursor.rowcount
                    conn.commit()
                    
                    if affected_rows > 0:
                        logger.info(f"成功删除用户记录 users#{record_id} 及其相关数据")
                    
                    return affected_rows
                    
                except sqlite3.Error as e:
                    conn.rollback()
                    logger.error(f"删除用户记录失败 users#{record_id}: {e}")
                    raise
        else:
            # 对于其他表，使用原有逻辑
            query = f"DELETE FROM {table} WHERE id = ?"
            return self.execute_query(query, (record_id,), db_type)
    
    def add_to_sync_queue(self, table_name: str, record_id: int, action: str, data: Optional[Dict] = None):
        sync_data = {
            'table_name': table_name,
            'record_id': record_id,
            'action': action,
            'data': json.dumps(data) if data else None,
            'created_at': datetime.now().isoformat()
        }
        self.insert_record('sync_queue', sync_data)
    
    def get_pending_syncs(self, limit: Optional[int] = None) -> List[Dict[str, Any]]:
        condition = "operation_state = 'pending'"
        order_by = "timestamp ASC"
        query = f"SELECT * FROM sync_record WHERE {condition} ORDER BY {order_by}"
        if limit:
            query += f" LIMIT {limit}"
        return self.execute_query(query, fetch="all")
    
    def select(self, table: str, condition: Optional[Dict[str, Any]] = None, 
               order_by: Optional[str] = None, limit: Optional[int] = None, 
               db_type: str = "main") -> List[Dict[str, Any]]:
        query = f"SELECT * FROM {table}"
        params = []
        if condition:
            where_clauses = [f"{k} = ?" for k in condition]
            params = list(condition.values())
            query += f" WHERE {' AND '.join(where_clauses)}"
        if order_by:
            query += f" ORDER BY {order_by}"
        if limit:
            query += f" LIMIT {limit}"
        return self.execute_query(query, tuple(params), db_type, "all")
    
    def update(self, table: str, update_data: Dict[str, Any], 
               condition: Dict[str, Any], db_type: str = "main") -> int:
        set_clauses = [f"{k} = ?" for k in update_data]
        where_clauses = [f"{k} = ?" for k in condition]
        query = f"UPDATE {table} SET {', '.join(set_clauses)}, updated_at = ? WHERE {' AND '.join(where_clauses)}"
        params = list(update_data.values()) + [now_iso()] + list(condition.values())
        return self.execute_query(query, tuple(params), db_type)
    
    def is_unified_mode(self) -> bool:
        return self.use_unified_db and not self.is_fallback_mode
    
    def connect(self, user_id: str) -> bool:
        try:
            if self.is_fallback_mode:
                logger.info(f"降级模式下连接用户数据库: {user_id}")
            else:
                logger.info(f"统一模式下连接用户数据库: {user_id}")
            return True
        except Exception as e:
            logger.error(f"连接用户数据库失败: {e}")
            self._init_fallback_mode()
            return True
    
    def disconnect(self):
        with self._lock:
            for conn in self._connections.values():
                conn.close()
            self._connections.clear()
        logger.info("数据库连接已断开")
    
    def get_database_info(self, db_type: str = "mobile") -> Dict[str, Any]:
        db_path = self._get_db_path_for_type(db_type)
        info = {'database_path': str(db_path), 'database_size': 0, 'table_count': 0, 'tables': []}
        if db_path.exists():
            info['database_size'] = db_path.stat().st_size
            with self.get_connection(db_type) as conn:
                cursor = conn.execute("SELECT name FROM sqlite_master WHERE type='table'")
                tables = [row[0] for row in cursor.fetchall()]
                info['table_count'] = len(tables)
                for table in tables:
                    count = conn.execute(f"SELECT COUNT(*) FROM {table}").fetchone()[0]
                    info['tables'].append({'name': table, 'record_count': count})
        return info
    
    def get_mode_info(self) -> Dict[str, Any]:
        return {
            'mode': 'unified' if self.is_unified_mode() else 'fallback',
            'unified_available': self.use_unified_db,
            'fallback_active': self.is_fallback_mode,
            'db_paths': {k: str(v) for k, v in self.DATABASE_PATHS.items()},
            'module_name': self.module_name
        }

# 全局管理器实例缓存
_managers: Dict[str, UnifiedDatabaseManager] = {}
_managers_lock = threading.Lock()
# 数据库路径配置日志标志，避免重复输出
_db_paths_logged = False
_db_paths_lock = threading.Lock()

def get_unified_database_manager(module_name: str = "default") -> UnifiedDatabaseManager:
    with _managers_lock:
        if module_name not in _managers:
            _managers[module_name] = UnifiedDatabaseManager(module_name)
        return _managers[module_name]

def create_database_manager(module_name: str) -> UnifiedDatabaseManager:
    return UnifiedDatabaseManager(module_name)

get_fallback_database_manager = get_unified_database_manager
get_fallback_manager = get_unified_database_manager

def is_in_fallback_mode(module_name: str = "default") -> bool:
    manager = get_unified_database_manager(module_name)
    return manager.is_fallback_mode

if __name__ == "__main__":
    # 测试代码
    print("=== 统一数据库管理器测试 ===")
    
    manager = get_unified_database_manager("test")
    
    mode_info = manager.get_mode_info()
    print(f"当前模式: {mode_info['mode']}")
    
    try:
        manager.ensure_tables_exist()
        print("✅ 数据库表创建成功")
        
        test_data = {
            'custom_id': 'test_med_001',
            'name': '测试药物',
            'dosage': '10mg',
            'frequency': '每日一次',
            'status': 'active',
            'created_at': now_iso(),
            'updated_at': now_iso()
        }
        
        record_id = manager.insert_record('medications', test_data)
        print(f"✅ 插入测试记录成功，ID: {record_id}")
        
        record = manager.get_record_by_id('medications', record_id)
        if record is not None:
            print(f"✅ 查询记录成功: {record['name']}")
        else:
            print("❌ 查询记录失败: 记录不存在")
        
        manager.delete_record('medications', record_id)
        print("✅ 删除测试记录成功")
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
    
    print("测试完成")