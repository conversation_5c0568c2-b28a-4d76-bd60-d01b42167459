from datetime import datetime
from typing import Dict, Any, Optional
from datetime import datetime

from fastapi import APIRouter, Depends, HTTPException, Path, Query, status
from sqlalchemy.orm import Session

from app.db.base_session import get_db
from app.models.user import User
from app.models.assessment import AssessmentResponse, Assessment
from app.api import deps
from app.core.auth import get_current_active_user_custom

router = APIRouter()

@router.get("/{response_id}", response_model=Dict[str, Any])
def get_assessment_response(
    *,
    db: Session = Depends(get_db),
    response_id: int = Path(..., description="回答ID"),
    current_user: User = Depends(deps.get_current_active_user_custom)
) -> Any:
    """
    获取指定评估回答的详细信息
    """
    # 查找回答记录
    response = db.query(AssessmentResponse).filter(
        AssessmentResponse.id == response_id
    ).first()
    
    if not response:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail=f"未找到回答记录ID: {response_id}"
        )
    
    # 权限校验 - 根据实际表结构使用user_id
    if hasattr(response, 'custom_id'):
        user_identifier = response.custom_id
    else:
        # 如果使用user_id，需要通过用户表查找custom_id
        from app.models.user import User
        user = db.query(User).filter(User.id == response.user_id).first()
        user_identifier = user.custom_id if user else None
    
    if current_user.custom_id != user_identifier and current_user.role not in ["admin", "super_admin", "unit_admin", "consultant"]:
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="没有权限访问此回答记录"
        )
    
    # 获取评估信息
    assessment = db.query(Assessment).filter(
        Assessment.id == response.assessment_id
    ).first()
    
    # 获取问题列表
    questions = []
    if assessment:
        if assessment.template and assessment.template.questions:
            # 从模板获取问题
            questions = [{
                "question_id": q.question_id,
                "question_text": q.question_text,
                "question_type": q.question_type,
                "options": q.options,
                "order": q.order,
                "is_required": q.is_required,
                "scoring": q.scoring,
                "dimension_key": q.dimension_key
            } for q in assessment.template.questions]
        elif assessment.items:
            # 从评估项目获取问题（备用方案）
            questions = [{
                "question_id": item.question_id,
                "question_text": item.question_text,
                "question_type": item.question_type,
                "options": item.options,
                "order": item.order_num,
                "is_required": item.is_required,
                "answer": item.answer,
                "score": item.score
            } for item in assessment.items]
    
    return {
        "status": "success",
        "data": {
            "id": response.id,
            "assessment_id": response.assessment_id,
            "custom_id": user_identifier,
            "answers": response.answers,
            "score": response.score,
            "result": response.result,
            "created_at": response.created_at.isoformat() if response.created_at else None,
            "updated_at": response.updated_at.isoformat() if response.updated_at else None,
            "assessment": {
                "id": assessment.id if assessment else None,
                "name": assessment.title if assessment else None,
                "description": assessment.notes if assessment else None,
                "questions": questions,
                "assessment_type": assessment.assessment_type if assessment else None,
                "version": assessment.version if assessment else None
            }
        }
    }

@router.get("/user/{custom_id}", response_model=Dict[str, Any])
def get_user_assessment_responses(
    *,
    db: Session = Depends(get_db),
    custom_id: str = Path(..., description="用户ID"),
    assessment_type: Optional[str] = Query(None, description="评估类型"),
    start_date: Optional[datetime] = Query(None, description="开始日期"),
    end_date: Optional[datetime] = Query(None, description="结束日期"),
    skip: int = Query(0, ge=0, description="跳过记录数"),
    limit: int = Query(100, ge=1, le=1000, description="返回记录数"),
    current_user: User = Depends(deps.get_current_active_user_custom)
) -> Any:
    """
    获取指定用户的评估回答列表
    """
    # 查找用户
    user = db.query(User).filter(User.custom_id == custom_id).first()
    if not user:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail=f"未找到用户ID: {custom_id}"
        )

    # 权限校验
    if current_user.custom_id != custom_id and current_user.role not in ["admin", "super_admin", "unit_admin", "consultant"]:
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="没有权限访问此用户的评估回答"
        )

    # 构建查询，join assessment表获取评估信息
    # 根据实际表结构使用正确的字段
    query = db.query(AssessmentResponse, Assessment).join(
        Assessment, AssessmentResponse.assessment_id == Assessment.id
    )
    
    # 根据表结构决定使用哪个字段进行过滤
    if hasattr(AssessmentResponse, 'custom_id'):
        query = query.filter(AssessmentResponse.custom_id == custom_id)
    else:
        # 如果使用user_id，需要通过用户ID过滤
        query = query.filter(AssessmentResponse.user_id == user.id)

    # 应用过滤条件
    if assessment_type:
        query = query.filter(Assessment.assessment_type == assessment_type)
    if start_date:
        query = query.filter(AssessmentResponse.created_at >= start_date)
    if end_date:
        query = query.filter(AssessmentResponse.created_at <= end_date)

    # 获取总数
    total = query.count()

    # 应用分页并获取结果
    responses = query.order_by(AssessmentResponse.created_at.desc()).offset(skip).limit(limit).all()

    # 格式化返回数据
    result_data = []
    for response, assessment in responses:
        # 确保正确返回用户标识符
        response_custom_id = custom_id  # 使用传入的custom_id参数
        
        result_data.append({
            "id": response.id,
            "assessment_id": response.assessment_id,
            "custom_id": response_custom_id,
            "answers": response.answers,
            "score": response.score,
            "result": response.result,
            "created_at": response.created_at.isoformat() if response.created_at else None,
            "updated_at": response.updated_at.isoformat() if response.updated_at else None,
            "assessment": {
                "id": assessment.id,
                "title": assessment.title,
                "name": assessment.title,
                "description": assessment.notes,
                "notes": assessment.notes,
                "assessment_type": assessment.assessment_type,
                "version": assessment.version
            }
        })

    return {
        "status": "success",
        "data": result_data,
        "total": total,
        "skip": skip,
        "limit": limit
    }

@router.delete("/{response_id}", response_model=Dict[str, Any])
def delete_assessment_response(
    *,
    db: Session = Depends(get_db),
    response_id: int = Path(..., description="回答ID"),
    current_user: User = Depends(deps.get_current_active_user_custom)
) -> Any:
    """
    删除指定的评估回答记录
    """
    # 查找回答记录
    response = db.query(AssessmentResponse).filter(
        AssessmentResponse.id == response_id
    ).first()
    
    if not response:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail=f"未找到回答记录ID: {response_id}"
        )
    
    # 权限校验 - 根据实际表结构使用user_id
    if hasattr(response, 'custom_id'):
        user_identifier = response.custom_id
    else:
        # 如果使用user_id，需要通过用户表查找custom_id
        from app.models.user import User
        user = db.query(User).filter(User.id == response.user_id).first()
        user_identifier = user.custom_id if user else None
    
    if current_user.custom_id != user_identifier and current_user.role not in ["admin", "super_admin", "unit_admin", "consultant"]:
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="没有权限删除此回答记录"
        )
    
    # 删除记录
    db.delete(response)
    db.commit()
    
    return {
        "status": "success",
        "message": f"评估回答记录 {response_id} 已成功删除"
    }