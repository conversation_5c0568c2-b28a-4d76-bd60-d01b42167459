"""基本健康信息屏幕模块

提供用户全面健康信息的录入、编辑和管理功能，包括：
- 基本信息：身高、体重、BMI等
- 生活方式：饮食、运动、睡眠、烟酒习惯（多选形式）
- 医疗历史：慢性病史、手术史、住院史、传染病史（列表管理）
- 家族遗传病史：直系亲属重大疾病史（列表管理）
- 过敏记录：药物/食物/环境过敏源（列表管理）
- 预防接种：疫苗接种记录（列表管理）
"""

import typing
from kivy.logger import Logger as KivyLogger
from kivymd.app import MDApp
from kivy.metrics import dp
from kivy.properties import StringProperty, ObjectProperty, BooleanProperty, ListProperty, DictProperty
from screens.base_screen import BaseScreen
from kivy.clock import Clock
from kivy.lang import Builder
from kivy.core.window import Window
import os
import json
import logging
from datetime import datetime
# 使用统一数据库管理器替代旧的数据库管理器
from utils.unified_database_manager_optimized import get_unified_database_manager

# 常量定义
ERROR_NO_CUSTOM_ID = "无法获取用户custom_id"
WARNING_NO_CUSTOM_ID_USE_DEFAULT = "无法获取用户custom_id，使用默认数据"

from kivymd.uix.boxlayout import MDBoxLayout
from kivymd.uix.button import MDIconButton, MDButtonText, MDButton
from kivymd.uix.label import MDLabel
from kivymd.uix.card import MDCard
from kivymd.uix.scrollview import MDScrollView
from kivymd.uix.dialog import MDDialog, MDDialogHeadlineText, MDDialogContentContainer, MDDialogButtonContainer
from kivymd.uix.divider import MDDivider
from kivy.uix.widget import Widget
from kivymd.uix.textfield import MDTextField, MDTextFieldHintText, MDTextFieldHelperText
from kivymd.uix.selectioncontrol import MDCheckbox
from kivymd.uix.menu import MDDropdownMenu
from kivymd.uix.list import MDList, MDListItem
from kivymd.uix.snackbar import MDSnackbar, MDSnackbarText
from widgets.common_kv_components import load_common_kv_components

# 导入主题和字体样式
try:
    from mobile.theme import AppTheme, AppMetrics, FontStyles
except ImportError:
    # 如果无法从mobile包导入，则尝试直接导入
    try:
        from theme import AppTheme, AppMetrics, FontStyles
    except ImportError:
        # 如果两个都失败，抛出异常而不是创建模拟类
        raise ImportError("无法导入主题配置，请检查theme.py文件是否存在且可访问")

# 设置日志
logger = logging.getLogger(__name__)

# 定义组件KV语言字符串
KV = '''
<HealthCategoryCard>:
    orientation: 'vertical'
    size_hint_y: None
    height: self.minimum_height
    md_bg_color: [0.95, 0.95, 0.95, 1]  # 直接使用颜色值
    radius: [dp(12)]
    padding: [dp(16), dp(12), dp(16), dp(12)]
    spacing: dp(8)
    elevation: 2

    # 标题栏
    MDBoxLayout:
        orientation: 'horizontal'
        size_hint_y: None
        height: dp(40)
        spacing: dp(12)

        MDIcon:
            icon: root.icon
            theme_icon_color: "Custom"
            icon_color: root.icon_color
            font_size: dp(24)
            size_hint_x: None
            width: dp(24)
            pos_hint: {"center_y": 0.5}

        MDLabel:
            text: root.title
            font_style: "Body"
            role: "large"
            bold: True
            theme_text_color: "Primary"
            pos_hint: {"center_y": 0.5}

        Widget:
            size_hint_x: 0.1

        MDIconButton:
            icon: "chevron-down" if root.expanded else "chevron-right"
            font_size: dp(20)
            theme_icon_color: "Custom"
            icon_color: [0.6, 0.6, 0.6, 1]  # 直接使用颜色值
            size_hint_x: None
            width: dp(40)
            on_release: root.toggle_expand()

    # 分割线
    MDDivider:
        height: dp(1)
        color: [0.8, 0.8, 0.8, 1]  # 直接使用颜色值

    # 内容区域
    MDBoxLayout:
        id: content_layout
        orientation: 'vertical'
        size_hint_y: None
        height: self.minimum_height if root.expanded else 0
        opacity: 1 if root.expanded else 0
        spacing: dp(8)

<MultiSelectItem>:
    orientation: 'horizontal'
    size_hint_y: None
    height: dp(48)
    spacing: dp(12)
    padding: [dp(8), dp(4), dp(8), dp(4)]

    MDCheckbox:
        id: checkbox
        size_hint_x: None
        width: dp(32)
        active: root.selected
        on_active: root.on_checkbox_active(self.active)

    MDLabel:
        text: root.text
        font_style: "Body"
        role: "medium"
        theme_text_color: "Primary"
        pos_hint: {"center_y": 0.5}

<ListManagementItem>:
    orientation: 'vertical'
    size_hint_y: None
    height: self.minimum_height
    md_bg_color: [0.98, 0.98, 0.98, 1]  # 直接使用颜色值
    radius: [dp(8)]
    padding: [dp(12), dp(8), dp(12), dp(8)]
    spacing: dp(8)

    MDBoxLayout:
        orientation: 'horizontal'
        size_hint_y: None
        height: dp(32)
        spacing: dp(8)

        MDLabel:
            text: root.title
            font_style: "Body"
            role: "medium"
            bold: True
            theme_text_color: "Primary"
            size_hint_x: 0.8
            pos_hint: {"center_y": 0.5}

        MDIconButton:
            icon: "pencil"
            font_size: dp(18)
            theme_icon_color: "Custom"
            icon_color: [0.133, 0.46, 0.82, 1]  # 直接使用颜色值
            size_hint_x: None
            width: dp(32)
            on_release: root.on_edit()

        MDIconButton:
            icon: "delete"
            font_size: dp(18)
            theme_icon_color: "Custom"
            icon_color: [0.8, 0.2, 0.2, 1]  # 直接使用颜色值
            size_hint_x: None
            width: dp(32)
            on_release: root.on_delete()

    MDLabel:
        text: root.content
        font_style: "Label"
        theme_text_color: "Secondary"
        size_hint_y: None
        height: self.texture_size[1]
        text_size: self.width, None

<HealthInfoItem>:
    orientation: 'horizontal'
    size_hint_y: None
    height: dp(48)
    spacing: dp(12)
    padding: [dp(8), dp(4), dp(8), dp(4)]

    MDLabel:
        text: root.label
        font_style: "Body"
        role: "medium"
        theme_text_color: "Primary"
        size_hint_x: 0.4
        pos_hint: {"center_y": 0.5}

    MDLabel:
        text: root.value if root.value else "未填写"
        font_style: "Body"
        role: "medium"
        theme_text_color: "Custom"
        text_color: [0.133, 0.46, 0.82, 1] if root.value else [0.45, 0.45, 0.45, 1]  # 直接使用颜色值
        size_hint_x: 0.5
        pos_hint: {"center_y": 0.5}

    MDIconButton:
        icon: "pencil"
        font_size: dp(18)
        theme_icon_color: "Custom"
        icon_color: [0.133, 0.46, 0.82, 1]  # 直接使用颜色值
        size_hint_x: None
        width: dp(40)
        on_release: root.on_edit()
'''

# 只加载一次KV，确保ids绑定唯一
Builder.load_string(KV)

class MultiSelectItem(MDBoxLayout):
    """多选项组件"""
    text = StringProperty("")
    selected = BooleanProperty(False)
    callback = ObjectProperty(None)

    def on_checkbox_active(self, active):
        """复选框状态改变"""
        self.selected = active
        if self.callback:
            self.callback(self.text, active)

class ListManagementItem(MDCard):
    """列表管理项组件"""
    title = StringProperty("")
    content = StringProperty("")
    item_data = DictProperty({})
    edit_callback = ObjectProperty(None)
    delete_callback = ObjectProperty(None)

    def on_edit(self):
        """编辑项目"""
        if self.edit_callback:
            self.edit_callback(self.item_data)

    def on_delete(self):
        """删除项目"""
        if self.delete_callback:
            self.delete_callback(self.item_data)

class HealthCategoryCard(MDCard):
    """健康分类卡片组件"""
    title = StringProperty("")
    icon = StringProperty("")
    icon_color = ListProperty([1, 1, 1, 1])
    expanded = BooleanProperty(True)
    category_data = DictProperty({})
    screen_ref = ObjectProperty(None)
    _content_loaded = BooleanProperty(False)  # 添加为类属性

    # 生活方式数据结构常量
    LIFESTYLE_SECTIONS = {
        'diet': {
            'title': '饮食习惯（可多选）',
            'options': ['肉食为主', '素食为主', '荤素均衡', '喜腌制食品', '喜食粥粉面等易消化食物', '喜热粥热茶', '喜食辛辣', '高盐饮食']
        },
        'exercise': {
            'title': '运动情况',
            'fields': {
                'exercise_type': {'label': '运动方式', 'options': ['跑步', '游泳', '健身', '瑜伽', '太极', '广场舞', '散步', '骑行', '球类运动', '其他']},
                'exercise_amount': {'label': '运动量', 'options': ['轻度', '中度', '重度']},
                'exercise_frequency': {'label': '运动频率', 'options': ['每天', '每周5-6次', '每周3-4次', '每周1-2次', '偶尔', '从不']}
            }
        },
        'sleep': {
            'title': '睡眠情况（可多选）',
            'options': ['主观感觉良好', '主观感觉一般', '主观感觉较差', '入眠困难', '易醒', '早醒', '多梦', '打鼾']
        },
        'smoking': {
            'title': '吸烟情况',
            'fields': {
                'smoking_years': {'label': '吸烟时长（年）', 'type': 'number'},
                'smoking_amount': {'label': '吸烟量（包/天）', 'type': 'number'},
                'quit_smoking': {'label': '有无戒烟', 'options': ['从未吸烟', '已戒烟', '正在戒烟', '未戒烟']},
                'quit_time': {'label': '戒烟时间', 'type': 'text'}
            }
        },
        'drinking': {
            'title': '饮酒情况',
            'fields': {
                'drinking_amount': {'label': '饮酒量', 'options': ['从不饮酒', '少量（<25g/天）', '中量（25-50g/天）', '大量（>50g/天）']},
                'drinking_frequency': {'label': '饮酒频率', 'options': ['从不', '偶尔', '每周1-2次', '每周3-4次', '每天']},
                'quit_drinking': {'label': '戒酒情况', 'options': ['从未饮酒', '已戒酒', '正在戒酒', '未戒酒']}
            }
        },
        'bowel': {
            'title': '大便情况（可多选）',
            'options': ['正常', '便秘', '腹泻', '便血', '大便不成形', '排便困难', '排便疼痛']
        },
        'urination': {
            'title': '小便情况（可多选）',
            'options': ['正常', '尿频', '尿急', '尿痛', '排尿困难', '血尿', '夜尿增多（>2次/夜）', '尿失禁']
        },
        'work_stress': {
            'title': '工作压力',
            'options': ['很低', '较低', '中等', '较高', '很高']
        }
    }

    def __init__(self, **kwargs):
        super().__init__(**kwargs)
        # 延迟初始化，确保KV模板完全加载
        Clock.schedule_once(self._delayed_init, 0.1)
    
    def _delayed_init(self, dt):
        """延迟初始化，确保KV模板已完全加载"""
        if self.category_data and not self._content_loaded:
            self._content_loaded = True
            self.load_items()
    
    def on_category_data(self, instance, value):
        """当category_data属性变化时，延迟加载内容"""
        if value and not self._content_loaded:
            # 使用延迟加载，确保ids已初始化
            Clock.schedule_once(lambda dt: self._load_content_safely(), 0.1)
    
    def _load_content_safely(self):
        """安全地加载内容，确保ids已初始化"""
        if not self._content_loaded and self.category_data:
            self._content_loaded = True
            self.load_items()

    def toggle_expand(self):
        """切换展开/收起状态"""
        self.expanded = not self.expanded

    def load_items(self, dt=0):
        """加载分类下的信息项"""
        try:
            # 安全检查：确保ids已经初始化
            if not hasattr(self, 'ids') or not self.ids:
                logger.warning("HealthCategoryCard的ids尚未初始化，延迟加载")
                Clock.schedule_once(self.load_items, 0.1)
                return
                
            # 安全检查：确保content_layout存在
            if 'content_layout' not in self.ids:
                logger.warning("content_layout不存在于ids中")
                return
                
            content_layout = self.ids.content_layout
            content_layout.clear_widgets()

            # 检查category_data是否为字典类型
            if not isinstance(self.category_data, dict):
                logger.warning(f"category_data不是字典类型: {type(self.category_data)} - {self.category_data}")
                return

            if not self.category_data or 'items' not in self.category_data:
                return

            category_key = self.category_data.get('key', '')

            # 根据不同类型加载不同的UI
            if category_key == 'lifestyle':
                self.load_lifestyle_items(content_layout)
            elif category_key in ['medical_history', 'family_history', 'allergies', 'vaccinations']:
                self.load_list_management_items(content_layout)
            else:
                self.load_normal_items(content_layout)
                
        except Exception as e:
            logger.error(f"加载分类项时出错: {e}")
            # 添加错误提示
            try:
                # 再次安全检查
                if hasattr(self, 'ids') and self.ids and 'content_layout' in self.ids:
                    content_layout = self.ids.content_layout
                    content_layout.clear_widgets()
                    error_label = MDLabel(
                        text=f"加载数据时出错: {str(e)}",
                        theme_text_color="Custom",
                        text_color=[1, 0.3, 0.3, 1],  # 直接使用颜色值而不是theme
                        size_hint_y=None,
                        height=dp(40)
                    )
                    content_layout.add_widget(error_label)
                else:
                    logger.warning("无法添加错误提示：ids未初始化")
            except Exception as inner_e:
                logger.error(f"添加错误提示时出错: {inner_e}")

    def load_normal_items(self, content_layout):
        """加载普通信息项"""
        try:
            items = self.category_data.get('items', {})
            
            # 确保items是字典类型
            if not isinstance(items, dict):
                logger.warning(f"items不是字典类型: {type(items)} - {items}")
                return
                
            for item_key, item_data in items.items():
                # 确保item_data是字典类型
                if not isinstance(item_data, dict):
                    logger.warning(f"跳过非字典类型的项目数据: {item_key} - {type(item_data)}")
                    continue
                    
                item = HealthInfoItem(
                    label=item_data.get('label', ''),
                    value=item_data.get('value', ''),
                    item_key=item_key,
                    category_key=self.category_data.get('key', ''),
                    item_data=item_data
                )
                content_layout.add_widget(item)
                
        except Exception as e:
            logger.error(f"加载普通信息项时出错: {e}")
            # 添加错误提示
            error_label = MDLabel(
                text=f"加载数据时出错: {str(e)}",
                theme_text_color="Custom",
                text_color=[0.8, 0.2, 0.2, 1],  # 直接使用颜色值
                size_hint_y=None,
                height=dp(48)
            )
            content_layout.add_widget(error_label)

    def load_lifestyle_items(self, content_layout):
        """加载生活方式多选项"""
        for section_key, section_data in self.LIFESTYLE_SECTIONS.items():
            self._add_lifestyle_section(content_layout, section_key, section_data)

    def _add_lifestyle_section(self, content_layout, section_key, section_data):
        """添加生活方式小节"""
        # 添加小节标题
        self._add_section_title(content_layout, section_data['title'])
        
        # 根据数据类型添加相应的UI组件
        if 'options' in section_data:
            self._add_multi_select_options(content_layout, section_key, section_data['options'])
        elif 'fields' in section_data:
            self._add_field_items(content_layout, section_key, section_data['fields'])
        
        # 添加分隔线
        content_layout.add_widget(MDBoxLayout(size_hint_y=None, height=dp(8)))

    def _add_section_title(self, content_layout, title):
        """添加小节标题"""
        title_label = MDLabel(
            text=title,
            font_style="Body",
            role="medium",
            bold=True,
            theme_text_color="Primary",
            size_hint_y=None,
            height=dp(32)
        )
        content_layout.add_widget(title_label)

    def _add_multi_select_options(self, content_layout, section_key, options):
        """添加多选项"""
        for option in options:
            selected = option in self.get_lifestyle_selections(section_key)
            item = MultiSelectItem(
                text=option,
                selected=selected,
                callback=lambda text, active, sk=section_key: self.on_lifestyle_selection(sk, text, active)
            )
            content_layout.add_widget(item)

    def _add_field_items(self, content_layout, section_key, fields):
        """添加字段项"""
        for field_key, field_data in fields.items():
            current_value = self.get_lifestyle_field_value(section_key, field_key)
            item_data = self._create_item_data(field_data)
            
            item = HealthInfoItem(
                label=field_data['label'],
                value=current_value,
                item_key=f"{section_key}_{field_key}",
                category_key='lifestyle',
                item_data=item_data
            )
            content_layout.add_widget(item)

    def _create_item_data(self, field_data):
        """创建项目数据"""
        item_data = {'type': 'select' if 'options' in field_data else field_data.get('type', 'text')}
        if 'options' in field_data:
            item_data['options'] = field_data['options']
        return item_data

    def load_list_management_items(self, content_layout):
        """加载列表管理项"""
        try:
            category_key = self.category_data.get('key', '')
            items = self.get_list_items(category_key)

            # 添加新增按钮
            add_button = MDButton(
                MDButtonText(text=f"添加{self.category_data['title']}"),
                style="outlined",
                size_hint_y=None,
                height=dp(40),
                on_release=lambda x: self.add_list_item(category_key)
            )
            content_layout.add_widget(add_button)

            # 添加现有项目
            for item_data in items:
                # 确保item_data是字典类型
                if not isinstance(item_data, dict):
                    logger.warning(f"跳过非字典类型的项目数据: {type(item_data)} - {item_data}")
                    continue
                    
                list_item = ListManagementItem(
                    title=item_data.get('title', ''),
                    content=item_data.get('content', ''),
                    item_data=item_data,
                    edit_callback=lambda data, ck=category_key: self.edit_list_item(ck, data),
                    delete_callback=lambda data, ck=category_key: self.delete_list_item(ck, data)
                )
                content_layout.add_widget(list_item)
                
        except Exception as e:
            logger.error(f"加载列表管理项时出错: {e}")
            # 添加错误提示
            error_label = MDLabel(
                text=f"加载数据时出错: {str(e)}",
                theme_text_color="Error",
                size_hint_y=None,
                height=dp(40)
            )
            content_layout.add_widget(error_label)

    def get_lifestyle_selections(self, section_key):
        """获取生活方式选择"""
        if not self.screen_ref:
            return []
        return self.screen_ref.lifestyle_selections.get(section_key, [])

    def get_lifestyle_field_value(self, section_key, field_key):
        """获取生活方式字段值"""
        if not self.screen_ref:
            return ''
        return self.screen_ref.lifestyle_fields.get(f"{section_key}_{field_key}", '')

    def on_lifestyle_selection(self, section_key, text, active):
        """生活方式选择回调"""
        if self.screen_ref:
            self.screen_ref.update_lifestyle_selection(section_key, text, active)

    def get_list_items(self, category_key):
        """获取列表项"""
        if not self.screen_ref:
            return []
        return self.screen_ref.list_data.get(category_key, [])

    def add_list_item(self, category_key):
        """添加列表项"""
        if self.screen_ref:
            self.screen_ref.add_list_item(category_key)

    def edit_list_item(self, category_key, item_data):
        """编辑列表项"""
        if self.screen_ref:
            self.screen_ref.edit_list_item(category_key, item_data)

    def delete_list_item(self, category_key, item_data):
        """删除列表项"""
        if self.screen_ref:
            self.screen_ref.delete_list_item(category_key, item_data)

class HealthInfoItem(MDBoxLayout):
    """健康信息项组件"""
    label = StringProperty("")
    value = StringProperty("")
    item_key = StringProperty("")
    category_key = StringProperty("")
    item_data = DictProperty({})

    def on_edit(self):
        """编辑项目"""
        try:
            # 获取屏幕引用
            screen = None
            parent = self.parent
            while parent:
                if hasattr(parent, 'screen_ref') and parent.screen_ref:
                    screen = parent.screen_ref
                    break
                parent = parent.parent
            
            if screen:
                screen.edit_health_item(self.category_key, self.item_key, self.label, self.value, self.item_data)
        except Exception as e:
            logger.error(f"编辑健康信息项时出错: {e}")

class BasicHealthInfoScreen(BaseScreen):
    """基本健康信息屏幕"""

    def __init__(self, **kwargs):
        # 设置导航栏属性
        kwargs['screen_title'] = '基本健康信息'
        kwargs['show_top_bar'] = True  # 显示顶端导航栏
        kwargs['top_bar_action_icon'] = 'refresh'
        
        # 初始化数据结构
        self.health_data = {}
        self.lifestyle_selections = {}
        self.lifestyle_fields = {}
        self.list_data = {}
        self.current_dialog = None
        
        # 调用父类初始化
        super().__init__(**kwargs)
        self.app = MDApp.get_running_app()
        
        # 初始化健康数据结构
        self.init_health_data_structure()

    def on_action(self):
        """顶部栏动作按钮回调"""
        self.save_all_data()

    def on_back(self):
        """返回按钮回调"""
        self.go_back()

    def do_content_setup(self):
        """设置页面内容 - 继承自BaseScreen的核心方法
        防重入与重复加载保护：
        - _building_content 防止并发/重复进入
        - _content_setup_done 防止刷新前多次构建
        """
        if getattr(self, '_building_content', False):
            logger.info("检测到内容正在构建中，跳过本次do_content_setup调用")
            return
        try:
            # 设置构建中标记，防止并发重复
            self._building_content = True

            # 获取内容容器，使用BaseScreen提供的标准方式
            content_container = self.ids.get('content_container')
            if not content_container:
                logger.error("内容容器不存在，无法设置页面内容")
                return

            # 检查是否已经设置过内容，避免重复添加 - 与健康日记保持一致
            if hasattr(self, '_content_setup_done') and self._content_setup_done:
                logger.info("页面内容已设置，跳过重复设置")
                return

            # 先标记已设置，避免同一帧内的重复进入
            self._content_setup_done = True

            # 清空现有内容
            content_container.clear_widgets()

            # 创建主布局 - 与健康日记UI管理器的模块容器保持一致的结构
            main_layout = MDBoxLayout(
                orientation='vertical',
                size_hint_y=None,
                spacing=dp(16),
                padding=[dp(0), dp(8), dp(0), dp(16)]
            )
            main_layout.bind(minimum_height=main_layout.setter('height'))

            # 加载数据
            self.load_data_from_db()

            # 创建健康信息分类卡片
            for category_key, category_data in self.health_data.items():
                if not isinstance(category_data, dict):
                    continue

                # 获取主题颜色 - 使用直接颜色值避免super()错误
                icon_color = [0.133, 0.46, 0.82, 1]  # 主题蓝色

                card = HealthCategoryCard(
                    title=category_data.get('title', ''),
                    icon=category_data.get('icon', 'information'),
                    icon_color=icon_color,
                    category_data=category_data,
                    screen_ref=self,
                    size_hint_y=None
                )
                card.bind(minimum_height=card.setter('height'))
                main_layout.add_widget(card)

            # 将主布局添加到内容容器 - 与健康日记UI管理器保持一致
            content_container.add_widget(main_layout)

            logger.info("基本健康信息页面内容设置完成")

        except Exception as e:
            logger.error(f"设置页面内容时出错: {e}")
            # 添加错误提示
            try:
                cc = content_container if 'content_container' in locals() and content_container else self.ids.get('content_container')
                if cc:
                    cc.clear_widgets()
                    error_label = MDLabel(
                        text=f"页面加载失败: {str(e)}",
                        theme_text_color="Custom",
                        text_color=[0.8, 0.2, 0.2, 1],  # 直接使用红色
                        halign="center",
                        size_hint_y=None,
                        height=dp(60)
                    )
                    cc.add_widget(error_label)
            except Exception:
                pass
        finally:
            # 结束构建标记
            self._building_content = False

    def init_ui(self, dt=0) -> None:
        """初始化UI - 延迟调用"""
        try:
            # 调用父类的初始化
            super().init_ui()
            logger.info("基本健康信息屏幕UI初始化完成")
        except Exception as e:
            logger.error(f"初始化UI时出错: {e}")

    def init_health_data_structure(self):
        """初始化健康数据结构"""
        self.health_data = {
            'basic_info': {
                'key': 'basic_info',
                'title': '基本信息',
                'icon': 'account',
                'items': {
                    'height': {'label': '身高(cm)', 'value': '', 'type': 'number'},
                    'weight': {'label': '体重(kg)', 'value': '', 'type': 'number'},
                    'bmi': {'label': 'BMI', 'value': '', 'type': 'readonly'},
                    'blood_type': {'label': '血型', 'value': '', 'type': 'select', 'options': ['A型', 'B型', 'AB型', 'O型', '未知']},
                    'rh_factor': {'label': 'Rh因子', 'value': '', 'type': 'select', 'options': ['阳性', '阴性', '未知']},
                    'marital_status': {'label': '婚姻状况', 'value': '', 'type': 'select', 'options': ['未婚', '已婚', '离异', '丧偶']},
                    'education': {'label': '教育程度', 'value': '', 'type': 'select', 'options': ['小学', '初中', '高中', '大专', '本科', '硕士', '博士']},
                    'occupation': {'label': '职业', 'value': '', 'type': 'text'},
                    'emergency_contact': {'label': '紧急联系人', 'value': '', 'type': 'text'},
                    'emergency_phone': {'label': '紧急联系电话', 'value': '', 'type': 'phone'}
                }
            },
            'lifestyle': {
                'key': 'lifestyle',
                'title': '生活方式',
                'icon': 'heart',
                'items': {}
            },
            'medical_history': {
                'key': 'medical_history',
                'title': '医疗历史',
                'icon': 'medical-bag',
                'items': {}
            },
            'family_history': {
                'key': 'family_history',
                'title': '家族病史',
                'icon': 'account-group',
                'items': {}
            },
            'allergies': {
                'key': 'allergies',
                'title': '过敏记录',
                'icon': 'alert',
                'items': {}
            },
            'vaccinations': {
                'key': 'vaccinations',
                'title': '预防接种',
                'icon': 'needle',
                'items': {}
            }
        }

        # 初始化生活方式选择和字段
        self.lifestyle_selections = {
            'diet': [],
            'sleep': [],
            'bowel': [],
            'urination': [],
            'work_stress': []
        }

        self.lifestyle_fields = {}

        # 初始化列表数据
        self._initialize_list_data()

    def on_enter(self, *args):
        """页面进入时的回调"""
        try:
            super().on_enter(*args)
            logger.info("进入基本健康信息页面")
        except Exception as e:
            logger.error(f"进入页面时出错: {e}")

    def refresh_ui(self, dt=0):
        """刷新UI界面"""
        try:
            logger.info("刷新基本健康信息UI")
            # 重置内容设置标志，允许重新设置内容
            if hasattr(self, '_content_setup_done'):
                self._content_setup_done = False
            # 重新加载数据
            self.load_data_from_db()
            # 重新设置内容
            self.do_content_setup()
        except Exception as e:
            logger.error(f"刷新UI时出错: {e}")

    def go_back(self):
        """返回上一页"""
        try:
            app = MDApp.get_running_app()
            if hasattr(app, 'screen_manager') and app.screen_manager:
                # 返回健康资料管理页面，而不是主页
                app.screen_manager.current = 'health_data_management_screen'
                logger.info("已返回健康资料管理页面")
            else:
                logger.warning("无法获取屏幕管理器")
        except Exception as e:
            logger.error(f"返回上一页时出错: {e}")

    def save_all_data(self):
        """保存所有数据"""
        try:
            # 计算BMI
            self.calculate_bmi()
            
            # 保存各类数据
            self._save_basic_info()
            self._save_lifestyle_data()
            self._save_list_data()
            
            self.show_message("数据保存成功")
            logger.info("所有健康数据保存成功")
            
        except Exception as e:
            logger.error(f"保存数据时出错: {e}")
            self.show_message(f"保存失败: {str(e)}")

    def _save_basic_info(self):
        """保存基本信息数据"""
        for category_key, category_data in self.health_data.items():
            if category_key == 'basic_info' and 'items' in category_data:
                for item_key, item_data in category_data['items'].items():
                    if isinstance(item_data, dict) and 'value' in item_data:
                        self.save_health_item_to_db(
                            category_key, 
                            item_key, 
                            item_data['value'], 
                            item_data.get('type', 'text')
                        )

    def _save_lifestyle_data(self):
        """保存生活方式数据"""
        # 保存多选项数据
        for section_key, selections in self.lifestyle_selections.items():
            if selections:
                self.save_health_item_to_db(
                    'lifestyle', 
                    section_key, 
                    ','.join(selections), 
                    'multiselect'
                )
        
        # 保存字段数据
        for field_key, field_value in self.lifestyle_fields.items():
            if field_value:
                self.save_health_item_to_db(
                    'lifestyle', 
                    field_key, 
                    field_value, 
                    'text'
                )

    def _save_list_data(self):
        """保存列表数据"""
        try:
            for category_key, items in self.list_data.items():
                for item_data in items:
                    if isinstance(item_data, dict):
                        self.save_list_item_to_db(category_key, item_data)
                
                self.show_message("数据保存成功")
                logger.info("所有健康数据保存成功")
                
        except Exception as e:
            logger.error(f"保存数据时出错: {e}")
            self.show_message(f"保存失败: {str(e)}")

    def calculate_bmi(self):
        """计算BMI"""
        try:
            height_str = self.health_data['basic_info']['items']['height']['value']
            weight_str = self.health_data['basic_info']['items']['weight']['value']
            
            if height_str and weight_str:
                height = float(height_str) / 100  # 转换为米
                weight = float(weight_str)
                
                if height > 0:
                    bmi = weight / (height * height)
                    self.health_data['basic_info']['items']['bmi']['value'] = f"{bmi:.1f}"
                    
                    # 保存BMI到数据库
                    self.save_health_item_to_db('basic_info', 'bmi', f"{bmi:.1f}", 'readonly')
                    
        except (ValueError, KeyError) as e:
            logger.warning(f"计算BMI时出错: {e}")

    def edit_health_item(self, category_key, item_key, label, value, item_data):
        """编辑健康信息项"""
        try:
            self.show_item_dialog(category_key, item_key, label, value, item_data)
        except Exception as e:
            logger.error(f"编辑健康信息项时出错: {e}")

    def show_item_dialog(self, category_key, item_key, label, value, item_data):
        """显示编辑对话框"""
        try:
            # 关闭现有对话框
            if self.current_dialog:
                self.current_dialog.dismiss()

            # 创建对话框内容容器
            content_container = MDDialogContentContainer(
                orientation="vertical",
                spacing=dp(16),
                size_hint_y=None,
                height=dp(200)
            )

            # 根据类型创建不同的输入控件
            input_widget = None
            item_type = item_data.get('type', 'text')

            if item_type == 'readonly':
                # 只读字段
                input_widget = MDLabel(
                    text=value,
                    theme_text_color="Primary",
                    size_hint_y=None,
                    height=dp(40)
                )
                content_container.add_widget(input_widget)
                
                # 创建只读对话框
                self.current_dialog = MDDialog(
                    MDDialogHeadlineText(text=label),
                    content_container,
                    MDDialogButtonContainer(
                        MDButton(
                            MDButtonText(text="确定"),
                            style="text",
                            on_release=lambda x: self.current_dialog.dismiss()
                        )
                    )
                )
                
            elif item_type == 'select':
                # 下拉选择
                options = item_data.get('options', [])
                
                # 创建选项按钮
                for option in options:
                    button = MDButton(
                        MDButtonText(text=option),
                        style="outlined" if option != value else "filled",
                        size_hint_y=None,
                        height=dp(40),
                        on_release=lambda x, opt=option: self.select_option(category_key, item_key, opt)
                    )
                    content_container.add_widget(button)
                
                # 创建选择对话框
                self.current_dialog = MDDialog(
                    MDDialogHeadlineText(text=f"选择{label}"),
                    content_container,
                    MDDialogButtonContainer(
                        MDButton(
                            MDButtonText(text="取消"),
                            style="text",
                            on_release=lambda x: self.current_dialog.dismiss()
                        )
                    )
                )
                
            else:
                # 文本输入
                input_widget = MDTextField(
                    MDTextFieldHintText(text=f"请输入{label}"),
                    text=value,
                    mode="outlined",
                    size_hint_y=None,
                    height=dp(56)
                )
                
                if item_type == 'number':
                    input_widget.input_filter = 'float'
                elif item_type == 'phone':
                    input_widget.input_filter = 'int'
                    input_widget.max_text_length = 11
                    
                content_container.add_widget(input_widget)
                
                # 创建输入对话框
                self.current_dialog = MDDialog(
                    MDDialogHeadlineText(text=f"编辑{label}"),
                    content_container,
                    MDDialogButtonContainer(
                        MDButton(
                            MDButtonText(text="取消"),
                            style="text",
                            on_release=lambda x: self.current_dialog.dismiss()
                        ),
                        MDButton(
                            MDButtonText(text="保存"),
                            style="filled",
                            on_release=lambda x: self.save_item_from_dialog(category_key, item_key, input_widget.text)
                        )
                    )
                )

            # 显示对话框
            if self.current_dialog:
                self.current_dialog.open()
                
        except Exception as e:
            logger.error(f"显示编辑对话框时出错: {e}")

    def select_option(self, category_key, item_key, option):
        """选择选项"""
        try:
            # 更新数据
            if category_key in self.health_data and 'items' in self.health_data[category_key]:
                if item_key in self.health_data[category_key]['items']:
                    self.health_data[category_key]['items'][item_key]['value'] = option
            
            # 保存到数据库
            self.save_health_item_to_db(category_key, item_key, option, 'select')
            
            # 关闭对话框
            if self.current_dialog:
                self.current_dialog.dismiss()
            
            # 刷新UI
            self.refresh_ui()
            
        except Exception as e:
            logger.error(f"选择选项时出错: {e}")

    def save_item_from_dialog(self, category_key, item_key, value):
        """从对话框保存项目"""
        try:
            # 更新数据
            if category_key in self.health_data and 'items' in self.health_data[category_key]:
                if item_key in self.health_data[category_key]['items']:
                    self.health_data[category_key]['items'][item_key]['value'] = value
            
            # 保存到数据库
            item_type = self.health_data[category_key]['items'][item_key].get('type', 'text')
            self.save_health_item_to_db(category_key, item_key, value, item_type)
            
            # 关闭对话框
            if self.current_dialog:
                self.current_dialog.dismiss()
            
            # 刷新UI
            self.refresh_ui()
            
        except Exception as e:
            logger.error(f"保存项目时出错: {e}")

    def update_lifestyle_selection(self, section_key, text, active):
        """更新生活方式选择"""
        try:
            if section_key not in self.lifestyle_selections:
                self.lifestyle_selections[section_key] = []
            
            if active:
                if text not in self.lifestyle_selections[section_key]:
                    self.lifestyle_selections[section_key].append(text)
            else:
                if text in self.lifestyle_selections[section_key]:
                    self.lifestyle_selections[section_key].remove(text)
            
            # 保存到数据库
            selections = self.lifestyle_selections[section_key]
            self.save_health_item_to_db('lifestyle', section_key, ','.join(selections), 'multiselect')
            
        except Exception as e:
            logger.error(f"更新生活方式选择时出错: {e}")

    def add_list_item(self, category_key):
        """添加列表项"""
        try:
            self.show_list_dialog(category_key)
        except Exception as e:
            logger.error(f"添加列表项时出错: {e}")

    def edit_list_item(self, category_key, item_data):
        """编辑列表项"""
        try:
            self.show_list_dialog(category_key, item_data)
        except Exception as e:
            logger.error(f"编辑列表项时出错: {e}")

    def show_list_dialog(self, category_key, existing_data=None):
        """显示列表编辑对话框"""
        try:
            # 关闭现有对话框
            if self.current_dialog:
                self.current_dialog.dismiss()

            # 获取字段配置
            fields_config = self.get_list_fields_config(category_key)
            
            # 创建对话框内容容器
            content_container = MDDialogContentContainer(
                orientation="vertical",
                spacing=dp(16),
                size_hint_y=None,
                height=dp(300)
            )

            # 创建输入字段
            input_widgets = {}
            for field_key, field_config in fields_config.items():
                field_widget = MDTextField(
                    MDTextFieldHintText(text=field_config['hint']),
                    text=existing_data.get(field_key, '') if existing_data else '',
                    mode="outlined",
                    size_hint_y=None,
                    height=dp(56)
                )
                
                if field_config.get('multiline', False):
                    field_widget.multiline = True
                    field_widget.height = dp(100)
                
                input_widgets[field_key] = field_widget
                content_container.add_widget(field_widget)

            # 创建对话框
            title = f"{'编辑' if existing_data else '添加'}{self.health_data[category_key]['title']}"
            self.current_dialog = MDDialog(
                MDDialogHeadlineText(text=title),
                content_container,
                MDDialogButtonContainer(
                    MDButton(
                        MDButtonText(text="取消"),
                        style="text",
                        on_release=lambda x: self.current_dialog.dismiss()
                    ),
                    MDButton(
                        MDButtonText(text="保存"),
                        style="filled",
                        on_release=lambda x: self.save_list_item(category_key, existing_data, input_widgets)
                    )
                )
            )

            # 显示对话框
            self.current_dialog.open()
            
        except Exception as e:
            logger.error(f"显示列表对话框时出错: {e}")

    def get_list_fields_config(self, category_key):
        """获取列表字段配置"""
        configs = {
            'medical_history': {
                'title': {'hint': '疾病名称', 'required': True},
                'diagnosis_date': {'hint': '诊断日期 (YYYY-MM-DD)', 'required': False},
                'hospital': {'hint': '医院名称', 'required': False},
                'doctor': {'hint': '医生姓名', 'required': False},
                'treatment': {'hint': '治疗方案', 'required': False, 'multiline': True},
                'status': {'hint': '当前状态', 'required': False},
                'notes': {'hint': '备注', 'required': False, 'multiline': True}
            },
            'family_history': {
                'title': {'hint': '疾病名称', 'required': True},
                'relationship': {'hint': '亲属关系', 'required': True},
                'age_onset': {'hint': '发病年龄', 'required': False},
                'status': {'hint': '当前状态', 'required': False},
                'notes': {'hint': '备注', 'required': False, 'multiline': True}
            },
            'allergies': {
                'title': {'hint': '过敏源名称', 'required': True},
                'type': {'hint': '过敏类型 (药物/食物/环境)', 'required': True},
                'symptoms': {'hint': '过敏症状', 'required': False, 'multiline': True},
                'severity': {'hint': '严重程度', 'required': False},
                'first_occurrence': {'hint': '首次发生时间', 'required': False},
                'notes': {'hint': '备注', 'required': False, 'multiline': True}
            },
            'vaccinations': {
                'title': {'hint': '疫苗名称', 'required': True},
                'date': {'hint': '接种日期 (YYYY-MM-DD)', 'required': True},
                'hospital': {'hint': '接种地点', 'required': False},
                'batch_number': {'hint': '批次号', 'required': False},
                'next_due': {'hint': '下次接种时间', 'required': False},
                'notes': {'hint': '备注', 'required': False, 'multiline': True}
            }
        }
        return configs.get(category_key, {})

    def save_list_item(self, category_key, existing_data, input_widgets):
        """保存列表项"""
        try:
            # 收集和验证数据
            item_data = self._collect_item_data(input_widgets)
            if not self._validate_required_fields(category_key, item_data):
                return
            
            # 设置内容字段
            if 'title' in item_data:
                item_data['content'] = self.format_list_item_content(category_key, item_data)
            
            # 更新列表数据
            self._update_list_data(category_key, existing_data, item_data)
            
            # 保存到数据库
            self.save_list_item_to_db(category_key, item_data)
            
            # 完成操作
            self._complete_save_operation()
            
        except Exception as e:
            logger.error(f"保存列表项时出错: {e}")
            self.show_message(f"保存失败: {str(e)}")

    def _collect_item_data(self, input_widgets):
        """收集输入数据"""
        item_data = {}
        for field_key, widget in input_widgets.items():
            item_data[field_key] = widget.text.strip()
        return item_data

    def _validate_required_fields(self, category_key, item_data):
        """验证必填字段"""
        fields_config = self.get_list_fields_config(category_key)
        for field_key, field_config in fields_config.items():
            if field_config.get('required', False) and not item_data.get(field_key):
                self.show_message(f"请填写{field_config['hint']}")
                return False
        return True

    def _update_list_data(self, category_key, existing_data, item_data):
        """更新列表数据"""
        if category_key not in self.list_data:
            self.list_data[category_key] = []
        
        if existing_data:
            # 编辑现有项
            for i, item in enumerate(self.list_data[category_key]):
                if item.get('id') == existing_data.get('id'):
                    item_data['id'] = existing_data['id']
                    self.list_data[category_key][i] = item_data
                    break
        else:
            # 添加新项
            import uuid
            item_data['id'] = str(uuid.uuid4())
            self.list_data[category_key].append(item_data)

    def _complete_save_operation(self):
        """完成保存操作"""
        # 关闭对话框
        if self.current_dialog:
            self.current_dialog.dismiss()
        
        # 刷新UI
        self.refresh_ui()
        
        self.show_message("保存成功")
            
    def format_list_item_content(self, category_key, item_data):
        """格式化列表项内容"""
        try:
            # 使用字典映射减少复杂度
            formatters = {
                'medical_history': self._format_medical_history,
                'family_history': self._format_family_history,
                'allergies': self._format_allergies,
                'vaccinations': self._format_vaccinations
            }
            
            formatter = formatters.get(category_key)
            if formatter:
                return formatter(item_data)
            
            return ""
            
        except Exception as e:
            logger.error(f"格式化列表项内容时出错: {e}")
            return ""

    def _format_medical_history(self, item_data):
        """格式化医疗历史数据"""
        parts = []
        if item_data.get('diagnosis_date'):
            parts.append(f"诊断时间: {item_data['diagnosis_date']}")
        if item_data.get('hospital'):
            parts.append(f"医院: {item_data['hospital']}")
        if item_data.get('status'):
            parts.append(f"状态: {item_data['status']}")
        return ' | '.join(parts)

    def _format_family_history(self, item_data):
        """格式化家族病史数据"""
        parts = []
        if item_data.get('relationship'):
            parts.append(f"关系: {item_data['relationship']}")
        if item_data.get('age_onset'):
            parts.append(f"发病年龄: {item_data['age_onset']}")
        if item_data.get('status'):
            parts.append(f"状态: {item_data['status']}")
        return ' | '.join(parts)

    def _format_allergies(self, item_data):
        """格式化过敏记录数据"""
        parts = []
        if item_data.get('type'):
            parts.append(f"类型: {item_data['type']}")
        if item_data.get('severity'):
            parts.append(f"严重程度: {item_data['severity']}")
        if item_data.get('symptoms'):
            parts.append(f"症状: {item_data['symptoms'][:20]}...")
        return ' | '.join(parts)

    def _format_vaccinations(self, item_data):
        """格式化疫苗接种数据"""
        parts = []
        if item_data.get('date'):
            parts.append(f"接种日期: {item_data['date']}")
        if item_data.get('hospital'):
            parts.append(f"地点: {item_data['hospital']}")
        if item_data.get('next_due'):
            parts.append(f"下次: {item_data['next_due']}")
        return ' | '.join(parts)

    def delete_list_item(self, category_key, item_data):
        """删除列表项"""
        try:
            # 从列表数据中删除
            if category_key in self.list_data:
                self.list_data[category_key] = [
                    item for item in self.list_data[category_key] 
                    if item.get('id') != item_data.get('id')
                ]
            
            # 从数据库删除
            self.delete_list_item_from_db(category_key, item_data)
            
            # 刷新UI
            self.refresh_ui()
            
            self.show_message("删除成功")
            
        except Exception as e:
            logger.error(f"删除列表项时出错: {e}")
            self.show_message(f"删除失败: {str(e)}")

    def save_list_item_to_db(self, category_key, item_data):
        """保存列表项到数据库"""
        try:
            # 获取用户custom_id
            custom_id = self._get_user_custom_id()
            if not custom_id:
                logger.error("无法获取有效的custom_id，无法保存数据")
                return
            
            # 验证custom_id是否存在于users表中
            if not self._validate_custom_id(custom_id):
                logger.error(f"custom_id {custom_id} 在users表中不存在，无法保存数据")
                return
            
            # 使用统一数据库管理器，使用basic_health_info_list表
            db_manager = get_unified_database_manager("basic_health_info")
            
            # 准备数据
            data_to_save = {
                'user_custom_id': custom_id,  # 使用user_custom_id字段
                'category': category_key,
                'item_key': item_data.get('id', ''),
                'item_data_str': json.dumps(item_data, ensure_ascii=False),  # 使用item_data_str字段
                'item_type': 'list',
                'created_at': datetime.now().isoformat(),
                'updated_at': datetime.now().isoformat()
            }
            
            # 使用统一数据库管理器的连接方式
            with db_manager.get_connection() as conn:
                cursor = conn.cursor()
                
                # 检查是否已存在 - 使用basic_health_info_list表和user_custom_id字段
                cursor.execute(
                    "SELECT id FROM basic_health_info_list WHERE user_custom_id = ? AND category = ? AND item_key = ?",
                    (custom_id, category_key, item_data.get('id', ''))
                )
                existing = cursor.fetchone()
                
                if existing:
                    # 更新现有记录
                    cursor.execute(
                        """UPDATE basic_health_info_list 
                           SET item_data_str = ?, updated_at = ? 
                           WHERE user_custom_id = ? AND category = ? AND item_key = ?""",
                        (data_to_save['item_data_str'], data_to_save['updated_at'], 
                         custom_id, category_key, item_data.get('id', ''))
                    )
                else:
                    # 插入新记录 - 使用basic_health_info_list表和正确的字段名
                    cursor.execute(
                        """INSERT INTO basic_health_info_list 
                           (user_custom_id, category, item_key, item_data_str, item_type, created_at, updated_at)
                           VALUES (?, ?, ?, ?, ?, ?, ?)""",
                        (data_to_save['user_custom_id'], data_to_save['category'], 
                         data_to_save['item_key'], data_to_save['item_data_str'], 
                         data_to_save['item_type'], data_to_save['created_at'], 
                         data_to_save['updated_at'])
                    )
                
                conn.commit()
            
            logger.info(f"列表项保存成功: {category_key} - {item_data.get('title', '')}")
            
        except Exception as e:
            logger.error(f"保存列表项到数据库时出错: {e}")
            raise

    def delete_list_item_from_db(self, category_key, item_data):
        """从数据库删除列表项"""
        try:
            # 获取用户custom_id
            custom_id = self._get_user_custom_id()
            if not custom_id:
                logger.error("无法获取有效的custom_id，无法删除数据")
                return
            
            # 验证custom_id是否存在于users表中
            if not self._validate_custom_id(custom_id):
                logger.error(f"custom_id {custom_id} 在users表中不存在，无法删除数据")
                return
            
            # 使用统一数据库管理器，使用basic_health_info_list表
            db_manager = get_unified_database_manager("basic_health_info")
            
            # 使用统一数据库管理器的连接方式
            with db_manager.get_connection() as conn:
                cursor = conn.cursor()
                
                # 删除记录 - 使用basic_health_info_list表和user_custom_id字段
                cursor.execute(
                    "DELETE FROM basic_health_info_list WHERE user_custom_id = ? AND category = ? AND item_key = ?",
                    (custom_id, category_key, item_data.get('id', ''))
                )
                
                conn.commit()
            
            logger.info(f"列表项删除成功: {category_key} - {item_data.get('title', '')}")
            
        except Exception as e:
            logger.error(f"从数据库删除列表项时出错: {e}")
            raise

    def save_health_item_to_db(self, category_key, item_key, item_value, item_type='text'):
        """保存健康信息项到数据库"""
        try:
            # 获取用户custom_id
            custom_id = self._get_user_custom_id()
            if not custom_id:
                logger.error("无法获取有效的custom_id，无法保存数据")
                return
            
            # 验证custom_id是否存在于users表中
            if not self._validate_custom_id(custom_id):
                logger.error(f"custom_id {custom_id} 在users表中不存在，无法保存数据")
                return
            
            # 使用统一数据库管理器，使用basic_health_info表而不是health_records表
            db_manager = get_unified_database_manager("basic_health_info")
            
            # 使用连接上下文管理器
            with db_manager.get_connection() as conn:
                cursor = conn.cursor()
                
                # 检查是否已存在 - 使用basic_health_info表，使用patient_id字段匹配实际数据库结构
                cursor.execute(
                    "SELECT id FROM basic_health_info WHERE patient_id = ? AND category = ? AND item_key = ?",
                    (custom_id, category_key, item_key)
                )
                existing = cursor.fetchone()
                
                if existing:
                    # 更新现有记录
                    cursor.execute(
                        """UPDATE basic_health_info 
                           SET item_value = ?, updated_at = ? 
                           WHERE patient_id = ? AND category = ? AND item_key = ?""",
                        (str(item_value), datetime.now().isoformat(), custom_id, category_key, item_key)
                    )
                else:
                    # 插入新记录 - 使用patient_id字段匹配实际数据库结构
                    cursor.execute(
                        """INSERT INTO basic_health_info 
                           (patient_id, category, item_key, item_value, created_at, updated_at)
                           VALUES (?, ?, ?, ?, ?, ?)""",
                        (custom_id, category_key, item_key, str(item_value), 
                         datetime.now().isoformat(), datetime.now().isoformat())
                    )
            
            logger.info(f"健康信息项保存成功: {category_key}.{item_key} = {item_value}")
            
        except Exception as e:
            logger.error(f"保存健康信息项到数据库时出错: {e}")
            raise
    
    def _validate_custom_id(self, custom_id):
        """验证custom_id是否存在于users表中"""
        try:
            db_manager = get_unified_database_manager("basic_health_info")
            with db_manager.get_connection() as conn:
                cursor = conn.cursor()
                cursor.execute("SELECT COUNT(*) FROM users WHERE custom_id = ?", (custom_id,))
                result = cursor.fetchone()
                return result and result[0] > 0
        except Exception as e:
            logger.error(f"验证custom_id时出错: {e}")
            return False

    def show_message(self, message):
        """显示消息提示"""
        try:
            # 使用应用程序的通知机制
            app = MDApp.get_running_app()
            if app is not None and hasattr(app, 'show_notification'):
                app.show_notification(message)
            else:
                # 使用Snackbar作为备选
                from kivymd.uix.snackbar import MDSnackbar, MDSnackbarText
                snackbar = MDSnackbar(
                    MDSnackbarText(
                        text=message,
                    ),
                    pos_hint={"center_x": 0.5},
                    duration=2,
                )
                snackbar.open()
        except Exception as e:
            logger.error(f"显示消息时出错: {e}")

    def load_data_from_db(self):
        """从数据库加载数据"""
        try:
            # 使用统一数据库管理器
            db_manager = get_unified_database_manager("basic_health_info")
            custom_id = self._get_user_custom_id()
            
            if not custom_id:
                logger.warning(WARNING_NO_CUSTOM_ID_USE_DEFAULT)
                return
            
            # 加载基本健康信息
            self._load_basic_health_info(db_manager, custom_id)
            
            # 加载列表数据
            self._load_list_data(db_manager, custom_id)
            
            logger.info("健康数据加载完成")
            
        except Exception as e:
            logger.error(f"从数据库加载数据时出错: {e}")

    def _initialize_list_data(self):
        """初始化列表数据"""
        self.list_data = {
            'medical_history': [],
            'family_history': [],
            'allergies': [],
            'vaccinations': []
        }

    def _get_user_custom_id(self):
        """获取用户custom_id - 使用多种方式确保获取到有效的custom_id"""
        try:
            # 1. 优先从user_data.json获取
            custom_id = self._get_custom_id_from_user_data()
            if custom_id:
                logger.info(f"从user_data.json获取到custom_id: {custom_id}")
                return custom_id
            
            # 2. 从应用获取
            app = MDApp.get_running_app()
            if hasattr(app, 'custom_id') and app.custom_id:
                logger.info(f"从应用获取到custom_id: {app.custom_id}")
                return app.custom_id
            
            # 3. 从用户管理器获取
            custom_id = self._get_custom_id_from_user_manager()
            if custom_id:
                logger.info(f"从用户管理器获取到custom_id: {custom_id}")
                return custom_id
            
            # 4. 如果都获取不到，记录错误并返回None，避免使用无效的default_user
            logger.error("无法获取有效的custom_id，这将导致外键约束失败")
            return None
            
        except Exception as e:
            logger.error(f"获取用户custom_id时出错: {e}")
            return None
    
    def _get_custom_id_from_user_data(self):
        """从user_data.json文件获取custom_id"""
        try:
            import os
            import json
            user_data_path = os.path.join(os.path.dirname(os.path.dirname(__file__)), 'data', 'user_data.json')
            if os.path.exists(user_data_path):
                with open(user_data_path, 'r', encoding='utf-8') as f:
                    user_data = json.load(f)
                    current_account = user_data.get('current_account')
                    accounts = user_data.get('accounts', [])
                    
                    # 查找当前账户的custom_id
                    for account in accounts:
                        if account.get('username') == current_account:
                            return account.get('custom_id')
                    
                    # 如果没有找到当前账户，使用第一个账户的custom_id
                    if accounts and accounts[0].get('custom_id'):
                        return accounts[0]['custom_id']
        except Exception as e:
            logger.debug(f"从user_data.json获取custom_id失败: {e}")
        return None
    
    def _get_custom_id_from_user_manager(self):
        """从用户管理器获取custom_id"""
        try:
            from utils.user_manager import get_user_manager
            user_manager = get_user_manager()
            current_user = user_manager.get_current_user()
            if current_user and hasattr(current_user, 'custom_id') and current_user.custom_id:
                return current_user.custom_id
        except Exception as e:
            logger.debug(f"从用户管理器获取custom_id失败: {e}")
        return None

    def _load_basic_health_info(self, db_manager, custom_id):
        """加载基本健康信息"""
        try:
            # 从basic_health_info表加载数据，使用patient_id字段匹配实际数据库结构
            records = db_manager.execute_query(
                "SELECT * FROM basic_health_info WHERE patient_id = ?",
                (custom_id,),
                fetch="all"
            )
            
            if records:
                for record in records:
                    self._process_health_record(record)
                
        except Exception as e:
            logger.error(f"加载基本健康信息时出错: {e}")

    def _process_health_record(self, record):
        """处理健康记录数据"""
        try:
            category_key = record.get('category', '')
            item_key = record.get('item_key', '')
            item_value = record.get('item_value', '')
            
            if category_key and item_key:
                # 确保数据结构存在
                if category_key not in self.health_data:
                    self.health_data[category_key] = {}
                
                # 存储数据
                self.health_data[category_key][item_key] = item_value
                
        except Exception as e:
            logger.error(f"处理健康记录时出错: {e}")

    def _load_list_data(self, db_manager, custom_id):
        """加载列表类型的健康数据"""
        try:
            # 从basic_health_info_list表加载数据，使用patient_id字段匹配实际数据库结构
            records = db_manager.execute_query(
                "SELECT * FROM basic_health_info_list WHERE user_custom_id = ?",
                (custom_id,),
                fetch="all"
            )
            
            if records:
                for record in records:
                    self._process_list_record(record)
                
        except Exception as e:
            logger.error(f"加载列表数据时出错: {e}")

    def _process_list_record(self, record):
        """处理列表类型的健康记录"""
        try:
            category_key = record.get('category', '')
            item_data_str = record.get('item_data', '')
            
            if category_key and item_data_str:
                # 解析JSON数据
                try:
                    item_data = json.loads(item_data_str) if isinstance(item_data_str, str) else item_data_str
                except (json.JSONDecodeError, TypeError):
                    logger.warning(f"无法解析列表数据: {item_data_str}")
                    return
                
                # 确保列表数据结构存在
                if category_key not in self.list_data:
                    self.list_data[category_key] = []
                
                # 添加到列表数据
                if item_data not in self.list_data[category_key]:
                    self.list_data[category_key].append(item_data)
                    
        except Exception as e:
            logger.error(f"处理列表记录时出错: {e}")

