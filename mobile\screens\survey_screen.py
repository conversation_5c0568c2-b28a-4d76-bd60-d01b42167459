# 问卷/量表填写与历史答卷界面
import os
import sys

# 添加项目根目录到Python路径
current_dir = os.path.dirname(os.path.abspath(__file__))
parent_dir = os.path.dirname(current_dir)  # mobile目录
if parent_dir not in sys.path:
    sys.path.insert(0, parent_dir)

from kivy.uix.screenmanager import Screen
from kivy.properties import ListProperty, ObjectProperty, StringProperty
from kivy.clock import Clock
from kivymd.uix.button import MDButton, MDButtonText
from kivymd.uix.dialog import MDDialog
from kivymd.uix.list import MDList, MDListItem, MDListItemHeadlineText, MDListItemSupportingText
from kivymd.uix.label import MDLabel
from kivy.metrics import dp
from kivy.factory import Factory
from kivy.lang import Builder
from screens.base_screen import BaseScreen
from api.api_client import APIClient
from utils.auth_manager import get_auth_manager
from utils.cloud_api import get_cloud_api
from theme import AppTheme
import logging
from datetime import datetime
from kivymd.app import MDApp
import traceback

logger = logging.getLogger(__name__)

KV = '''
<SurveyScreen>:
    # BaseScreen会提供main_layout，这里不定义根节点内容
    # 内容将通过setup_content方法动态创建
'''

class SurveyScreen(BaseScreen):
    items = ListProperty([])  # 统一列表：评估/问卷/历史
    current_tab = StringProperty("assessments")
    _is_active = False
    _data_cache = {}  # 缓存：{'assessments': [], 'questionnaires': [], 'history': []}
    _question_cache = {}  # 问题缓存：{id: questions}
    api_client = None

    def __init__(self, **kwargs):
        kwargs['screen_title'] = '评估量表与问卷'
        kwargs['show_top_bar'] = True
        kwargs['top_bar_action_icon'] = 'refresh'
        super().__init__(**kwargs)
        self.api_client = APIClient()
        self.current_tab = "assessments"
        self.auth_manager = get_auth_manager()
        self.app = MDApp.get_running_app()
        self._is_active = False
        self._data_loaded = False
        self._ui_initialized = False  # 添加UI初始化标志

    def init_ui(self, dt=None):
        """初始化UI"""
        try:
            logger.info("[SurveyScreen] 开始初始化UI")
            super().init_ui(dt)
            
            # 检查是否已经初始化过
            if hasattr(self, '_ui_initialized') and self._ui_initialized:
                logger.info("[SurveyScreen] UI已经初始化，跳过")
                return
                
            if not hasattr(self.ids, 'content_container') or self.ids.content_container is None:
                logger.warning("[SurveyScreen] content_container尚未准备好，延迟初始化")
                Clock.schedule_once(self.init_ui, 0.2)
                return
                
            self.setup_content()
            self._ui_initialized = True
            logger.info("[SurveyScreen] UI初始化完成")
        except Exception as e:
            logger.error(f"[SurveyScreen] 初始化UI失败: {e}")
            traceback.print_exc()

    def setup_content(self):
        """设置屏幕内容"""
        try:
            # 使用content_container而不是main_layout
            content_container = self.ids.content_container
            content_container.clear_widgets()  # 清除之前的内容
            
            from kivymd.uix.scrollview import MDScrollView
            from kivymd.uix.boxlayout import MDBoxLayout
            from kivymd.uix.card import MDCard

            content_layout = MDBoxLayout(orientation='vertical', size_hint_y=None, padding=[dp(20), dp(10), dp(20), dp(20)], spacing=dp(6))
            content_layout.bind(minimum_height=content_layout.setter('height'))

            main_card = MDCard(orientation='vertical', size_hint_y=None, height=dp(600), radius=[dp(12)], elevation=3, padding=[dp(16), dp(16), dp(16), dp(16)])
            theme = AppTheme
            main_card.md_bg_color = getattr(theme, 'SURFACE_COLOR', [1, 1, 1, 1])

            tab_layout = MDBoxLayout(size_hint_y=None, height=dp(48), spacing=dp(10))
            self.assessment_tab_btn = MDButton(size_hint_x=0.33, on_release=lambda x: self.switch_tab("assessments"))
            self.assessment_tab_btn.add_widget(MDButtonText(text="评估量表"))
            self.questionnaire_tab_btn = MDButton(size_hint_x=0.33, on_release=lambda x: self.switch_tab("questionnaires"))
            self.questionnaire_tab_btn.add_widget(MDButtonText(text="问卷"))
            self.history_tab_btn = MDButton(size_hint_x=0.33, on_release=lambda x: self.switch_tab("history"))
            self.history_tab_btn.add_widget(MDButtonText(text="历史记录"))
            tab_layout.add_widget(self.assessment_tab_btn)
            tab_layout.add_widget(self.questionnaire_tab_btn)
            tab_layout.add_widget(self.history_tab_btn)

            self.content_area = MDBoxLayout(orientation='vertical')
            self.assessments_content = self.create_tab_content("assessments")
            self.questionnaires_content = self.create_tab_content("questionnaires")
            self.history_content = self.create_tab_content("history")

            main_card.add_widget(tab_layout)
            main_card.add_widget(self.content_area)
            content_layout.add_widget(main_card)
            content_container.add_widget(content_layout)
            self.update_tab_display()
            logger.info("[SurveyScreen] 屏幕内容设置完成")
        except Exception as e:
            logger.error(f"[SurveyScreen] 设置屏幕内容失败: {e}")
            traceback.print_exc()

    def create_tab_content(self, tab_name):
        """创建标签页内容"""
        from kivymd.uix.boxlayout import MDBoxLayout
        from kivymd.uix.scrollview import MDScrollView
        content = MDBoxLayout(orientation='vertical', padding=dp(10), spacing=dp(10), size_hint_y=None, height=dp(500))
        list_scroll = MDScrollView(do_scroll_x=False, do_scroll_y=True, size_hint_y=1)
        list_widget = MDList(padding=dp(0), spacing=dp(1))
        setattr(self, f'{tab_name}_list_widget', list_widget)
        list_scroll.add_widget(list_widget)
        content.add_widget(list_scroll)
        return content

    def on_action(self):
        """处理刷新按钮"""
        self.load_data(force_refresh=True)

    def update_tab_display(self):
        """更新标签页显示"""
        try:
            theme = AppTheme
            primary_color = getattr(theme, 'PRIMARY_COLOR', [0.133, 0.46, 0.82, 1])
            text_light = (1, 1, 1, 1)
            for btn, tab in [(self.assessment_tab_btn, "assessments"), (self.questionnaire_tab_btn, "questionnaires"), (self.history_tab_btn, "history")]:
                btn.style = "filled" if self.current_tab == tab else "outlined"
                btn.md_bg_color = primary_color if self.current_tab == tab else (0, 0, 0, 0)
                for child in btn.children:
                    if hasattr(child, 'text_color'):
                        child.text_color = text_light if self.current_tab == tab else primary_color
            self.content_area.clear_widgets()
            content_attr = f'{self.current_tab}_content'
            if hasattr(self, content_attr):
                self.content_area.add_widget(getattr(self, content_attr))
        except Exception as e:
            logger.error(f"[SurveyScreen] 更新标签页显示失败: {e}")

    def switch_tab(self, tab_name):
        """切换标签页"""
        if self.current_tab == tab_name:
            return
        self.current_tab = tab_name
        self.update_tab_display()
        self.load_data()

    def on_enter(self):
        """进入屏幕"""
        super().on_enter()
        self._is_active = True
        if not self._data_loaded:
            self.load_data()
            self._data_loaded = True

    def on_leave(self):
        """离开屏幕"""
        self._is_active = False

    def load_data(self, force_refresh=False):
        """加载数据（统一方法）"""
        try:
            cloud_api = get_cloud_api()
            if not cloud_api.is_authenticated():
                logger.warning("用户未登录，无法加载数据")
                return
            tab = self.current_tab
            if tab in self._data_cache and not force_refresh:
                self.items = self._data_cache[tab]
                self.update_list_ui()
                return
            if tab == "assessments":
                endpoint = "mobile/pending-assessments"
            elif tab == "questionnaires":
                endpoint = "mobile/pending-questionnaires"
            elif tab == "history":
                self.load_history()
                return
            else:
                return
            result = cloud_api._make_request("GET", endpoint, headers={'Authorization': f"Bearer {cloud_api.token}", 'X-User-ID': cloud_api.custom_id})
            if result and result.get('status') == 'success':
                data = result.get('data', [])
                self.items = data
                self._data_cache[tab] = data
                logger.info(f"成功加载 {tab} 数据，共 {len(data)} 条")
            else:
                logger.warning(f"加载 {tab} 失败")
            self.update_list_ui()
        except Exception as e:
            logger.error(f"加载数据失败: {e}")
            traceback.print_exc()

    def load_history(self):
        """加载历史记录"""
        try:
            cloud_api = get_cloud_api()
            assessments = cloud_api._make_request("GET", "mobile/history-assessments", headers={'Authorization': f"Bearer {cloud_api.token}", 'X-User-ID': cloud_api.custom_id}).get('data', [])
            questionnaires = cloud_api._make_request("GET", "mobile/history-questionnaires", headers={'Authorization': f"Bearer {cloud_api.token}", 'X-User-ID': cloud_api.custom_id}).get('data', [])
            self.items = assessments + questionnaires
            self._data_cache['history'] = self.items
            logger.info(f"加载历史记录，共 {len(self.items)} 条")
            self.update_list_ui()
        except Exception as e:
            logger.error(f"加载历史记录失败: {e}")

    def update_list_ui(self):
        """更新列表UI（统一方法）"""
        try:
            list_widget = getattr(self, f'{self.current_tab}_list_widget', None)
            if not list_widget:
                return
            list_widget.clear_widgets()
            if not self.items:
                list_widget.add_widget(MDLabel(text='暂无数据', halign='center', theme_text_color='Hint'))
                return
            unique_items = []
            seen_titles = set()
            for item in self.items:
                title = item.get('title') or item.get('name') or '未命名'
                if title not in seen_titles:
                    seen_titles.add(title)
                    unique_items.append(item)
            for item in unique_items:
                list_item = MDListItem(on_release=lambda x, i=item: self.on_item_selected(i))
                list_item.add_widget(MDListItemHeadlineText(text=item.get('title') or '未命名'))
                desc = item.get('description') or item.get('template', {}).get('description') or '无描述'
                if len(desc) > 100:
                    desc = desc[:100] + '...'
                list_item.add_widget(MDListItemSupportingText(text=desc))
                list_widget.add_widget(list_item)
        except Exception as e:
            logger.error(f"更新列表UI失败: {e}")

    def on_item_selected(self, item):
        """项选择事件"""
        item_type = self.current_tab if self.current_tab != "history" else item.get('item_type', '').split('_')[0]
        if item_type == "assessments":
            self.open_assessment(item)
        elif item_type == "questionnaires":
            self.open_questionnaire(item)
        else:
            self.navigate_to_response_detail(item)

    def open_assessment(self, data):
        """打开评估"""
        try:
            assessment_id = data.get('id')
            if assessment_id in self._question_cache:
                data['questions'] = self._question_cache[assessment_id]
            else:
                cloud_api = get_cloud_api()
                result = cloud_api.get_assessment_questions(assessment_id)
                if result and 'questions' in result:
                    data['questions'] = result['questions']
                    self._question_cache[assessment_id] = data['questions']
            self.app.assessment_to_fill = data
            self.manager.current = "assessment_form_screen"
        except Exception as e:
            logger.error(f"打开评估失败: {e}")
            self.show_error("打开评估失败")

    def open_questionnaire(self, data):
        """打开问卷"""
        try:
            questionnaire_id = data.get('id')
            if questionnaire_id in self._question_cache:
                data['questions'] = self._question_cache[questionnaire_id]
            else:
                cloud_api = get_cloud_api()
                result = cloud_api.get_questionnaire_questions(questionnaire_id)
                if result and 'questions' in result:
                    data['questions'] = result['questions']
                    self._question_cache[questionnaire_id] = data['questions']
                elif not data['questions']:  # 默认问题作为fallback
                    data['questions'] = [{'id': i+1, 'text': f'问题 {i+1}', 'type': 'radio', 'options': [{'value': str(j), 'text': f'选项{j}'} for j in range(1,6)], 'is_required': True} for i in range(data.get('question_count', 5))]
            self.app.questionnaire_to_fill = data
            self.manager.current = "questionnaire_form_screen"
        except Exception as e:
            logger.error(f"打开问卷失败: {e}")
            self.show_error("打开问卷失败")

    def navigate_to_response_detail(self, response):
        """导航到报告详情"""
        try:
            report_type = "assessment" if "assessment" in response.get('item_type', '') else "questionnaire"
            report_id = response.get('id')
            cloud_api = get_cloud_api()
            result = cloud_api.get_report_detail(report_id, report_type)
            report_data = result.get('data', response) if result.get('status') == 'success' else response
            self.manager.current_report_data = report_data
            self.manager.current_report_type = report_type
            self.manager.current_report_id = report_id
            self.manager.current = 'report_detail'
        except Exception as e:
            logger.error(f"导航报告详情失败: {e}")
            self.show_error("打开报告失败")

    def show_error(self, message):
        """显示错误对话"""
        dialog = MDDialog(title="错误", text=message, buttons=[MDButton(MDButtonText(text="确定"), on_release=lambda x: dialog.dismiss())])
        dialog.open()

Factory.register('SurveyScreen', cls=SurveyScreen)
Builder.load_string(KV)