"""移动端API

此模块提供了移动端应用程序所需的API端点，使用新的临床量表和问卷模块。
"""
from typing import List, Optional, Dict, Any
from fastapi import APIRouter, Depends, HTTPException, Query, Path, File, UploadFile, Body
from sqlalchemy.orm import Session
from pydantic import ValidationError
from datetime import datetime
import logging
import json

logger = logging.getLogger(__name__)

from app.db.session import get_db
from app.models.user import User
from app.core.auth import get_current_user, get_current_active_user, get_current_active_user_custom

# 导入评估量表和问卷模型
try:
    from app.models.assessment import (
        Assessment, AssessmentTemplate, AssessmentTemplateQuestion, AssessmentResponse
    )
    from app.models.distribution import AssessmentDistribution
except ImportError as e:
    logger.error(f"无法导入评估量表模型: {e}")
    # 设置为None以便在代码中检查
    Assessment = AssessmentTemplate = AssessmentTemplateQuestion = AssessmentResponse = None
    AssessmentDistribution = None

try:
    from app.models.questionnaire import (
        Questionnaire, QuestionnaireTemplate, QuestionnaireTemplateQuestion,
        QuestionnaireResponse
    )
    from app.models.distribution import QuestionnaireDistribution
except ImportError as e:
    logger.error(f"无法导入问卷模型: {e}")
    # 设置为None以便在代码中检查
    Questionnaire = QuestionnaireTemplate = QuestionnaireTemplateQuestion = None
    QuestionnaireResponse = QuestionnaireDistribution = None

# 导入结果模型
try:
    from app.models.result import AssessmentResult, QuestionnaireResult
except ImportError as e:
    logger.error(f"无法导入结果模型: {e}")
    AssessmentResult = None
    QuestionnaireResult = None

# 导入健康记录模型
try:
    from app.models.health_record import HealthRecord, RecordType
except ImportError as e:
    logger.error(f"无法导入健康记录模型: {e}")
    HealthRecord = RecordType = None

router = APIRouter()

# 评估量表相关API
@router.get("/templates/assessment-templates", response_model=Dict[str, Any])
def get_assessment_templates(
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_active_user_custom)
):
    """
    获取评估量表模板列表
    """
    try:
        # 检查模型是否可用
        if AssessmentTemplate is None:
            return {
                "status": "error",
                "message": "评估量表模型不可用"
            }
        
        # 从正确的评估量表模板表获取模板
        templates = db.query(AssessmentTemplate).filter(
            AssessmentTemplate.is_active == True
        ).all()
        
        # 转换为前端需要的格式
        result = []
        for tpl in templates:
            # 获取评估量表问题
            if AssessmentTemplateQuestion is not None:
                questions = db.query(AssessmentTemplateQuestion).filter(
                    AssessmentTemplateQuestion.template_id == tpl.id
                ).order_by(AssessmentTemplateQuestion.order).all()
                
                # 构建问题列表
                question_list = []
                for q in questions:
                    question_data = {
                        "id": q.id,
                        "question_id": q.question_id,
                        "question_text": q.question_text,
                        "question_type": q.question_type,
                        "options": q.options,
                        "order": q.order,
                        "is_required": q.is_required,
                        "jump_logic": q.jump_logic
                    }
                    question_list.append(question_data)
            else:
                question_list = []
            
            template_data = {
                "id": tpl.id,
                "template_key": getattr(tpl, 'template_key', None),
                "name": tpl.name,
                "name_en": getattr(tpl, 'name_en', ''),
                "description": tpl.description,
                "instructions": tpl.instructions,
                "category": getattr(tpl, 'category', ''),
                "type": "assessment",  # 确保评估量表类型正确
                "assessment_type": tpl.assessment_type,  # 添加评估量表类型
                "status": getattr(tpl, 'status', 'active'),
                "version": tpl.version,
                "max_score": tpl.max_score,  # 添加最大分数
                "questions": question_list
            }
            result.append(template_data)
        
        logger.info(f"返回 {len(result)} 个评估量表模板")
        return {
            "status": "success",
            "data": result
        }
    except Exception as e:
        logger.error(f"获取评估量表模板失败: {str(e)}")
        return {
            "status": "error",
            "message": f"获取评估量表模板失败: {str(e)}"
        }

@router.get("/assessments", response_model=Dict[str, Any])
def get_assessments(
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_active_user_custom)
):
    """
    获取用户的评估量表列表（优化版本，减少数据库查询）
    """
    try:
        # 检查模型是否可用
        if any(model is None for model in [AssessmentDistribution, Assessment, AssessmentTemplate, AssessmentTemplateQuestion]):
            return {
                "status": "error",
                "message": "评估量表模型不可用"
            }
        
        # 使用JOIN查询一次性获取所有相关数据，避免N+1查询问题
        query = db.query(
            AssessmentDistribution,
            Assessment,
            AssessmentTemplate
        ).join(
            Assessment, AssessmentDistribution.assessment_id == Assessment.id
        ).join(
            AssessmentTemplate, Assessment.template_id == AssessmentTemplate.id
        ).filter(
            AssessmentDistribution.custom_id == current_user.custom_id
        )
        
        distributions_data = query.all()
        
        # 批量获取所有模板的问题，避免循环查询
        template_ids = [template.id for _, _, template in distributions_data]
        questions_by_template = {}
        if template_ids:
            questions_query = db.query(AssessmentTemplateQuestion).filter(
                AssessmentTemplateQuestion.template_id.in_(template_ids)
            ).order_by(AssessmentTemplateQuestion.template_id, AssessmentTemplateQuestion.order)
            
            # 按模板ID分组问题
            for q in questions_query:
                if q.template_id not in questions_by_template:
                    questions_by_template[q.template_id] = []
                questions_by_template[q.template_id].append({
                    "id": q.id,
                    "question_id": q.question_id,
                    "question_text": q.question_text,
                    "question_type": q.question_type,
                    "options": q.options,
                    "order": q.order,
                    "is_required": q.is_required,
                    "jump_logic": q.jump_logic
                })
        
        result = []
        for dist, assessment, template in distributions_data:
            question_list = questions_by_template.get(template.id, [])
            
            assessment_data = {
                "id": assessment.id,
                "distribution_id": dist.id,
                "name": assessment.title or template.name or "未命名量表",
                "assessment_type": assessment.assessment_type or template.assessment_type,
                "status": assessment.status,
                "total_score": assessment.score,
                "round_number": getattr(assessment, 'round_number', 1),
                "sequence_number": getattr(assessment, 'sequence_number', 1),
                "unique_identifier": getattr(assessment, 'unique_identifier', None),
                "due_date": dist.due_date.isoformat() if dist.due_date else None,
                "created_at": assessment.created_at.isoformat() if assessment.created_at else None,
                "completed_at": assessment.completed_at.isoformat() if assessment.completed_at else None,
                "template": {
                    "id": template.id,
                    "name": template.name or "未命名量表模板",
                    "assessment_type": template.assessment_type,
                    "version": template.version,
                    "description": template.description,
                    "instructions": template.instructions,
                    "questions": question_list
                }
            }
            result.append(assessment_data)
        
        return {
            "status": "success",
            "data": result
        }
    except Exception as e:
        logger.error(f"获取用户评估量表失败: {str(e)}")
        return {
            "status": "error",
            "message": f"获取用户评估量表失败: {str(e)}"
        }

@router.post("/assessments/{assessment_id}/submit", response_model=Dict[str, Any])
def submit_assessment_result(
    assessment_id: int,
    request_data: Dict[str, Any],
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_active_user_custom)
):
    """
    提交评估量表结果
    """
    try:
        # 验证请求数据格式
        if "answers" not in request_data:
            return {
                "status": "error",
                "message": "缺少answers字段"
            }
        
        answers = request_data["answers"]
        
        # 验证用户是否有权限提交此评估量表
        distribution = db.query(AssessmentDistribution).filter(
            AssessmentDistribution.assessment_id == assessment_id,
            AssessmentDistribution.custom_id == current_user.custom_id
        ).first()
        
        if not distribution:
            return {
                "status": "error",
                "message": "无权限提交此评估量表"
            }
        
        # 获取评估量表
        assessment = db.query(Assessment).filter(Assessment.id == assessment_id).first()
        if not assessment:
            return {
                "status": "error",
                "message": "评估量表不存在"
            }
        
        # 检查评估量表是否已完成
        if assessment.status == "completed":
            return {
                "status": "error",
                "message": "评估量表已完成，无法重复提交"
            }
        
        # 获取评估量表模板进行验证
        template = None
        if assessment.template_id:
            template = db.query(AssessmentTemplate).filter(
                AssessmentTemplate.id == assessment.template_id
            ).first()
        else:
            # 如果template_id为空，尝试根据名称匹配模板
            template = db.query(AssessmentTemplate).filter(
                AssessmentTemplate.name == assessment.title
            ).first()
            
            # 如果找到匹配的模板，更新assessment的template_id
            if template:
                assessment.template_id = template.id
        
        if not template:
            return {
                "status": "error",
                "message": "评估量表模板不存在"
            }
        
        # 获取模板问题进行答案验证
        template_questions = db.query(AssessmentTemplateQuestion).filter(
            AssessmentTemplateQuestion.template_id == template.id
        ).all()
        
        question_map = {str(q.question_id): q for q in template_questions}
        
        # 验证答案格式和完整性
        validated_answers = []
        total_score = 0
        
        for answer in answers:
            if not isinstance(answer, dict):
                return {
                    "status": "error",
                    "message": "答案格式错误"
                }
            
            question_id = str(answer.get("question_id", ""))
            if question_id not in question_map:
                return {
                    "status": "error",
                    "message": f"问题ID {question_id} 不存在"
                }
            
            question = question_map[question_id]
            
            # 验证必填问题
            if question.is_required and ("answer" not in answer or answer["answer"] is None):
                return {
                    "status": "error",
                    "message": f"问题 {question_id} 为必填项"
                }
            
            validated_answer = {
                "question_id": question_id,
                "answer": answer.get("answer"),
                "score": answer.get("score", 0)
            }
            
            validated_answers.append(validated_answer)
            total_score += answer.get("score", 0)
        
        # 进行结果计算和分析
        result_analysis = calculate_assessment_result(template, total_score, validated_answers)
        
        # 生成或更新唯一标识符
        if not assessment.unique_identifier:
            assessment.unique_identifier = f"{template.id}_{current_user.custom_id}_{assessment.round_number}_{assessment.sequence_number}"
        
        # 更新评估量表状态和结果
        assessment.status = "completed"
        assessment.completed_at = datetime.now()
        assessment.answers = {"answers": validated_answers}
        assessment.score = total_score
        assessment.max_score = template.max_score
        assessment.result = result_analysis["result_category"]
        assessment.conclusion = result_analysis["conclusion"]
        
        # 更新分发记录状态
        distribution.status = "completed"
        distribution.completed_at = datetime.now()
        
        # 生成分析报告（提前生成，用于AssessmentResponse）
        analysis_report = generate_assessment_analysis_report(template, result_analysis, validated_answers)
        
        # 创建评估响应记录到assessment_responses表
        try:
            assessment_response = AssessmentResponse(
                assessment_id=assessment_id,
                custom_id=current_user.custom_id,
                answers=validated_answers,  # 直接使用列表，SQLAlchemy会自动转换为JSON
                score=float(total_score),
                dimension_scores=result_analysis.get("dimension_scores", {}),
                result=result_analysis["result_category"],
                notes=result_analysis["conclusion"]
            )
            
            db.add(assessment_response)
            db.flush()  # 获取ID但不提交
            
            logger.info(f"已创建评估响应记录: assessment_id={assessment_id}, response_id={assessment_response.id}")
            
        except Exception as response_error:
            logger.warning(f"创建评估响应记录失败: {str(response_error)}")
            # 不影响主流程，继续执行
        
        # 自动创建评估结果记录到assessment_results表
        try:
            # 计算分数和等级
            percentage = (total_score / template.max_score * 100) if template.max_score > 0 else 0
            
            # 简单的等级判断
            if percentage >= 80:
                result_level = "优秀"
            elif percentage >= 60:
                result_level = "良好"
            elif percentage >= 40:
                result_level = "一般"
            else:
                result_level = "需要改进"
            
            # 创建评估结果记录
            assessment_result = AssessmentResult(
                assessment_id=assessment_id,
                custom_id=current_user.custom_id,
                template_id=assessment.template_id,
                total_score=float(total_score),
                max_score=float(template.max_score),
                percentage=percentage,
                result_level=result_level,
                result_category=result_analysis["result_category"],
                interpretation=result_analysis["conclusion"],
                recommendations=json.dumps(result_analysis.get("recommendations", []), ensure_ascii=False),
                dimension_scores=result_analysis.get("dimension_scores", {}),
                raw_answers=validated_answers,
                report_generated=True,
                report_content=analysis_report,
                report_format="markdown",
                status="calculated"
            )
            
            db.add(assessment_result)
            logger.info(f"已创建评估结果记录: assessment_id={assessment_id}, result_level={result_level}")
            
        except Exception as result_error:
            logger.warning(f"创建评估结果记录失败: {str(result_error)}")
            # 不影响主流程，继续执行
        
        # 保存到健康资料
        health_record_data = create_health_record_from_assessment(
            db, current_user, assessment, template, result_analysis
        )
        
        db.commit()
        
        return {
            "status": "success",
            "message": "评估量表提交成功，结果已保存到健康资料",
            "data": {
                "assessment_id": assessment_id,
                "total_score": total_score,
                "max_score": template.max_score,
                "result_category": result_analysis["result_category"],
                "conclusion": result_analysis["conclusion"],
                "completed_at": assessment.completed_at.isoformat(),
                "answers_count": len(validated_answers),
                "health_record_id": health_record_data["id"] if health_record_data else None
            }
        }
    except Exception as e:
        db.rollback()
        return {
            "status": "error",
            "message": f"提交评估量表失败: {str(e)}"
        }

# 问卷相关API
@router.get("/templates/questionnaire-templates", response_model=Dict[str, Any])
def get_questionnaire_templates(
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_active_user_custom)
):
    """
    获取问卷模板列表
    """
    try:
        # 检查模型是否可用
        if QuestionnaireTemplate is None or QuestionnaireTemplateQuestion is None:
            return {
                "status": "error",
                "message": "问卷模型不可用"
            }
        
        # 从数据库获取可用的问卷模板（排除评估量表类型）
        templates = db.query(QuestionnaireTemplate).filter(
            QuestionnaireTemplate.is_active == True,
            ~QuestionnaireTemplate.questionnaire_type.like('assessment_%')
        ).all()
        
        # 转换为前端需要的格式
        result = []
        for tpl in templates:
            # 获取问卷问题
            questions = db.query(QuestionnaireTemplateQuestion).filter(
                QuestionnaireTemplateQuestion.template_id == tpl.id
            ).order_by(QuestionnaireTemplateQuestion.order).all()
            
            # 构建问题列表
            question_list = []
            for q in questions:
                question_data = {
                    "id": q.id,
                    "question_id": q.question_id,
                    "question_text": q.question_text,
                    "question_type": q.question_type,
                    "options": q.options,
                    "order": q.order,
                    "is_required": q.is_required,
                    "jump_logic": q.jump_logic
                }
                question_list.append(question_data)
            
            template_data = {
                "id": tpl.id,
                "template_key": getattr(tpl, 'template_key', None),
                "name": tpl.name,
                "name_en": getattr(tpl, 'name_en', ''),
                "description": tpl.description,
                "instructions": tpl.instructions,
                "category": getattr(tpl, 'category', ''),
                "type": getattr(tpl, 'type', 'questionnaire'),
                "status": getattr(tpl, 'status', 'pending'),
                "version": tpl.version,
                "questions": question_list
            }
            result.append(template_data)
        
        return {
            "status": "success",
            "data": result
        }
    except Exception as e:
        logger.error(f"获取问卷模板失败: {str(e)}")
        return {
            "status": "error",
            "message": f"获取问卷模板失败: {str(e)}"
        }

@router.get("/questionnaires", response_model=Dict[str, Any])
def get_questionnaires(
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_active_user_custom)
):
    """
    获取用户的问卷列表
    """
    try:
        # 从分发记录中获取分配给用户的问卷
        distributions = db.query(QuestionnaireDistribution).filter(
            QuestionnaireDistribution.custom_id == current_user.custom_id
        ).all()
        
        result = []
        for dist in distributions:
            # 获取问卷
            questionnaire = db.query(Questionnaire).filter(Questionnaire.id == dist.questionnaire_id).first()
            if questionnaire:
                # 获取问卷模板
                template = db.query(QuestionnaireTemplate).filter(
                    QuestionnaireTemplate.id == questionnaire.template_id
                ).first() if hasattr(questionnaire, 'template_id') else None
                
                if template:
                    # 获取模板问题
                    questions = db.query(QuestionnaireTemplateQuestion).filter(
                        QuestionnaireTemplateQuestion.template_id == template.id
                    ).order_by(QuestionnaireTemplateQuestion.order).all()
                    
                    question_list = []
                    for q in questions:
                        question_data = {
                            "id": q.id,
                            "question_id": q.question_id,
                            "question_text": q.question_text,
                            "question_type": q.question_type,
                            "options": q.options,
                            "order": q.order,
                            "is_required": q.is_required,
                            "jump_logic": q.jump_logic
                        }
                        question_list.append(question_data)
                    
                    questionnaire_data = {
                        "id": questionnaire.id,
                        "distribution_id": dist.id,
                        "name": questionnaire.title,
                        "questionnaire_type": questionnaire.questionnaire_type,
                        "status": questionnaire.status,
                        "due_date": dist.due_date.isoformat() if dist.due_date else None,
                        "created_at": questionnaire.created_at.isoformat() if questionnaire.created_at else None,
                        "completed_at": dist.completed_at.isoformat() if dist.completed_at else None,
                        "template": {
                            "id": template.id,
                            "name": template.name,
                            "questionnaire_type": template.questionnaire_type,
                            "version": template.version,
                            "description": template.description,
                            "instructions": template.instructions,
                            "questions": question_list
                        }
                    }
                    result.append(questionnaire_data)
        
        return {
            "status": "success",
            "data": result
        }
    except Exception as e:
        return {
            "status": "error",
            "message": f"获取用户问卷失败: {str(e)}"
        }

@router.post("/questionnaires/{questionnaire_id}/submit", response_model=Dict[str, Any])
def submit_questionnaire_result(
    questionnaire_id: int,
    request_data: Dict[str, Any],
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_active_user_custom)
):
    """
    提交问卷结果
    """
    try:
        # 验证请求数据格式
        if "answers" not in request_data:
            return {
                "status": "error",
                "message": "缺少answers字段"
            }
        
        answers = request_data["answers"]
        
        # 验证用户是否有权限提交此问卷
        distribution = db.query(QuestionnaireDistribution).filter(
            QuestionnaireDistribution.questionnaire_id == questionnaire_id,
            QuestionnaireDistribution.custom_id == current_user.custom_id
        ).first()
        
        if not distribution:
            return {
                "status": "error",
                "message": "无权限提交此问卷"
            }
        
        # 获取问卷
        questionnaire = db.query(Questionnaire).filter(Questionnaire.id == questionnaire_id).first()
        if not questionnaire:
            return {
                "status": "error",
                "message": "问卷不存在"
            }
        
        # 检查问卷是否已完成
        if questionnaire.status == "completed":
            return {
                "status": "error",
                "message": "问卷已完成，无法重复提交"
            }
        
        # 获取问卷模板进行验证
        template = db.query(QuestionnaireTemplate).filter(
            QuestionnaireTemplate.id == questionnaire.template_id
        ).first()
        
        if not template:
            return {
                "status": "error",
                "message": "问卷模板不存在"
            }
        
        # 获取模板问题进行答案验证
        template_questions = db.query(QuestionnaireTemplateQuestion).filter(
            QuestionnaireTemplateQuestion.template_id == template.id
        ).all()
        
        question_map = {str(q.question_id): q for q in template_questions}
        
        # 验证答案格式和完整性
        validated_answers = []
        
        for answer in answers:
            if not isinstance(answer, dict):
                return {
                    "status": "error",
                    "message": "答案格式错误"
                }
            
            question_id = str(answer.get("question_id", ""))
            if question_id not in question_map:
                return {
                    "status": "error",
                    "message": f"问题ID {question_id} 不存在"
                }
            
            question = question_map[question_id]
            
            # 验证必填问题
            if question.is_required and ("answer" not in answer or answer["answer"] is None):
                return {
                    "status": "error",
                    "message": f"问题 {question_id} 为必填项"
                }
            
            validated_answer = {
                "question_id": question_id,
                "answer": answer.get("answer")
            }
            
            validated_answers.append(validated_answer)
        
        # 计算问卷结果和分析
        result_analysis = calculate_questionnaire_result(template, validated_answers)
        
        # 生成问卷报告
        analysis_report = generate_questionnaire_analysis_report(
            template, validated_answers, result_analysis["result_category"], 
            result_analysis.get("completion_rate", 100.0)
        )
        
        # 更新问卷状态和结果
        questionnaire.status = "completed"
        questionnaire.answers = {"answers": validated_answers}
        questionnaire.conclusion = result_analysis["conclusion"]
        
        # 更新分发记录状态
        distribution.status = "completed"
        distribution.completed_at = datetime.now()
        
        # 创建问卷回复
        questionnaire_response = QuestionnaireResponse(
            questionnaire_id=questionnaire_id,
            custom_id=current_user.custom_id,
            answers={"answers": validated_answers},
            status="completed",
            report=analysis_report
        )
        
        db.add(questionnaire_response)
        db.flush()  # 获取ID但不提交
        
        # 自动创建问卷结果记录到questionnaire_results表
        try:
            # 计算分数和等级
            total_score = result_analysis.get("completed_questions", 0)
            max_score = result_analysis.get("total_questions", 1)
            percentage = (total_score / max_score * 100) if max_score > 0 else 0
            
            # 简单的等级判断
            if percentage >= 80:
                result_level = "优秀"
            elif percentage >= 60:
                result_level = "良好"
            elif percentage >= 40:
                result_level = "一般"
            else:
                result_level = "需要改进"
            
            # 创建问卷结果记录
            questionnaire_result = QuestionnaireResult(
                questionnaire_id=questionnaire_id,
                response_id=questionnaire_response.id,
                custom_id=current_user.custom_id,
                template_id=questionnaire.template_id,
                total_score=float(total_score),
                max_score=float(max_score),
                percentage=percentage,
                result_level=result_level,
                result_category=result_analysis["result_category"],
                interpretation=result_analysis["conclusion"],
                recommendations=json.dumps(result_analysis.get("recommendations", []), ensure_ascii=False),
                raw_answers=json.dumps(validated_answers, ensure_ascii=False),
                report_generated=True,
                report_content=analysis_report,
                report_format="markdown",
                status="calculated",
                calculated_at=datetime.now()
            )
            
            db.add(questionnaire_result)
            logger.info(f"已创建问卷结果记录: response_id={questionnaire_response.id}, result_level={result_level}")
            
        except Exception as result_error:
            logger.warning(f"创建问卷结果记录失败: {str(result_error)}")
            # 不影响主流程，继续执行
        
        # 保存到健康资料
        health_record_info = create_health_record_from_questionnaire(
            db, current_user, questionnaire, template, result_analysis, distribution
        )
        
        db.commit()
        
        return {
            "status": "success",
            "message": "问卷提交成功",
            "data": {
                "questionnaire_id": questionnaire_id,
                "response_id": questionnaire_response.id,
                "completed_at": distribution.completed_at.isoformat(),
                "answers_count": len(validated_answers),
                "result_category": result_analysis["result_category"],
                "conclusion": result_analysis["conclusion"],
                "health_record_id": health_record_info["id"] if health_record_info else None
            }
        }
    except Exception as e:
        db.rollback()
        return {
            "status": "error",
            "message": f"提交问卷失败: {str(e)}"
        }

@router.get("/pending-questionnaires", tags=["移动端-问卷"], summary="获取待完成问卷列表")
def get_pending_questionnaires(
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_active_user_custom)
):
    distributions = db.query(QuestionnaireDistribution, Questionnaire, QuestionnaireTemplate).join(
        Questionnaire, QuestionnaireDistribution.questionnaire_id == Questionnaire.id
    ).outerjoin(
        QuestionnaireTemplate, Questionnaire.template_id == QuestionnaireTemplate.id
    ).filter(
        QuestionnaireDistribution.custom_id == current_user.custom_id,
        QuestionnaireDistribution.status == "pending"
    ).all()
    
    # 批量获取所有模板的问题，避免循环查询
    template_ids = [template.id for _, _, template in distributions if template]
    questions_by_template = {}
    if template_ids:
        questions_query = db.query(QuestionnaireTemplateQuestion).filter(
            QuestionnaireTemplateQuestion.template_id.in_(template_ids)
        ).order_by(QuestionnaireTemplateQuestion.template_id, QuestionnaireTemplateQuestion.order)
        
        # 按模板ID分组问题
        for q in questions_query:
            if q.template_id not in questions_by_template:
                questions_by_template[q.template_id] = []
            questions_by_template[q.template_id].append({
                "id": q.id,
                "question_id": q.question_id,
                "question_text": q.question_text,
                "question_type": q.question_type,
                "options": q.options,
                "order": q.order,
                "is_required": q.is_required,
                "jump_logic": q.jump_logic
            })
    
    result = []
    for d, q, t in distributions:
        # 获取问题列表
        question_list = questions_by_template.get(t.id, []) if t else []
        
        questionnaire_data = {
            "id": q.id,
            "title": q.title or "未命名问卷",
            "due_date": d.due_date.isoformat() if d.due_date else None,
            "status": d.status,
            "distribution_id": d.id,
            "created_at": q.created_at.isoformat() if q.created_at else None,
            "type": "questionnaire"
        }
        
        # 如果有模板，添加模板信息和问题列表
        if t:
            questionnaire_data["template"] = {
                "id": t.id,
                "name": t.name or "未命名问卷模板",
                "questionnaire_type": t.questionnaire_type,
                "version": t.version,
                "description": t.description,
                "instructions": t.instructions,
                "questions": question_list
            }
        
        # 同时也在根级别添加questions字段，确保兼容性
        questionnaire_data["questions"] = question_list
        
        result.append(questionnaire_data)
        
    return {"status": "success", "data": result}

@router.get("/history-questionnaires", tags=["移动端-问卷"], summary="获取历史问卷报告")
def get_history_questionnaires(
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_active_user_custom)
):
    responses = db.query(QuestionnaireResponse, Questionnaire).join(
        Questionnaire, QuestionnaireResponse.questionnaire_id == Questionnaire.id
    ).filter(
        QuestionnaireResponse.custom_id == current_user.custom_id,
        QuestionnaireResponse.status == "completed",
        QuestionnaireResponse.report != None
    ).all()
    result = []
    for r, q in responses:
        result.append({
            "id": r.id,
            "questionnaire_id": r.questionnaire_id,
            "title": q.title or "未命名问卷",
            "report": r.report,
            "completed_at": r.updated_at,
            "type": "questionnaire_report"
        })
    return {"status": "success", "data": result}

@router.get("/completed-questionnaires", tags=["移动端-问卷"], summary="获取已完成问卷（含原始与报告）")
def get_completed_questionnaires(
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_active_user_custom)
):
    responses = db.query(QuestionnaireResponse, Questionnaire).join(
        Questionnaire, QuestionnaireResponse.questionnaire_id == Questionnaire.id
    ).filter(
        QuestionnaireResponse.custom_id == current_user.custom_id,
        QuestionnaireResponse.status == "completed"
    ).all()
    original = []
    reports = []
    for r, q in responses:
        item = {
            "id": r.id,
            "questionnaire_id": r.questionnaire_id,
            "title": q.title or "未命名问卷",
            "completed_at": r.updated_at,
            "score": getattr(r, 'total_score', None),
            "type": "questionnaire_response"
        }
        if getattr(r, 'report', None):
            item["report"] = r.report
            item["type"] = "questionnaire_report"
            reports.append(item)
        else:
            original.append(item)
    return {"status": "success", "data": {"original": original, "reports": reports}}

@router.get("/pending-assessments", tags=["移动端-量表"], summary="获取待完成量表列表")
def get_pending_assessments(
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_active_user_custom)
):
    try:
        # 检查模型是否可用
        if any(model is None for model in [AssessmentDistribution, Assessment, AssessmentTemplate, AssessmentTemplateQuestion]):
            logger.error("评估量表模型不可用")
            return {
                "status": "error",
                "message": "评估量表模型不可用",
                "data": []
            }
        
        # 添加调试信息
        logger.info(f"查询用户 {current_user.custom_id} 的pending量表")
        
        # 先查询所有分发记录
        all_distributions = db.query(AssessmentDistribution).filter(
            AssessmentDistribution.custom_id == current_user.custom_id
        ).all()
        logger.info(f"用户总共有 {len(all_distributions)} 个量表分发记录")
        
        # 查询pending状态的分发记录
        pending_distributions = db.query(AssessmentDistribution).filter(
            AssessmentDistribution.custom_id == current_user.custom_id,
            AssessmentDistribution.status == "pending"
        ).all()
        logger.info(f"用户有 {len(pending_distributions)} 个pending状态的量表分发")
        
        distributions = db.query(AssessmentDistribution, Assessment, AssessmentTemplate).join(
            Assessment, AssessmentDistribution.assessment_id == Assessment.id
        ).outerjoin(
            AssessmentTemplate, Assessment.template_id == AssessmentTemplate.id
        ).filter(
            AssessmentDistribution.custom_id == current_user.custom_id,
            AssessmentDistribution.status == "pending"
        ).all()
        
        # 添加详细的调试日志
        logger.info(f"查询到 {len(distributions)} 个分发记录")
        for i, (d, a, t) in enumerate(distributions):
            logger.info(f"分发{i+1}: Distribution ID={d.id}, Assessment ID={a.id}, Assessment Name={a.name}")
            logger.info(f"   Assessment template_id={a.template_id}, Template对象={t is not None}")
            if t:
                logger.info(f"   Template: ID={t.id}, Name={t.name}")
            else:
                logger.warning(f"   Template为None！Assessment template_id={a.template_id}")
                # 如果template为None但template_id有值，手动查询模板
                if a.template_id:
                    manual_template = db.query(AssessmentTemplate).filter(AssessmentTemplate.id == a.template_id).first()
                    if manual_template:
                        logger.info(f"   手动查询到模板: ID={manual_template.id}, Name={manual_template.name}")
                    else:
                        logger.error(f"   手动查询也没找到template_id={a.template_id}的模板")
        
        # 批量获取所有模板的问题，避免循环查询
        template_ids = [template.id for _, _, template in distributions if template]
        questions_by_template = {}
        if template_ids:
            questions_query = db.query(AssessmentTemplateQuestion).filter(
                AssessmentTemplateQuestion.template_id.in_(template_ids)
            ).order_by(AssessmentTemplateQuestion.template_id, AssessmentTemplateQuestion.order)
            
            # 按模板ID分组问题
            for q in questions_query:
                if q.template_id not in questions_by_template:
                    questions_by_template[q.template_id] = []
                questions_by_template[q.template_id].append({
                    "id": q.id,
                    "question_id": q.question_id,
                    "question_text": q.question_text,
                    "question_type": q.question_type,
                    "options": q.options,
                    "order": q.order,
                    "is_required": q.is_required,
                    "jump_logic": q.jump_logic
                })
        
        result = []
        for d, a, t in distributions:
            # 优先使用Assessment的name，如果为空则使用Template的name，最后使用默认值
            title = a.name or (t.name if t else None) or "未命名量表"
            
            # 获取问题列表
            question_list = questions_by_template.get(t.id, []) if t else []
            
            assessment_data = {
                "id": a.id,
                "title": title,
                "name": title,  # 兼容性字段
                "due_date": d.due_date.isoformat() if d.due_date else None,
                "status": d.status,
                "distribution_id": d.id,
                "created_at": a.created_at.isoformat() if a.created_at else None,
                "type": "assessment"
            }
            
            # 如果有模板，添加模板信息和问题列表
            if t:
                assessment_data["template"] = {
                    "id": t.id,
                    "name": t.name or "未命名量表模板",
                    "assessment_type": t.assessment_type,
                    "version": t.version,
                    "description": t.description,
                    "instructions": t.instructions,
                    "questions": question_list
                }
            
            # 同时也在根级别添加questions字段，确保兼容性
            assessment_data["questions"] = question_list
            
            result.append(assessment_data)
        
        logger.info(f"最终返回 {len(result)} 个pending量表")
        for i, item in enumerate(result):
            questions_count = len(item.get("questions", []))
            logger.info(f"量表{i+1}: {item['title']}, 问题数量: {questions_count}")
        
        return {
            "status": "success", 
            "data": result, 
            "debug": {
                "total_distributions": len(all_distributions), 
                "pending_count": len(pending_distributions)
            }
        }
    except Exception as e:
        logger.error(f"获取待完成量表失败: {str(e)}")
        return {
            "status": "error",
            "message": f"获取待完成量表失败: {str(e)}",
            "data": []
        }

@router.get("/history-assessments", tags=["移动端-量表"], summary="获取历史量表报告")
def get_history_assessments(
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_active_user_custom)
):
    """移动端历史记录：查询assessment_results表中的量表报告"""
    # 获取评估量表报告 - 从assessment_results表查询
    if AssessmentResult is not None:
        assessment_results = db.query(AssessmentResult, Assessment).join(
            Assessment, AssessmentResult.assessment_id == Assessment.id
        ).filter(
            AssessmentResult.custom_id == current_user.custom_id,
            AssessmentResult.report_generated == True,
            AssessmentResult.report_content != None  # 只返回有报告的记录
        ).all()
    else:
        assessment_results = []
    
    # 获取问卷报告 - 从questionnaire_results表查询
    if QuestionnaireResult is not None:
        questionnaire_results = db.query(QuestionnaireResult, Questionnaire).join(
            Questionnaire, QuestionnaireResult.questionnaire_id == Questionnaire.id
        ).filter(
            QuestionnaireResult.custom_id == current_user.custom_id,
            QuestionnaireResult.report_generated == True,
            QuestionnaireResult.report_content != None  # 只返回有报告的记录
        ).all()
    else:
        questionnaire_results = []
    
    result = []
    
    # 添加评估量表报告
    for result_record, assessment in assessment_results:
        result.append({
            "id": result_record.id,
            "assessment_id": result_record.assessment_id,
            "title": assessment.title or "未命名量表",
            "report": result_record.report_content,
            "completed_at": result_record.calculated_at,
            "type": "assessment_report",
            "category": "评估量表",
            "total_score": result_record.total_score,
            "result_level": result_record.result_level
        })

    # 添加问卷报告
    for result_record, questionnaire in questionnaire_results:
        result.append({
            "id": result_record.id,
            "questionnaire_id": result_record.questionnaire_id,
            "title": questionnaire.title or "未命名问卷",
            "report": result_record.report_content,
            "completed_at": result_record.calculated_at,
            "type": "questionnaire_report",
            "category": "调查问卷",
            "total_score": result_record.total_score,
            "result_level": result_record.result_level
        })
    
    # 按完成时间倒序排列
    result.sort(key=lambda x: x["completed_at"], reverse=True)
    
    return {"status": "success", "data": result}

@router.get("/completed-assessments", tags=["移动端-量表"], summary="获取已完成量表（含原始与报告）")
def get_completed_assessments(
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_active_user_custom)
):
    responses = db.query(AssessmentResponse, Assessment).join(
        Assessment, AssessmentResponse.assessment_id == Assessment.id
    ).filter(
        AssessmentResponse.custom_id == current_user.custom_id,
        AssessmentDistribution.status == "completed"
    ).all()
    original = []
    reports = []
    for r, a in responses:
        item = {
            "id": r.id,
            "assessment_id": r.assessment_id,
            "title": a.name or "未命名量表",
            "completed_at": r.updated_at,
            "score": getattr(r, 'score', None),
            "type": "assessment_response"
        }
        if getattr(r, 'report', None):
            item["report"] = r.report
            item["type"] = "assessment_report"
            reports.append(item)
        else:
            original.append(item)
    return {"status": "success", "data": {"original": original, "reports": reports}}

def calculate_assessment_result(template: AssessmentTemplate, total_score: float, answers: List[Dict]) -> Dict[str, Any]:
    """
    计算评估量表结果和分析
    """
    try:
        result_category = "未分类"
        conclusion = "评估完成"
        recommendations = []
        
        # 如果模板有结果范围配置，使用配置进行分析
        if template.result_ranges:
            if isinstance(template.result_ranges, str):
                result_ranges = json.loads(template.result_ranges)
            else:
                result_ranges = template.result_ranges
            
            # 根据得分确定结果分类
            for range_config in result_ranges:
                min_score = range_config.get("min_score", 0)
                max_score = range_config.get("max_score", float('inf'))
                
                if min_score <= total_score <= max_score:
                    result_category = range_config.get("category", "未分类")
                    conclusion = range_config.get("description", "评估完成")
                    recommendations = range_config.get("recommendations", [])
                    break
        else:
            # 默认分析逻辑
            if template.max_score:
                percentage = (total_score / template.max_score) * 100
                if percentage >= 80:
                    result_category = "优秀"
                    conclusion = "评估结果优秀，各项指标表现良好"
                elif percentage >= 60:
                    result_category = "良好"
                    conclusion = "评估结果良好，大部分指标正常"
                elif percentage >= 40:
                    result_category = "一般"
                    conclusion = "评估结果一般，建议关注相关指标"
                else:
                    result_category = "需要关注"
                    conclusion = "评估结果显示需要特别关注，建议咨询专业人士"
        
        # 生成详细分析报告
        analysis_report = generate_analysis_report(template, total_score, answers, result_category)
        
        return {
            "result_category": result_category,
            "conclusion": conclusion,
            "recommendations": recommendations,
            "analysis_report": analysis_report,
            "score_percentage": (total_score / template.max_score * 100) if template.max_score else 0
        }
    except Exception as e:
        return {
            "result_category": "分析异常",
            "conclusion": f"结果分析过程中出现异常: {str(e)}",
            "recommendations": [],
            "analysis_report": "",
            "score_percentage": 0
        }

def generate_analysis_report(template: AssessmentTemplate, total_score: float, answers: List[Dict], result_category: str) -> str:
    """
    生成详细的分析报告
    """
    report_parts = []
    
    # 基本信息
    report_parts.append(f"## {template.name} 评估报告")
    report_parts.append(f"**评估时间**: {datetime.now().strftime('%Y年%m月%d日 %H:%M')}")
    report_parts.append(f"**总分**: {total_score}/{template.max_score}")
    report_parts.append(f"**结果分类**: {result_category}")
    
    # 得分分析
    if template.max_score:
        percentage = (total_score / template.max_score) * 100
        report_parts.append(f"**得分率**: {percentage:.1f}%")
    
    # 答题情况分析
    report_parts.append("\n### 答题情况分析")
    report_parts.append(f"- 总题数: {len(answers)}")
    report_parts.append(f"- 已完成: {len([a for a in answers if a.get('answer') is not None])}")
    
    # 分数分布
    if answers:
        scores = [a.get('score', 0) for a in answers]
        avg_score = sum(scores) / len(scores)
        report_parts.append(f"- 平均每题得分: {avg_score:.2f}")
    
    # 建议
    report_parts.append("\n### 建议")
    if result_category in ["需要关注", "一般"]:
        report_parts.append("- 建议定期进行评估，关注变化趋势")
        report_parts.append("- 如有疑问，建议咨询专业医生或心理咨询师")
    elif result_category in ["良好", "优秀"]:
        report_parts.append("- 继续保持良好状态")
        report_parts.append("- 建议定期进行评估以监测变化")
    
    return "\n".join(report_parts)

def create_health_record_from_assessment(
    db: Session, 
    user: User, 
    assessment: Assessment, 
    template: AssessmentTemplate, 
    result_analysis: Dict[str, Any]
) -> Optional[Dict[str, Any]]:
    """
    将评估量表结果保存到健康资料中
    """
    try:
        # 创建健康记录
        health_record = HealthRecord(
            custom_id=user.custom_id,
            record_type=RecordType.ASSESSMENT,
            title=f"{template.name} - 评估结果",
            description=f"评估时间: {assessment.completed_at.strftime('%Y年%m月%d日 %H:%M')}, 得分: {assessment.score}/{assessment.max_score}, 结果: {result_analysis['result_category']}",
            content=json.dumps({
                "assessment_id": assessment.id,
                "template_name": template.name,
                "assessment_type": template.assessment_type.value,
                "total_score": assessment.score,
                "max_score": assessment.max_score,
                "result_category": result_analysis["result_category"],
                "conclusion": result_analysis["conclusion"],
                "analysis_report": result_analysis["analysis_report"],
                "score_percentage": result_analysis["score_percentage"],
                "recommendations": result_analysis["recommendations"],
                "completed_at": assessment.completed_at.isoformat(),
                "answers_summary": {
                    "total_questions": len(assessment.answers.get("answers", [])),
                    "completed_questions": len([a for a in assessment.answers.get("answers", []) if a.get("answer") is not None])
                }
            }, ensure_ascii=False),
            is_important=result_analysis["result_category"] in ["需要关注", "异常"]
        )
        
        db.add(health_record)
        db.flush()  # 获取ID但不提交
        
        return {
            "id": health_record.id,
            "title": health_record.title,
            "record_type": health_record.recordtype.value,
            "created_at": health_record.created_at
        }
    except Exception as e:
        logger.error(f"创建健康记录失败: {str(e)}")
        return None

def calculate_questionnaire_result(template: QuestionnaireTemplate, answers: List[Dict]) -> Dict[str, Any]:
    """
    计算调查问卷结果和分析
    """
    try:
        result_category = "已完成"
        conclusion = "问卷调查完成"
        recommendations = []
        
        # 根据问卷类型进行不同的分析
        questionnaire_type = template.questionnaire_type
        total_questions = len(answers)
        completed_questions = len([a for a in answers if a.get("answer") is not None])
        completion_rate = (completed_questions / total_questions * 100) if total_questions > 0 else 0
        
        # 基于问卷类型的分析
        if questionnaire_type == "health":
            result_category = "健康状况调查"
            conclusion = f"健康状况调查已完成，完成率: {completion_rate:.1f}%"
            if completion_rate >= 90:
                recommendations.append("调查完整度良好，数据可靠")
            else:
                recommendations.append("建议完善未完成的问题以获得更准确的分析")
        elif questionnaire_type == "satisfaction":
            result_category = "满意度调查"
            conclusion = f"满意度调查已完成，完成率: {completion_rate:.1f}%"
            # 可以根据答案内容进行满意度分析
            recommendations.append("感谢您的反馈，我们将持续改进服务质量")
        elif questionnaire_type == "psqi":
            result_category = "睡眠质量评估"
            conclusion = f"匹兹堡睡眠质量指数评估已完成，完成率: {completion_rate:.1f}%"
            recommendations.append("建议根据评估结果关注睡眠质量")
        else:
            result_category = "问卷调查"
            conclusion = f"问卷调查已完成，完成率: {completion_rate:.1f}%"
        
        # 生成详细分析报告
        analysis_report = generate_questionnaire_analysis_report(template, answers, result_category, completion_rate)
        
        return {
            "result_category": result_category,
            "conclusion": conclusion,
            "recommendations": recommendations,
            "analysis_report": analysis_report,
            "completion_rate": completion_rate,
            "total_questions": total_questions,
            "completed_questions": completed_questions
        }
    except Exception as e:
        return {
            "result_category": "分析异常",
            "conclusion": f"问卷分析过程中出现异常: {str(e)}",
            "recommendations": [],
            "analysis_report": "",
            "completion_rate": 0,
            "total_questions": 0,
            "completed_questions": 0
        }

def generate_assessment_analysis_report(template: AssessmentTemplate, result_analysis: Dict[str, Any], answers: List[Dict]) -> str:
    """
    生成评估量表的详细分析报告
    """
    report_parts = []
    
    # 基本信息
    report_parts.append(f"## {template.name} 评估报告")
    report_parts.append(f"**评估时间**: {datetime.now().strftime('%Y年%m月%d日 %H:%M')}")
    report_parts.append(f"**评估类型**: {result_analysis['result_category']}")
    report_parts.append(f"**总分**: {result_analysis.get('total_score', 0)}/{template.max_score}")
    
    # 计算百分比
    percentage = (result_analysis.get('total_score', 0) / template.max_score * 100) if template.max_score > 0 else 0
    report_parts.append(f"**得分率**: {percentage:.1f}%")
    
    # 评估结果分析
    report_parts.append("\n### 评估结果分析")
    report_parts.append(f"- 评估结论: {result_analysis['conclusion']}")
    report_parts.append(f"- 结果分类: {result_analysis['result_category']}")
    
    # 答题情况分析
    report_parts.append("\n### 答题情况分析")
    report_parts.append(f"- 总题数: {len(answers)}")
    report_parts.append(f"- 已完成: {len([a for a in answers if a.get('answer') is not None])}")
    report_parts.append(f"- 未完成: {len([a for a in answers if a.get('answer') is None])}")
    
    # 分数分析
    if answers:
        total_possible_score = sum([a.get('score', 0) for a in answers])
        report_parts.append(f"- 实际得分: {result_analysis.get('total_score', 0)}")
        report_parts.append(f"- 最高可能分: {template.max_score}")
    
    # 结果等级评估
    report_parts.append("\n### 结果等级评估")
    if percentage >= 80:
        report_parts.append("- 评估等级: 优秀")
        report_parts.append("- 建议: 继续保持良好状态")
    elif percentage >= 60:
        report_parts.append("- 评估等级: 良好")
        report_parts.append("- 建议: 可适当改进相关方面")
    elif percentage >= 40:
        report_parts.append("- 评估等级: 一般")
        report_parts.append("- 建议: 需要关注并改善相关问题")
    else:
        report_parts.append("- 评估等级: 需要改进")
        report_parts.append("- 建议: 建议寻求专业指导")
    
    # 建议
    report_parts.append("\n### 专业建议")
    recommendations = result_analysis.get('recommendations', [])
    if recommendations:
        for rec in recommendations:
            report_parts.append(f"- {rec}")
    else:
        report_parts.append("- 请根据评估结果关注相关方面")
        report_parts.append("- 如有疑问，请咨询专业人员")
    
    return "\n".join(report_parts)

def generate_questionnaire_analysis_report(template: QuestionnaireTemplate, answers: List[Dict], result_category: str, completion_rate: float) -> str:
    """
    生成调查问卷的详细分析报告
    """
    report_parts = []
    
    # 基本信息
    report_parts.append(f"## {template.name} 调查报告")
    report_parts.append(f"**调查时间**: {datetime.now().strftime('%Y年%m月%d日 %H:%M')}")
    report_parts.append(f"**问卷类型**: {result_category}")
    report_parts.append(f"**完成率**: {completion_rate:.1f}%")
    
    # 答题情况分析
    report_parts.append("\n### 答题情况分析")
    report_parts.append(f"- 总题数: {len(answers)}")
    report_parts.append(f"- 已完成: {len([a for a in answers if a.get('answer') is not None])}")
    report_parts.append(f"- 未完成: {len([a for a in answers if a.get('answer') is None])}")
    
    # 答案类型分析
    if answers:
        text_answers = len([a for a in answers if isinstance(a.get('answer'), str) and a.get('answer')])
        choice_answers = len([a for a in answers if a.get('answer') and not isinstance(a.get('answer'), str)])
        report_parts.append(f"- 文本回答: {text_answers}")
        report_parts.append(f"- 选择回答: {choice_answers}")
    
    # 数据质量评估
    report_parts.append("\n### 数据质量评估")
    if completion_rate >= 90:
        report_parts.append("- 数据完整性: 优秀")
        report_parts.append("- 分析可靠性: 高")
    elif completion_rate >= 70:
        report_parts.append("- 数据完整性: 良好")
        report_parts.append("- 分析可靠性: 中等")
    else:
        report_parts.append("- 数据完整性: 需要改善")
        report_parts.append("- 分析可靠性: 较低")
    
    # 建议
    report_parts.append("\n### 建议")
    if completion_rate < 100:
        report_parts.append("- 建议完善未完成的问题以获得更全面的分析")
    report_parts.append("- 感谢您的参与，您的反馈对我们很重要")
    report_parts.append("- 如有疑问，请联系相关工作人员")
    
    return "\n".join(report_parts)

def create_health_record_from_questionnaire(
    db: Session, 
    user: User, 
    questionnaire: Questionnaire, 
    template: QuestionnaireTemplate, 
    result_analysis: Dict[str, Any],
    distribution: QuestionnaireDistribution
) -> Optional[Dict[str, Any]]:
    """
    将调查问卷结果保存到健康资料中
    """
    try:
        # 创建健康记录
        health_record = HealthRecord(
            custom_id=user.custom_id,
            record_type=RecordType.QUESTIONNAIRE,  # 使用问卷类型
            title=f"{template.name} - 调查结果",
            description=f"调查时间: {distribution.completed_at.strftime('%Y年%m月%d日 %H:%M')}, 完成率: {result_analysis['completion_rate']:.1f}%, 类型: {result_analysis['result_category']}",
            content=json.dumps({
                "questionnaire_id": questionnaire.id,
                "template_name": template.name,
                "questionnaire_type": template.questionnaire_type,
                "result_category": result_analysis["result_category"],
                "conclusion": result_analysis["conclusion"],
                "analysis_report": result_analysis["analysis_report"],
                "completion_rate": result_analysis["completion_rate"],
                "total_questions": result_analysis["total_questions"],
                "completed_questions": result_analysis["completed_questions"],
                "recommendations": result_analysis["recommendations"],
                "completed_at": distribution.completed_at.isoformat(),
                "answers_summary": {
                    "total_questions": result_analysis["total_questions"],
                    "completed_questions": result_analysis["completed_questions"],
                    "completion_rate": result_analysis["completion_rate"]
                }
            }, ensure_ascii=False),
            is_important=result_analysis["completion_rate"] < 70  # 完成率低于70%标记为重要
        )
        
        db.add(health_record)
        db.flush()  # 获取ID但不提交
        
        return {
            "id": health_record.id,
            "title": health_record.title,
            "record_type": health_record.recordtype.value,
            "created_at": health_record.created_at
        }
    except Exception as e:
        logger.error(f"创建健康记录失败: {str(e)}")
        return None

@router.get("/assessment-reports/{report_id}", response_model=Dict[str, Any])
def get_assessment_report_detail(
    report_id: int = Path(..., description="报告ID"),
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_active_user_custom)
):
    """获取评估量表报告详情"""
    try:
        # 查询评估量表报告
        assessment_response = db.query(AssessmentResponse, Assessment).join(
            Assessment, AssessmentResponse.assessment_id == Assessment.id
        ).filter(
            AssessmentResponse.id == report_id,
            AssessmentResponse.custom_id == current_user.custom_id,
            AssessmentDistribution.status == "completed",
            AssessmentResponse.report != None
        ).first()
        
        if not assessment_response:
            raise HTTPException(status_code=404, detail="报告不存在或无权访问")
        
        response, assessment = assessment_response
        
        # 构建报告详情
        report_data = {
            "id": response.id,
            "assessment_id": response.assessment_id,
            "title": assessment.title,
            "report": response.report,
            "score": getattr(response, 'score', None),
            "total_score": getattr(response, 'total_score', None),
            "completed_at": response.updated_at,
            "created_at": response.created_at,
            "type": "assessment_report",
            "category": "评估量表",
            "custom_id": response.custom_id
        }
        
        # 如果有报告内容，尝试解析JSON
        if response.report:
            try:
                import json
                if isinstance(response.report, str):
                    report_content = json.loads(response.report)
                else:
                    report_content = response.report
                
                # 添加分析信息
                if isinstance(report_content, dict):
                    report_data.update({
                        "analysis": report_content.get("analysis", {}),
                        "conclusion": report_content.get("conclusion", ""),
                        "recommendations": report_content.get("recommendations", []),
                        "result": report_content.get("result", "")
                    })
            except (json.JSONDecodeError, TypeError):
                # 如果解析失败，保持原始报告内容
                pass
        
        # 获取问题和答案
        try:
            if hasattr(response, 'answers') and response.answers:
                if isinstance(response.answers, str):
                    import json
                    answers = json.loads(response.answers)
                else:
                    answers = response.answers
                report_data["answers"] = answers
        except:
            pass
        
        # 获取模板问题
        try:
            template = db.query(AssessmentTemplate).filter(
                AssessmentTemplate.id == assessment.template_id
            ).first()
            
            if template:
                questions = db.query(AssessmentTemplateQuestion).filter(
                    AssessmentTemplateQuestion.template_id == template.id
                ).order_by(AssessmentTemplateQuestion.order).all()
                
                report_data["questions"] = [{
                    "id": q.id,
                    "text": q.question_text,
                    "type": q.question_type,
                    "options": q.options
                } for q in questions]
        except:
            pass
        
        return {"status": "success", "data": report_data}
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"获取评估量表报告详情失败: {str(e)}")
        raise HTTPException(status_code=500, detail="获取报告详情失败")

@router.get("/questionnaire-reports/{report_id}", response_model=Dict[str, Any])
def get_questionnaire_report_detail(
    report_id: int = Path(..., description="报告ID"),
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_active_user_custom)
):
    """获取问卷报告详情"""
    try:
        # 查询问卷报告
        questionnaire_response = db.query(QuestionnaireResponse, Questionnaire).join(
            Questionnaire, QuestionnaireResponse.questionnaire_id == Questionnaire.id
        ).filter(
            QuestionnaireResponse.id == report_id,
            QuestionnaireResponse.custom_id == current_user.custom_id,
            QuestionnaireResponse.status == "completed",
            QuestionnaireResponse.report != None
        ).first()
        
        if not questionnaire_response:
            raise HTTPException(status_code=404, detail="报告不存在或无权访问")
        
        response, questionnaire = questionnaire_response
        
        # 构建报告详情
        report_data = {
            "id": response.id,
            "questionnaire_id": response.questionnaire_id,
            "title": questionnaire.title,
            "report": response.report,
            "score": getattr(response, 'score', None),
            "total_score": getattr(response, 'total_score', None),
            "completed_at": response.updated_at,
            "created_at": response.created_at,
            "type": "questionnaire_report",
            "category": "调查问卷",
            "custom_id": response.custom_id
        }
        
        # 如果有报告内容，尝试解析JSON
        if response.report:
            try:
                import json
                if isinstance(response.report, str):
                    report_content = json.loads(response.report)
                else:
                    report_content = response.report
                
                # 添加分析信息
                if isinstance(report_content, dict):
                    report_data.update({
                        "analysis": report_content.get("analysis", {}),
                        "conclusion": report_content.get("conclusion", ""),
                        "recommendations": report_content.get("recommendations", []),
                        "result": report_content.get("result", "")
                    })
            except (json.JSONDecodeError, TypeError):
                # 如果解析失败，保持原始报告内容
                pass
        
        # 获取问题和答案
        try:
            if hasattr(response, 'answers') and response.answers:
                if isinstance(response.answers, str):
                    import json
                    answers = json.loads(response.answers)
                else:
                    answers = response.answers
                report_data["answers"] = answers
        except:
            pass
        
        # 获取模板问题
        try:
            template = db.query(QuestionnaireTemplate).filter(
                QuestionnaireTemplate.id == questionnaire.template_id
            ).first()
            
            if template:
                questions = db.query(QuestionnaireTemplateQuestion).filter(
                    QuestionnaireTemplateQuestion.template_id == template.id
                ).order_by(QuestionnaireTemplateQuestion.order).all()
                
                report_data["questions"] = [{
                    "id": q.id,
                    "text": q.question_text,
                    "type": q.question_type,
                    "options": q.options
                } for q in questions]
        except:
            pass
        
        return {"status": "success", "data": report_data}
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"获取问卷报告详情失败: {str(e)}")
        raise HTTPException(status_code=500, detail="获取报告详情失败")