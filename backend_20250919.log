2025-09-19 12:56:22 [INFO] [logging_config] [configure_logging:172] - 日志系统配置完成 - 级别: INFO, 文件: backend_20250919.log
2025-09-19 12:56:22 [INFO] [logging_config] [configure_logging:173] - SQLAlchemy日志级别: WARNING
2025-09-19 12:56:22 [INFO] [backend.app.core.db_connection] [_create_engine:192] - 数据库引擎创建成功: sqlite:///c:\Users\<USER>\Desktop\health-Trea\YUN\backend\app.db
2025-09-19 12:56:22 [INFO] [query_cache] [__init__:58] - 查询缓存初始化完成 - 启用: True, 使用Redis: False
2025-09-19 12:56:22 [INFO] [app.db.base_session] [<module>:45] - 数据库引擎创建成功: sqlite:///c:\Users\<USER>\Desktop\health-Trea\YUN\backend\app.db
2025-09-19 16:34:41 [INFO] [logging_config] [configure_logging:172] - 日志系统配置完成 - 级别: INFO, 文件: backend_20250919.log
2025-09-19 16:34:41 [INFO] [logging_config] [configure_logging:173] - SQLAlchemy日志级别: WARNING
2025-09-19 16:34:41 [INFO] [app.db.base_session] [<module>:45] - 数据库引擎创建成功: sqlite:///c:\Users\<USER>\Desktop\health-Trea\YUN\backend\app.db
2025-09-19 16:34:42 [INFO] [app.core.db_connection] [_create_engine:192] - 数据库引擎创建成功: sqlite:///c:\Users\<USER>\Desktop\health-Trea\YUN\backend\app.db
2025-09-19 16:34:42 [INFO] [auth_service] [__init__:49] - 统一认证服务初始化完成
2025-09-19 16:34:42 [INFO] [backend.app.core.db_connection] [_create_engine:192] - 数据库引擎创建成功: sqlite:///c:\Users\<USER>\Desktop\health-Trea\YUN\backend\app.db
2025-09-19 16:34:42 [INFO] [query_cache] [__init__:58] - 查询缓存初始化完成 - 启用: True, 使用Redis: False
2025-09-19 16:34:42 [INFO] [BackendMockDataManager] [__init__:35] - 后端模拟数据模式已启用
2025-09-19 16:34:46 [INFO] [DataExportService] [_log:378] - 已初始化 19 个表模型
2025-09-19 16:34:46 [INFO] [DataExportService] [_log:378] - 已初始化 19 个表模型
2025-09-19 16:34:46 [INFO] [DataExportService] [_log:378] - 已初始化 19 个表模型
2025-09-19 16:34:47 [INFO] [DataExportService] [_log:378] - 已初始化 19 个表模型
2025-09-19 16:34:47 [INFO] [root] [<module>:28] - 成功导入psutil模块，路径: C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\psutil\__init__.py
2025-09-19 16:34:47 [INFO] [fallback_manager] [register_dependency:51] - 依赖 psutil (psutil) 可用
2025-09-19 16:34:47 [INFO] [health_monitor] [__init__:115] - 健康监控器初始化完成
2025-09-19 16:34:47 [INFO] [app.core.system_monitor] [_load_history:254] - 已加载 16 个历史数据点
2025-09-19 16:34:47 [INFO] [app.core.alert_detector] [_load_rules:464] - 已加载 6 个告警规则
2025-09-19 16:34:47 [INFO] [app.core.alert_detector] [_load_alerts:484] - 已加载 0 个当前告警
2025-09-19 16:34:47 [INFO] [app.core.alert_detector] [_load_alerts:491] - 已加载 3 个历史告警
2025-09-19 16:34:47 [INFO] [app.core.alert_detector] [_load_notification_channels:502] - 已加载 1 个通知渠道
2025-09-19 16:34:47 [INFO] [query_cache] [__init__:58] - 查询缓存初始化完成 - 启用: True, 使用Redis: False
2025-09-19 16:34:47 [INFO] [alert_manager] [_init_alert_rules:315] - 已初始化默认告警规则
2025-09-19 16:34:47 [INFO] [alert_manager] [_init_notification_channels:333] - 已初始化默认通知渠道
2025-09-19 16:34:47 [INFO] [alert_manager] [__init__:258] - 告警管理器初始化完成
2025-09-19 16:34:48 [INFO] [db_service] [_create_engine:92] - 数据库引擎和会话工厂创建成功
2025-09-19 16:34:48 [INFO] [db_service] [__init__:56] - 数据库服务初始化完成
2025-09-19 16:34:48 [INFO] [notification_service] [__init__:55] - 通知服务初始化完成
2025-09-19 16:34:48 [INFO] [main] [<module>:56] - 错误处理模块导入成功
2025-09-19 16:34:48 [INFO] [main] [<module>:65] - 监控模块导入成功
2025-09-19 16:34:48 [INFO] [main] [<module>:75] - 不再使用迁移脚本，使用标准化的数据库模型
2025-09-19 16:34:48 [INFO] [error_handling] [setup_error_handling:271] - 错误处理已设置
2025-09-19 16:34:48 [INFO] [main] [<module>:88] - 错误处理系统已设置
