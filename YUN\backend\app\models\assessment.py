"""
评估量表和调查问卷模型
"""
import enum
from sqlalchemy import <PERSON><PERSON><PERSON>, Column, Enum, ForeignKey, Integer, String, Text, DateTime, Float, JSON
from sqlalchemy.orm import relationship
from sqlalchemy.sql import func

from app.db.base_session import Base
from app.models.enums import AssessmentType, QuestionnaireType  # 添加 QuestionnaireType 导入

# 枚举类型已移至 app.models.enums 模块

class Assessment(Base):
    """评估量表模型"""
    __tablename__ = "assessments"
    __table_args__ = {"extend_existing": True}

    id = Column(Integer, primary_key=True, index=True)
    custom_id = Column(String(20), ForeignKey("users.custom_id"), nullable=False)
    template_id = Column(Integer, ForeignKey("assessment_templates.id"), nullable=True)  # 关联评估模板
    assessment_type = Column(Enum(AssessmentType), nullable=False)
    title = Column(String, nullable=False)  # 量表名称
    version = Column(String, nullable=True)  # 版本
    round_number = Column(Integer, default=1, nullable=False)  # 评估轮次，支持多次评估
    sequence_number = Column(Integer, default=1, nullable=False)  # 同一轮次内的序号
    unique_identifier = Column(String, nullable=True, index=True)  # 唯一标识符，格式：template_id_custom_id_round_sequence
    completed_at = Column(DateTime(timezone=True), nullable=True)
    assessor = Column(String, nullable=True)  # 评估人
    score = Column(Float, nullable=True)  # 总分
    max_score = Column(Float, nullable=True)  # 满分
    result = Column(String, nullable=True)  # 结果分类
    conclusion = Column(Text, nullable=True)  # 结论
    notes = Column(Text, nullable=True)
    status = Column(String, default='draft', nullable=False)  # 新增：状态字段
    created_at = Column(DateTime(timezone=True), server_default=func.now())
    updated_at = Column(DateTime(timezone=True), server_default=func.now(), onupdate=func.now())
    last_reminded_at = Column(DateTime(timezone=True), nullable=True)  # 最后催办时间

    # 关系 - 使用字符串引用避免循环导入
    user = relationship("app.models.user.User", back_populates="assessments")
    template = relationship("AssessmentTemplate", back_populates="assessments")
    items = relationship("AssessmentItem", back_populates="assessment", cascade="all, delete-orphan")
    # 关联分发记录 - 由app.models.relationships模块管理
    # distributions = relationship("AssessmentDistribution", back_populates="assessment", cascade="all, delete-orphan")

    def __repr__(self):
        return f"<Assessment {self.id} - {self.name}>"

class AssessmentItem(Base):
    """评估量表项目模型"""
    __tablename__ = "assessment_items"
    __table_args__ = {"extend_existing": True}

    id = Column(Integer, primary_key=True, index=True)
    assessment_id = Column(Integer, ForeignKey("assessments.id"), nullable=False)
    question_id = Column(String, nullable=False)  # 问题ID
    question_text = Column(Text, nullable=False)  # 问题文本
    question_type = Column(String, nullable=False)  # 问题类型：单选、多选、填空等
    options = Column(JSON, nullable=True)  # 选项，JSON格式
    order_num = Column(Integer, nullable=True)  # 问题顺序
    is_required = Column(Boolean, default=True)  # 是否必填
    jump_logic = Column(JSON, nullable=True)  # 跳转逻辑，JSON格式
    answer = Column(Text, nullable=True)  # 回答
    score = Column(Float, nullable=True)  # 得分
    notes = Column(Text, nullable=True)
    created_at = Column(DateTime(timezone=True), server_default=func.now())

    # 关系
    assessment = relationship("Assessment", back_populates="items")

    def __repr__(self):
        return f"<AssessmentItem {self.id} - {self.question_id}>"

class AssessmentResponse(Base):
    """评估量表填写记录"""
    __tablename__ = "assessment_responses"
    __table_args__ = {"extend_existing": True}

    id = Column(Integer, primary_key=True, index=True)
    assessment_id = Column(Integer, ForeignKey("assessments.id"), nullable=False)
    custom_id = Column(String(20), ForeignKey("users.custom_id"), nullable=False)
    answers = Column(JSON, nullable=False)  # 用户填写的答案，JSON格式
    score = Column(Float, nullable=True)  # 总分
    dimension_scores = Column(JSON, nullable=True)  # 维度分数，JSON格式
    result = Column(String, nullable=True)  # 评估结果
    notes = Column(Text, nullable=True)
    created_at = Column(DateTime(timezone=True), server_default=func.now())
    updated_at = Column(DateTime(timezone=True), server_default=func.now(), onupdate=func.now())

    # 关系
    assessment = relationship("Assessment", backref="responses")
    # user = relationship("app.models.user.User")  # 可选

    def __repr__(self):
        return f"<AssessmentResponse {self.id} - assessment {self.assessment_id}>"

class AssessmentTemplate(Base):
    """评估量表模板"""
    __tablename__ = "assessment_templates"
    __table_args__ = {"extend_existing": True}

    id = Column(Integer, primary_key=True, index=True)
    template_key = Column(String, nullable=True, unique=True, index=True)  # 模板键值，用于标识标准模板
    assessment_type = Column(Enum(AssessmentType), nullable=False)
    sub_type = Column(String, nullable=True)  # 添加子类型字段，例如：depression, anxiety等
    title = Column(String, nullable=False)  # 量表名称
    version = Column(String, nullable=True)  # 版本
    description = Column(Text, nullable=True)  # 描述
    instructions = Column(Text, nullable=True)  # 使用说明
    scoring_method = Column(Text, nullable=True)  # 评分方法
    max_score = Column(Float, nullable=True)  # 满分
    result_ranges = Column(JSON, nullable=True)  # 结果范围，JSON格式
    dimensions = Column(JSON, nullable=True)  # 维度定义，JSON格式
    is_active = Column(Boolean, default=True)
    status = Column(String, default="draft", nullable=True)  # 状态：draft-草稿，pending-待审核，approved-已批准，rejected-已拒绝
    created_by = Column(Integer, ForeignKey("users.id"), nullable=True)  # 修改为引用 users.id
    created_at = Column(DateTime(timezone=True), server_default=func.now())
    
    # 添加关联创建者的关系
    creator = relationship("User", back_populates="created_assessment_templates", foreign_keys=[created_by])
    questions = relationship("AssessmentTemplateQuestion", back_populates="template", cascade="all, delete-orphan")
    assessments = relationship("Assessment", back_populates="template")

    def __repr__(self):
        return f"<AssessmentTemplate {self.id} - {self.name}>"

class AssessmentTemplateQuestion(Base):
    """评估量表模板问题"""
    __tablename__ = "assessment_template_questions"
    __table_args__ = {"extend_existing": True}

    id = Column(Integer, primary_key=True, index=True)
    template_id = Column(Integer, ForeignKey("assessment_templates.id"), nullable=False)
    question_id = Column(String, nullable=False)  # 问题ID
    question_text = Column(Text, nullable=False)  # 问题文本
    question_type = Column(String, nullable=False)  # 问题类型：单选、多选、填空等
    options = Column(JSON, nullable=True)  # 选项，JSON格式
    scoring = Column(JSON, nullable=True)  # 评分规则，JSON格式
    order = Column(Integer, nullable=False)  # 问题顺序
    is_required = Column(Boolean, default=True)
    dimension_key = Column(String, nullable=True)  # 维度归属
    jump_logic = Column(JSON, nullable=True)  # 跳转逻辑
    created_at = Column(DateTime(timezone=True), server_default=func.now())
    updated_at = Column(DateTime(timezone=True), server_default=func.now(), onupdate=func.now())

    # 关系
    template = relationship("AssessmentTemplate", back_populates="questions")

    def __repr__(self):
        return f"<AssessmentTemplateQuestion {self.id} - {self.question_id}>"

# QuestionnaireTemplate 和 QuestionnaireTemplateQuestion 类已在 app.models.questionnaire 中定义
